package meter

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateE3dc(base *E3dc, battery func() (float64, error), batteryCapacity func() float64, batteryController func(api.BatteryMode) error, maxACPowerGetter func() float64) api.Meter {
	switch {
	case battery == nil && maxACPowerGetter == nil:
		return base

	case battery != nil && batteryCapacity == nil && batteryController == nil && maxACPowerGetter == nil:
		return &struct {
			*E3dc
			api.Battery
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && maxACPowerGetter == nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryCapacity
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateE3dcBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && maxACPowerGetter == nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryController
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateE3dcBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && maxACPowerGetter == nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryCapacity
			api.BatteryController
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateE3dcBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateE3dcBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery == nil && maxACPowerGetter != nil:
		return &struct {
			*E3dc
			api.MaxACPowerGetter
		}{
			E3dc: base,
			MaxACPowerGetter: &decorateE3dcMaxACPowerGetterImpl{
				maxACPowerGetter: maxACPowerGetter,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController == nil && maxACPowerGetter != nil:
		return &struct {
			*E3dc
			api.Battery
			api.MaxACPowerGetter
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			MaxACPowerGetter: &decorateE3dcMaxACPowerGetterImpl{
				maxACPowerGetter: maxACPowerGetter,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && maxACPowerGetter != nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryCapacity
			api.MaxACPowerGetter
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateE3dcBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			MaxACPowerGetter: &decorateE3dcMaxACPowerGetterImpl{
				maxACPowerGetter: maxACPowerGetter,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && maxACPowerGetter != nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryController
			api.MaxACPowerGetter
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateE3dcBatteryControllerImpl{
				batteryController: batteryController,
			},
			MaxACPowerGetter: &decorateE3dcMaxACPowerGetterImpl{
				maxACPowerGetter: maxACPowerGetter,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && maxACPowerGetter != nil:
		return &struct {
			*E3dc
			api.Battery
			api.BatteryCapacity
			api.BatteryController
			api.MaxACPowerGetter
		}{
			E3dc: base,
			Battery: &decorateE3dcBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateE3dcBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateE3dcBatteryControllerImpl{
				batteryController: batteryController,
			},
			MaxACPowerGetter: &decorateE3dcMaxACPowerGetterImpl{
				maxACPowerGetter: maxACPowerGetter,
			},
		}
	}

	return nil
}

type decorateE3dcBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateE3dcBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateE3dcBatteryCapacityImpl struct {
	batteryCapacity func() float64
}

func (impl *decorateE3dcBatteryCapacityImpl) Capacity() float64 {
	return impl.batteryCapacity()
}

type decorateE3dcBatteryControllerImpl struct {
	batteryController func(api.BatteryMode) error
}

func (impl *decorateE3dcBatteryControllerImpl) SetBatteryMode(p0 api.BatteryMode) error {
	return impl.batteryController(p0)
}

type decorateE3dcMaxACPowerGetterImpl struct {
	maxACPowerGetter func() float64
}

func (impl *decorateE3dcMaxACPowerGetterImpl) MaxACPower() float64 {
	return impl.maxACPowerGetter()
}
