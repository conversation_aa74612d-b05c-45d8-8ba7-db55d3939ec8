package meter

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateRCT(base *RCT, meterEnergy func() (float64, error), battery func() (float64, error), batteryController func(api.BatteryMode) error, batteryCapacity func() float64) api.Meter {
	switch {
	case battery == nil && meterEnergy == nil:
		return base

	case battery == nil && meterEnergy != nil:
		return &struct {
			*RCT
			api.MeterEnergy
		}{
			RCT: base,
			MeterEnergy: &decorateRCTMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController == nil && meterEnergy == nil:
		return &struct {
			*RCT
			api.Battery
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController == nil && meterEnergy != nil:
		return &struct {
			*RCT
			api.Battery
			api.MeterEnergy
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			MeterEnergy: &decorateRCTMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && meterEnergy == nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryController
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateRCTBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && meterEnergy != nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryController
			api.MeterEnergy
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateRCTBatteryControllerImpl{
				batteryController: batteryController,
			},
			MeterEnergy: &decorateRCTMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && meterEnergy == nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryCapacity
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateRCTBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && meterEnergy != nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryCapacity
			api.MeterEnergy
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateRCTBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			MeterEnergy: &decorateRCTMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && meterEnergy == nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryCapacity
			api.BatteryController
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateRCTBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateRCTBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && meterEnergy != nil:
		return &struct {
			*RCT
			api.Battery
			api.BatteryCapacity
			api.BatteryController
			api.MeterEnergy
		}{
			RCT: base,
			Battery: &decorateRCTBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateRCTBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateRCTBatteryControllerImpl{
				batteryController: batteryController,
			},
			MeterEnergy: &decorateRCTMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}
	}

	return nil
}

type decorateRCTBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateRCTBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateRCTBatteryCapacityImpl struct {
	batteryCapacity func() float64
}

func (impl *decorateRCTBatteryCapacityImpl) Capacity() float64 {
	return impl.batteryCapacity()
}

type decorateRCTBatteryControllerImpl struct {
	batteryController func(api.BatteryMode) error
}

func (impl *decorateRCTBatteryControllerImpl) SetBatteryMode(p0 api.BatteryMode) error {
	return impl.batteryController(p0)
}

type decorateRCTMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateRCTMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}
