package meter

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateLgEss(base *LgEss, meterEnergy func() (float64, error), battery func() (float64, error), batteryController func(api.BatteryMode) error, batteryCapacity func() float64) api.Meter {
	switch {
	case battery == nil && meterEnergy == nil:
		return base

	case battery == nil && meterEnergy != nil:
		return &struct {
			*LgEss
			api.MeterEnergy
		}{
			LgEss: base,
			MeterEnergy: &decorateLgEssMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController == nil && meterEnergy == nil:
		return &struct {
			*LgEss
			api.Battery
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController == nil && meterEnergy != nil:
		return &struct {
			*LgEss
			api.Battery
			api.MeterEnergy
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			MeterEnergy: &decorateLgEssMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && meterEnergy == nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryController
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateLgEssBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery != nil && batteryCapacity == nil && batteryController != nil && meterEnergy != nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryController
			api.MeterEnergy
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryController: &decorateLgEssBatteryControllerImpl{
				batteryController: batteryController,
			},
			MeterEnergy: &decorateLgEssMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && meterEnergy == nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryCapacity
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateLgEssBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController == nil && meterEnergy != nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryCapacity
			api.MeterEnergy
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateLgEssBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			MeterEnergy: &decorateLgEssMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && meterEnergy == nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryCapacity
			api.BatteryController
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateLgEssBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateLgEssBatteryControllerImpl{
				batteryController: batteryController,
			},
		}

	case battery != nil && batteryCapacity != nil && batteryController != nil && meterEnergy != nil:
		return &struct {
			*LgEss
			api.Battery
			api.BatteryCapacity
			api.BatteryController
			api.MeterEnergy
		}{
			LgEss: base,
			Battery: &decorateLgEssBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateLgEssBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
			BatteryController: &decorateLgEssBatteryControllerImpl{
				batteryController: batteryController,
			},
			MeterEnergy: &decorateLgEssMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}
	}

	return nil
}

type decorateLgEssBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateLgEssBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateLgEssBatteryCapacityImpl struct {
	batteryCapacity func() float64
}

func (impl *decorateLgEssBatteryCapacityImpl) Capacity() float64 {
	return impl.batteryCapacity()
}

type decorateLgEssBatteryControllerImpl struct {
	batteryController func(api.BatteryMode) error
}

func (impl *decorateLgEssBatteryControllerImpl) SetBatteryMode(p0 api.BatteryMode) error {
	return impl.batteryController(p0)
}

type decorateLgEssMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateLgEssMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}
