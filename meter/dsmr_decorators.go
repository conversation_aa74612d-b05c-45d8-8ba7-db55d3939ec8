package meter

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateDsmr(base api.Meter, meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error)) api.Meter {
	switch {
	case meterEnergy == nil && phaseCurrents == nil:
		return base

	case meterEnergy != nil && phaseCurrents == nil:
		return &struct {
			api.Meter
			api.MeterEnergy
		}{
			Meter: base,
			MeterEnergy: &decorateDsmrMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case meterEnergy == nil && phaseCurrents != nil:
		return &struct {
			api.Meter
			api.PhaseCurrents
		}{
			Meter: base,
			PhaseCurrents: &decorateDsmrPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case meterEnergy != nil && phaseCurrents != nil:
		return &struct {
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Meter: base,
			MeterEnergy: &decorateDsmrMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateDsmrPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}
	}

	return nil
}

type decorateDsmrMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateDsmrMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateDsmrPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateDsmrPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}
