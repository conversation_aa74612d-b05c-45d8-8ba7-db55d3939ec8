package meter

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateGoodWeWifi(base *goodWeWiFi, battery func() (float64, error), batteryCapacity func() float64) api.Meter {
	switch {
	case battery == nil:
		return base

	case battery != nil && batteryCapacity == nil:
		return &struct {
			*goodWeWiFi
			api.Battery
		}{
			goodWeWiFi: base,
			Battery: &decorateGoodWeWifiBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && batteryCapacity != nil:
		return &struct {
			*goodWeWiFi
			api.Battery
			api.BatteryCapacity
		}{
			goodWeWiFi: base,
			Battery: &decorateGoodWeWifiBatteryImpl{
				battery: battery,
			},
			BatteryCapacity: &decorateGoodWeWifiBatteryCapacityImpl{
				batteryCapacity: batteryCapacity,
			},
		}
	}

	return nil
}

type decorateGoodWeWifiBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateGoodWeWifiBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateGoodWeWifiBatteryCapacityImpl struct {
	batteryCapacity func() float64
}

func (impl *decorateGoodWeWifiBatteryCapacityImpl) Capacity() float64 {
	return impl.batteryCapacity()
}
