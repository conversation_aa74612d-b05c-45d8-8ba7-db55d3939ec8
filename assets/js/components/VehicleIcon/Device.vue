<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4 21q-1.25 0-2.125-.875T1 18V6q0-1.25.875-2.125T4 3h8q1.25 0 2.125.875T15 6v6.3h1q.825 0 1.413.588T18 14.3v3.2q0 .2.15.35t.35.15t.35-.15t.15-.35V11q-.825 0-1.412-.587T17 9V6h1V4h1.5v2h1V4H22v2h1v3q0 .825-.587 1.413T21 11v6.4q0 1.05-.725 1.775T18.5 19.9t-1.775-.725T16 17.4v-3.2h-1V17q0 1.65-1.175 2.825T11 21zm0-2h7q.825 0 1.413-.587T13 17t-.587-1.412T11 15H6.5q-.2 0-.35.15T6 15.5t.15.35t.35.15H11q.425 0 .713.288T12 17t-.288.713T11 18H6.5q-1.05 0-1.775-.725T4 15.5t.725-1.775T6.5 13H11q.525 0 1.038.15t.962.45V6q0-.425-.288-.712T12 5H4q-.425 0-.712.288T3 6v12q0 .425.288.713T4 19m0 0h7h-8z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
	name: "Device",
});
</script>
