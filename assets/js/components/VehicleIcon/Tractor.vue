<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4 9q-.425 0-.712-.288Q3 8.425 3 8t.288-.713Q3.575 7 4 7h3q.825 0 1.412.587Q9 8.175 9 9Zm2 9q1.25 0 2.125-.875T9 15q0-1.25-.875-2.125T6 12q-1.25 0-2.125.875T3 15q0 1.25.875 2.125T6 18Zm13.5 0q.625 0 1.062-.438Q21 17.125 21 16.5t-.438-1.062Q20.125 15 19.5 15t-1.062.438Q18 15.875 18 16.5t.438 1.062Q18.875 18 19.5 18ZM6 16.5q-.625 0-1.062-.438Q4.5 15.625 4.5 15t.438-1.062Q5.375 13.5 6 13.5t1.062.438Q7.5 14.375 7.5 15t-.438 1.062Q6.625 16.5 6 16.5Zm14-3.475q.65.125 1.075.337q.425.213.925.688V8q0-.825-.587-1.412Q20.825 6 20 6h-6.3l-1.05-1.1l1.05-1.05q.15-.15.15-.35q0-.2-.15-.35q-.15-.15-.35-.15q-.2 0-.35.15l-2.825 2.825q-.15.15-.15.363q0 .212.175.362q.15.15.35.15q.2 0 .35-.15l1.05-1.05L13 6.7V9q0 .825-.587 1.412Q11.825 11 11 11H8.975q.575.425.925.875q.35.45.7 1.125h.4q1.65 0 2.825-1.175Q15 10.65 15 9V8h5ZM16.025 16q.15-.675.363-1.088q.212-.412.662-.912H10.9q.1.575.1 1q0 .425-.1 1Zm3.475 4q-1.45 0-2.475-1.025Q16 17.95 16 16.5q0-1.45 1.025-2.475Q18.05 13 19.5 13q1.45 0 2.475 1.025Q23 15.05 23 16.5q0 1.45-1.025 2.475Q20.95 20 19.5 20ZM6 20q-2.075 0-3.537-1.462Q1 17.075 1 15q0-2.075 1.463-3.538Q3.925 10 6 10t3.538 1.462Q11 12.925 11 15q0 2.075-1.462 3.538Q8.075 20 6 20Zm9.825-9Z"
		></path>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Tractor",
});
</script>
