<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M9 20.95v-1.5q-2.65-.925-4.325-3.237T3 10.95q0-1.875.713-3.512t1.924-2.85t2.85-1.925t3.488-.713t3.5.713t2.875 1.925t1.938 2.85T21 10.95q0 2.95-1.687 5.238T15 19.425v1.525q0 .425-.288.713T14 21.95t-.712-.287T13 20.95V19.9q-.25.05-.5.05h-.525q-.25 0-.488-.012T11 19.9v1.05q0 .425-.288.713T10 21.95t-.712-.287T9 20.95M12 18q2.9 0 4.95-2.05T19 11t-2.05-4.95T12 4T7.05 6.05T5 11t2.05 4.95T12 18M9 9h6q.425 0 .713-.288T16 8t-.288-.712T15 7H9q-.425 0-.712.288T8 8t.288.713T9 9m2 5.25l-.5.5q-.325.325-.325.75t.325.75t.75.325t.75-.325l1.55-1.55q.3-.3.3-.7t-.3-.7l-.55-.55l.5-.5q.325-.325.325-.75t-.325-.75t-.75-.325t-.75.325l-1.55 1.55q-.3.3-.3.7t.3.7zM12 11"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Meter",
});
</script>
