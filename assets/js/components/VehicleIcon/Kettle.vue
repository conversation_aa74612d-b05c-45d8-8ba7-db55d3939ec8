<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M6 17V6L4.2 3.6q-.375-.5-.1-1.05T5 2h10.775q.925 0 1.575.65T18 4.225V5h2q.825 0 1.413.588T22 7v5q0 .825-.587 1.413T20 14h-2v3q0 .825-.587 1.413T16 19H8q-.825 0-1.412-.587T6 17m2 0h8V4H7l1 1.3zm10-5h2V7h-2zm-4.5-7q-.625 0-1.062.438T12 6.5v8q0 .625.438 1.063T13.5 16t1.063-.437T15 14.5v-8q0-.625-.437-1.062T13.5 5M4 22q-.425 0-.712-.288T3 21t.288-.712T4 20h16q.425 0 .713.288T21 21t-.288.713T20 22zm7.5-11.5"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
	name: "Kettle",
});
</script>
