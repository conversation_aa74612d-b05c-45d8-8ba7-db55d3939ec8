<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M12 18q-2.5 0-4.25-1.75T6 12t1.75-4.25T12 6t4.25 1.75T18 12t-1.75 4.25T12 18m-.75-2.075V13.8l-1.5 1.5q.35.225.713.388t.787.237m1.5 0q.4-.075.775-.238t.725-.387l-1.5-1.5zm2.55-1.675q.225-.35.388-.725t.237-.775H13.8zm-1.5-3h2.125q-.075-.4-.238-.775T15.3 9.75zm-1.05-1.05l1.5-1.5q-.35-.225-.712-.387t-.788-.238zM12 13q.425 0 .713-.288T13 12t-.288-.712T12 11t-.712.288T11 12t.288.713T12 13m-.75-2.8V8.075q-.4.075-.775.238T9.75 8.7zm-3.175 1.05H10.2l-1.5-1.5q-.225.35-.387.725t-.238.775m.625 3l1.5-1.5H8.075q.075.4.238.775t.387.725M5 21q-.825 0-1.412-.587T3 19V5q0-.825.588-1.412T5 3h14q.825 0 1.413.588T21 5v14q0 .825-.587 1.413T19 21zm0-2h14V5H5zM5 5v14z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
	name: "Heatpump",
});
</script>
