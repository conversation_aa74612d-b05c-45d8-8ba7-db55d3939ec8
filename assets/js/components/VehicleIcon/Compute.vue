<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M8.075 20q-.275 0-.513-.137q-.237-.138-.362-.388L5.25 16H6.7l1 2H10v-1H8.3l-1-2H4.7l-1.425-2.5q-.05-.125-.087-.25q-.038-.125-.038-.25t.038-.25q.037-.125.087-.25L4.7 9h2.6l1-2H10V6H7.7l-1 2H5.25L7.2 4.525q.125-.25.362-.388Q7.8 4 8.075 4H10.5q.425 0 .713.287q.287.288.287.713v4H10l-1 1h2.5v3H9.3l-1-2H6l-1 1h2.7l1 2h2.8v5q0 .425-.287.712q-.288.288-.713.288Zm5.425 0q-.425 0-.712-.288q-.288-.287-.288-.712v-5h2.8l1-2H19l-1-1h-2.3l-1 2h-2.2v-3H15l-1-1h-1.5V5q0-.425.288-.713Q13.075 4 13.5 4h2.425q.275 0 .513.137q.237.138.362.388L18.75 8H17.3l-1-2H14v1h1.7l1 2h2.6l1.425 2.5q.05.125.087.25q.038.125.038.25t-.038.25q-.037.125-.087.25L19.3 15h-2.6l-1 2H14v1h2.3l1-2h1.45l-1.95 3.475q-.125.25-.362.388q-.238.137-.513.137Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Compute",
});
</script>
