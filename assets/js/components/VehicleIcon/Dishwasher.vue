<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M8 7q.425 0 .713-.288T9 6t-.288-.712T8 5t-.712.288T7 6t.288.713T8 7m3 0q.425 0 .713-.288T12 6t-.288-.712T11 5t-.712.288T10 6t.288.713T11 7m1 11q1.575 0 2.688-1.075T15.8 14.3q0-.725-.25-1.412T14.8 11.7L12 8.9l-2.7 2.7q-.55.55-.837 1.25T8.2 14.3q.05 1.55 1.15 2.625T12 18m0-1.9q-.75 0-1.275-.525T10.2 14.3q0-.375.138-.712t.412-.613l1.25-1.25l1.225 1.225q.275.275.425.625t.15.725q0 .75-.525 1.275T12 16.1M6 22q-.825 0-1.412-.587T4 20V4q0-.825.588-1.412T6 2h12q.825 0 1.413.588T20 4v16q0 .825-.587 1.413T18 22zm0-2h12V4H6zm0 0V4z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
	name: "Dishwasher",
});
</script>
