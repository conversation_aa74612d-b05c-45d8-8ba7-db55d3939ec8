<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M3.725 7q-.65-.775-.937-1.475T2.475 4q0-.425.313-.712T3.525 3q.375 0 .613.3t.237.7q0 .5.175.925t.6.925q.75.9 1.063 1.613T6.5 9q-.025.425-.35.713T5.4 10q-.35 0-.562-.3t-.213-.675q0-.575-.238-1.05T3.726 7M7.75 7q-.65-.775-.95-1.475T6.475 4q0-.425.313-.712T7.525 3q.375 0 .613.3t.237.7q0 .5.175.925t.6.925q.75.9 1.063 1.613T10.5 9q-.025.425-.35.713T9.4 10q-.35 0-.562-.3t-.213-.675q.025-.575-.213-1.05T7.75 7m4 0q-.65-.775-.95-1.475T10.475 4q0-.425.313-.712T11.525 3q.375 0 .613.3t.237.7q0 .5.175.925t.6.925q.75.9 1.063 1.613T14.5 9q-.025.425-.35.713T13.4 10q-.35 0-.562-.3t-.213-.675q.025-.575-.213-1.05T11.75 7M5 20q-1.25 0-2.125-.875T2 17v-4q0-.425.288-.712T3 12h13.025q.125-.85.675-1.487t1.35-.913l3.675-1.225q.4-.125.775.05T23 9t-.062.775t-.588.5L18.675 11.5q-.3.1-.488.363T18 12.45V17q0 1.25-.875 2.125T15 20zm0-2h10q.425 0 .713-.288T16 17v-3H4v3q0 .425.288.713T5 18m5-2"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Cooking",
});
</script>
