<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M12 15q-.825 0-1.412-.587T10 13q0-.575.238-1.137t.912-1.613l.425-.625q.15-.225.425-.225t.425.225l.425.625q.675 1.05.913 1.613T14 13q0 .825-.587 1.413T12 15m-9 2h4.1q-.425-.425-.787-.925T5.675 15H3zm9 0q2.075 0 3.538-1.463T17 12t-1.463-3.537T12 7T8.463 8.463T7 12t1.463 3.538T12 17m6.325-8H21V7h-4.1q.425.425.788.925T18.325 9M3 19q0 .425-.288.713T2 20t-.712-.288T1 19v-6q0-.425.288-.712T2 12t.713.288T3 13h2.075q-.05-.25-.062-.488T5 12q0-2.925 2.038-4.962T12 5h9q0-.425.288-.712T22 4t.713.288T23 5v6q0 .425-.288.713T22 12t-.712-.288T21 11h-2.075q.05.25.063.488T19 12q0 2.925-2.037 4.963T12 19zm0-2v-2zm18-8V7zm-9 3"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Pump",
});
</script>
