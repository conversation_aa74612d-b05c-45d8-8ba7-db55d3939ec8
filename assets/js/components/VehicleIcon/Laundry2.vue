<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4.875 9.3L8 7.575V13.5q-.525.05-1.025.163T6 13.975v-3l-1.025.55q-.35.2-.75.088t-.6-.463l-2-3.475q-.2-.35-.088-.762T2 6.3l4.975-2.875q.3-.175.625-.3T8.275 3t.6.213t.375.537q.35.95.913 1.6T12 6t1.838-.65t.912-1.6q.125-.325.388-.537T15.75 3t.663.125t.612.3L22 6.3q.35.2.45.6t-.1.75l-1.975 3.5q-.2.35-.6.463t-.75-.088L18 10.975v4.8l-1.575 1.375q-.1.075-.2.138T16 17.4V7.575L19.125 9.3l1-1.75L16.3 5.325q-.6 1.225-1.763 1.95T12 8t-2.537-.725T7.7 5.325L3.85 7.55zM4 18.625q-.275-.325-.238-.737t.363-.688l1.4-1.2q.575-.5 1.313-.763t1.537-.262t1.525.263t1.3.762l2.9 2.475q.3.25.713.388t.837.137q.45 0 .838-.125t.687-.4l1.4-1.2q.325-.275.738-.25t.687.35t.238.738t-.363.687l-1.4 1.2q-.575.5-1.3.75T15.65 21t-1.537-.25T12.8 20l-2.9-2.475q-.3-.25-.687-.387T8.375 17q-.425 0-.837.138t-.713.387l-1.425 1.2q-.325.275-.725.25T4 18.625"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Laundry2",
});
</script>
