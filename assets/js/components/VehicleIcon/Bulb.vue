<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M11 21v-1q0-.425.288-.712T12 19t.713.288T13 20v1q0 .425-.288.713T12 22t-.712-.288T11 21M3 11h1q.425 0 .713.288T5 12t-.288.713T4 13H3q-.425 0-.712-.288T2 12t.288-.712T3 11m17 0h1q.425 0 .713.288T22 12t-.288.713T21 13h-1q-.425 0-.712-.288T19 12t.288-.712T20 11m-2.7 8.2l-.7-.7q-.275-.275-.275-.7t.275-.7t.7-.275t.7.275l.7.7q.275.275.275.7t-.275.7t-.7.275t-.7-.275m-12-1.4l.7-.7q.275-.275.7-.275t.7.275t.275.7t-.275.7l-.7.7q-.275.275-.7.275t-.7-.275t-.275-.7t.275-.7M12 17q-2.075 0-3.537-1.462T7 12q0-1.2.538-2.238T9 8V5q0-.825.588-1.412T11 3h2q.825 0 1.413.588T15 5v3q.925.725 1.463 1.763T17 12q0 2.075-1.463 3.538T12 17m-1-9.9q.25-.05.5-.075T12 7t.5.025t.5.075V5h-2zm1 7.9q1.25 0 2.125-.875T15 12t-.875-2.125T12 9t-2.125.875T9 12t.875 2.125T12 15m0-3"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Bulb",
});
</script>
