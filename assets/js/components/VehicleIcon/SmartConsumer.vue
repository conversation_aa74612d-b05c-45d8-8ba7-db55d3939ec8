<template>
	<svg width="1em" height="1em" viewBox="0 0 32 32">
		<path
			fill="currentColor"
			d="M12,27.933l0,-2c-2.356,-0.822 -4.278,-2.261 -5.767,-4.316c-1.489,-2.055 -2.233,-4.394 -2.233,-7.017c0,-1.667 0.317,-3.228 0.951,-4.683c0.633,-1.455 1.489,-2.721 2.565,-3.8c1.076,-1.078 2.343,-1.933 3.8,-2.566c1.457,-0.633 3.007,-0.95 4.651,-0.951c1.643,-0.001 3.199,0.316 4.666,0.951c1.468,0.634 2.746,1.49 3.834,2.566c1.088,1.077 1.949,2.343 2.584,3.8c0.634,1.457 0.951,3.018 0.949,4.683c-0,2.622 -0.75,4.95 -2.249,6.984c-1.5,2.034 -3.417,3.472 -5.751,4.316l-0,2.033c-0,0.378 -0.128,0.695 -0.384,0.951c-0.256,0.256 -0.572,0.384 -0.949,0.383c-0.377,-0.001 -0.694,-0.129 -0.95,-0.383c-0.256,-0.254 -0.384,-0.571 -0.384,-0.951l0,-1.4c-0.222,0.045 -0.444,0.067 -0.666,0.067l-0.7,-0c-0.223,-0 -0.439,-0.005 -0.651,-0.016c-0.212,-0.011 -0.428,-0.028 -0.649,-0.051l-0,1.4c-0,0.378 -0.128,0.695 -0.384,0.951c-0.256,0.256 -0.573,0.384 -0.95,0.383c-0.377,-0.001 -0.693,-0.129 -0.949,-0.383c-0.256,-0.254 -0.384,-0.571 -0.384,-0.951Zm4,-3.933c2.578,-0 4.778,-0.911 6.6,-2.733c1.822,-1.823 2.733,-4.023 2.733,-6.6c0,-2.578 -0.911,-4.778 -2.733,-6.6c-1.822,-1.823 -4.022,-2.734 -6.6,-2.734c-2.578,0 -4.778,0.911 -6.6,2.734c-1.822,1.822 -2.733,4.022 -2.733,6.6c-0,2.577 0.911,4.777 2.733,6.6c1.822,1.822 4.022,2.733 6.6,2.733Zm-4,-12c-0.376,0.002 -0.692,-0.126 -0.949,-0.383c-0.257,-0.257 -0.385,-0.573 -0.384,-0.95c0.001,-0.377 0.129,-0.694 0.384,-0.95c0.255,-0.256 0.571,-0.384 0.949,-0.384l8,0c0.378,0 0.694,0.128 0.949,0.384c0.255,0.256 0.383,0.573 0.384,0.95c0.001,0.377 -0.126,0.693 -0.382,0.949c-0.256,0.256 -0.573,0.384 -0.951,0.384l-8,-0Zm6.833,5.5l-1.833,-0.833l1.833,-0.834l0.834,-1.833l0.833,1.833l1.833,0.834l-1.833,0.833l-0.833,1.833l-0.834,-1.833Zm-6.433,1.767l-2.733,-1.267l2.733,-1.267l1.267,-2.733l1.266,2.733l2.734,1.267l-2.734,1.267l-1.266,2.733l-1.267,-2.733Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "SmartConsumer",
});
</script>
