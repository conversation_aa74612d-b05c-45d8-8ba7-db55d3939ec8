<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M3 21V6q0-1.25.875-2.125T6 3h4q1.25 0 2.125.875T13 6v15zm7-6h1v-5h-1zm-5 4h6v-2h-1q-.825 0-1.412-.587T8 15v-5q0-.825.588-1.412T10 8h1V6q0-.425-.288-.712T10 5H6q-.425 0-.712.288T5 6zm12.2-5.425q-.65 0-1.275-.175t-1.225-.45l.625-1.9q.5.225.988.375t.912.15q.3 0 .6-.1t.625-.3q.6-.425 1.2-.575t1.15-.15q.625 0 1.288.163t1.237.437l-.625 1.9q-.575-.2-1.062-.35t-.838-.15q-.3 0-.662.113t-.763.387q-.525.35-1.062.488t-1.113.137m.025-3.9q-.65 0-1.3-.175T14.7 9.05l.625-1.9q.65.275 1.1.4t.8.125q.3 0 .6-.087t.625-.313q.625-.425 1.213-.575t1.137-.15q.625 0 1.25.163t1.275.437l-.625 1.9q-.65-.225-1.1-.363t-.8-.137q-.325 0-.663.1t-.762.4q-.45.325-1.012.475t-1.138.15m0 7.8q-.65 0-1.287-.175t-1.238-.45l.625-1.9q.55.25 1.025.388t.875.137q.3 0 .6-.088t.625-.312q.575-.4 1.225-.562t1.15-.163q.625 0 1.275.175t1.225.425l-.625 1.9q-.65-.225-1.112-.363t-.788-.137q-.35 0-.712.113t-.713.387q-.425.3-.987.463t-1.163.162M11 19V5.788V6v-.212z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Airpurifier",
});
</script>
