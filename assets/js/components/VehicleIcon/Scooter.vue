<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M5 18q-1.25 0-2.125-.875T2 15q0-1.25.875-2.125T5 12q.975 0 1.738.562Q7.5 13.125 7.8 14h5.3q.275-1.7 1.412-2.975Q15.65 9.75 17.3 9.25L15.9 3H13q-.425 0-.712-.288Q12 2.425 12 2t.288-.713Q12.575 1 13 1h2.9q.675 0 1.237.438q.563.437.713 1.112l1.9 8.45H19q-1.65 0-2.825 1.175Q15 13.35 15 15v1H7.8q-.3.875-1.062 1.438Q5.975 18 5 18Zm0-2q.425 0 .713-.288Q6 15.425 6 15t-.287-.713Q5.425 14 5 14t-.713.287Q4 14.575 4 15t.287.712Q4.575 16 5 16Zm14 2q-1.25 0-2.125-.875T16 15q0-1.25.875-2.125T19 12q1.25 0 2.125.875T22 15q0 1.25-.875 2.125T19 18Zm0-2q.425 0 .712-.288Q20 15.425 20 15t-.288-.713Q19.425 14 19 14t-.712.287Q18 14.575 18 15t.288.712Q18.575 16 19 16Zm-6 7l-6-3h4v-2l6 3h-4Zm-8-8Zm14 0Z"
		></path>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Scooter",
});
</script>
