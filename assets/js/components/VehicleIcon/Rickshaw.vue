<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M6 17q-.975 0-1.737-.562T3.2 15H3q-.825 0-1.412-.587T1 13V5q0-.825.588-1.412T3 3h12.05q.45 0 .85.175t.7.525l3.95 4.75q.225.275.338.588T21 9.7v1.45q.875.3 1.438 1.088T23 14q0 1.25-.875 2.125T20 17q-.975 0-1.763-.562T17.15 15h-8.3q-.35.875-1.112 1.438T6 17M3 8h4V5H3zm6 5h5V5H9v3h2q.425 0 .713.288T12 9t-.288.713T11 10H9zm7-4h2.4L16 6.1zM6 15q.425 0 .713-.288T7 14t-.288-.712T6 13t-.712.288T5 14t.288.713T6 15m14 0q.425 0 .713-.288T21 14t-.288-.712T20 13t-.712.288T19 14t.288.713T20 15m-7.725 7.65L7.95 20.475q-.175-.1-.137-.288T8.05 20H11v-1.2q0-.275.238-.425t.487-.025l4.325 2.175q.175.1.138.288T15.95 21H13v1.2q0 .275-.238.425t-.487.025M3 10v3h.15q.35-.875 1.113-1.437T6 11q.275 0 .525.038T7 11.15V10zm13 3h1.15q.225-.65.713-1.137T19 11.15V11h-3zM3 10h4zm13 1h3z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Rickshaw",
});
</script>
