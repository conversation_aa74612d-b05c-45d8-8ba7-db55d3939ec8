<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4 12.6q0-1.65.613-3.062T6.35 7.05l4.6-4.525q.225-.2.5-.312T12 2.1t.55.113t.5.312l4.6 4.525q.725.725 1.263 1.6t.812 1.875q.1.35-.137.613t-.588.337t-.75-.137t-.625-.788t-.562-1.1t-.813-.95L12 4.3L7.75 8.5q-.875.825-1.312 1.863T6 12.6q0 1.9 1.138 3.425t2.887 2.15q.575.2.775.6t.15.75t-.312.588t-.613.137Q7.4 19.575 5.7 17.463T4 12.6m13.375 2.9q-.45-.2-.913-.35T15.5 15q-.4 0-.775.088t-.725.237q-.275.125-.575.025T13 14.975q-.125-.3 0-.6t.425-.425q.5-.2 1.025-.325t1.05-.125q.575 0 1.138.138t1.087.337q.45.2.913.35t.962.15q.4 0 .775-.087t.725-.238q.275-.125.575-.025t.425.375q.125.3 0 .6t-.425.425q-.5.2-1.025.325t-1.05.125q-.575 0-1.137-.137t-1.088-.338m0 3q-.45-.2-.913-.35T15.5 18q-.4 0-.775.088t-.725.237q-.275.125-.575.025T13 17.975q-.125-.3 0-.6t.425-.425q.5-.2 1.025-.325t1.05-.125q.575 0 1.138.137t1.087.338q.45.2.913.35t.962.15q.4 0 .775-.088t.725-.237q.275-.125.575-.025t.425.375q.125.3 0 .6t-.425.425q-.5.2-1.025.325t-1.05.125q-.575 0-1.137-.137t-1.088-.338m0 3q-.45-.2-.913-.35T15.5 21q-.4 0-.775.088t-.725.237q-.275.125-.575.025T13 20.975q-.125-.3 0-.6t.425-.425q.5-.2 1.025-.325t1.05-.125q.575 0 1.138.137t1.087.338q.45.2.913.35t.962.15q.4 0 .775-.088t.725-.237q.275-.125.575-.025t.425.375q.125.3 0 .6t-.425.425q-.5.2-1.025.325t-1.05.125q-.575 0-1.137-.137t-1.088-.338"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
	name: "Dryer",
});
</script>
