<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4 20q-.825 0-1.412-.587T2 18V6q0-.825.588-1.412T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.587 1.413T20 20zm0-2h16V6H4zm2-1h8q.425 0 .713-.288T15 16V8q0-.425-.288-.712T14 7H6q-.425 0-.712.288T5 8v8q0 .425.288.713T6 17m12 0q.425 0 .713-.288T19 16t-.288-.712T18 15t-.712.288T17 16t.288.713T18 17M7 15V9h6v6zm11-2q.425 0 .713-.288T19 12t-.288-.712T18 11t-.712.288T17 12t.288.713T18 13m0-4q.425 0 .713-.288T19 8t-.288-.712T18 7t-.712.288T17 8t.288.713T18 9M4 18V6z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Microwave",
});
</script>
