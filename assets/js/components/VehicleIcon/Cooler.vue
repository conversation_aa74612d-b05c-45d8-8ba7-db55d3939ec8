<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="m11 17.85l-2.575 2.525q-.275.275-.687.275q-.413 0-.688-.3q-.3-.275-.3-.687q0-.413.3-.713L11 15v-2H9l-3.975 3.975q-.275.275-.687.275q-.413 0-.713-.3q-.275-.275-.275-.688q0-.412.275-.687L6.15 13H2.975q-.425 0-.7-.288Q2 12.425 2 12t.288-.713Q2.575 11 3 11h3.15L3.625 8.45q-.275-.275-.275-.688q0-.412.3-.712q.275-.275.688-.275q.412 0 .712.275L9 11h2V9L7.025 5.05q-.275-.275-.275-.688q0-.412.3-.712q.275-.275.688-.275q.412 0 .687.275L11 6.15V3q0-.425.288-.713Q11.575 2 12 2t.713.287Q13 2.575 13 3v3.15l2.55-2.5q.275-.275.688-.275q.412 0 .712.275q.275.3.275.712q0 .413-.275.688L13 9v2h2l3.95-3.95q.275-.275.688-.275q.412 0 .712.3q.275.275.275.687q0 .413-.275.688L17.85 11H21q.425 0 .712.287q.288.288.288.713t-.288.712Q21.425 13 21 13h-3.15l2.5 2.575q.275.275.275.687q0 .413-.275.688q-.3.3-.712.3q-.413 0-.688-.3L15 13h-2v2l3.95 3.975q.275.275.275.688q0 .412-.3.712q-.275.275-.687.275q-.413 0-.688-.275L13 17.85v3.175q0 .425-.287.7Q12.425 22 12 22t-.712-.288Q11 21.425 11 21Z"
		></path>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Cooler",
});
</script>
