<template>
	<svg width="1em" height="1em" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M20 12H4q-.825 0-1.412-.587T2 10V4q0-.825.588-1.412T4 2h16q.825 0 1.413.588T22 4v6q0 .825-.587 1.413T20 12M4 19v-2q1.25 0 2.125-.875T7 14h2q0 2.075-1.463 3.538T4 19m16 0q-2.075 0-3.537-1.463T15 14h2q0 1.25.875 2.125T20 17zm-9 1v-6h2v6zm9-10H4zM6 10V8q0-.825.588-1.412T8 6h8q.825 0 1.413.588T18 8v2h-2V8H8v2zm-2 0h16V4H4z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Climate",
});
</script>
