<template>
	<YamlModal
		id="modbusProxyModal"
		:title="$t('config.modbusproxy.title')"
		:description="$t('config.modbusproxy.description')"
		docs="/docs/reference/configuration/modbusproxy"
		:defaultYaml="defaultYaml"
		endpoint="/config/modbusproxy"
		removeKey="modbusproxy"
		size="md"
		@changed="$emit('changed')"
	/>
</template>

<script>
import YamlModal from "./YamlModal.vue";
import defaultYaml from "./defaultYaml/modbusproxy.yaml?raw";

export default {
	name: "ModbusProxyModal",
	components: { YamlModal },
	emits: ["changed"],
	data() {
		return { defaultYaml: defaultYaml.trim() };
	},
};
</script>
