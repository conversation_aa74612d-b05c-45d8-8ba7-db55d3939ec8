title: green Honda
icon: car
capacity: 50 # kWh

## required attributes

soc: # state of charge
  source: const
  value: 42 # %

## optional attributes (read-only)

#limitsoc: # in-vehicle charge limit
#  source: const
#  value: 80 # %
#status: # status [A..F]
#  source: const
#  value: "A"
#range: # range
#  source: const
#  value: 123 # km
#climater: # climate active
#  source: const
#  value: true
#getmaxcurrent: # max charge current
#  source: const
#  value: 16.0 # A
#finishtime: # finish time (RFC3339)
#  source: const
#  value: "2030-01-01T00:00:00Z"

## optional attributes (writeable)

#wakeup: # wake up vehicle
#    source: js
#    script: console.log(wakeup);
#chargeenable: # start/stop charging
#    source: js
#    script: console.log(chargeenable);
#maxcurrent: # set max charge current
#    source: js
#    script: console.log(maxcurrent);
