## required attributes [type: switchsocket]

enabled: # is switch enabled?
  source: const
  value: true
enable: # enable/disable switch
  source: js
  script: console.log(enable)
power: # charge power reading in W
  source: const
  value: 11000
standbypower: 20 # in W, below values will be treaded as inactive

features:
  - integrateddevice # no charging sessions, no connected vehicles

## optional attributes (read-only)

#icon: generic # icon for UI purpose only
#energy: # meter reading in kWh
#  source: const
#  value: 42.5
#soc: # charge level (%) of the connected device
#  source: const
#  value: 75
