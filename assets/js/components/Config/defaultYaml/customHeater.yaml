## required attributes [type: custom]

status: # heating status (B: standby, C: heating)
  source: const
  value: "B"
enabled: # is heating enabled?
  source: const
  value: true
enable: # enable/disable charging
  source: js
  script: console.log(enable)
maxcurrent: # set maximum heating current in A
  source: js
  script: console.log(maxcurrent)

features:
  - heating # treat as a heating device
  - integrateddevice # no charging sessions, no connected vehicles

icon: heater # icon for UI purpose only

## optional attributes (read-only)

#power: # heating power in W
#  source: const
#  value: 11000
#energy: # meter reading in kWh
#  source: const
#  value: 42.5
#powers: # phase powers in W
#  - source: const
#    value: 3600
#  - source: const
#    value: 3700
#  - source: const
#    value: 3800
#currents: # phase currents in A
#  - source: const
#    value: 16.0
#  - source: const
#    value: 16.1
#  - source: const
#    value: 16.2
#voltages: # phase voltages in V
#  - source: const
#    value: 230.1
#  - source: const
#    value: 230.2
#  - source: const
#    value: 230.3

## optional attributes (writeable)

#maxcurrentmillis: # set maximum heating current in A with decimal precision
#  source: js
#  script: console.log(maxcurrentmillis);
