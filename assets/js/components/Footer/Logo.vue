<template>
	<svg
		viewBox="0 0 122 35"
		xmlns="http://www.w3.org/2000/svg"
		fill-rule="evenodd"
		clip-rule="evenodd"
		stroke-linejoin="round"
		stroke-miterlimit="2"
	>
		<g class="letter">
			<path d="M26.38,19.19c-1.2,0-2.32-0.7-2.85-1.78l-4.21-8.67c-0.86-1.75-2.6-2.85-4.54-2.85s-3.7,1.09-4.56,2.85 l-4.86,9.93c-0.14,0.28-0.43,0.47-0.75,0.47c-0.46,0-0.83-0.37-0.83-0.83V8h-3.8v12.5c0,2.56,2.08,4.64,4.64,4.64 c1.76,0,3.4-1.02,4.17-2.6l4.86-9.93c0.31-0.64,0.9-0.71,1.14-0.71s0.83,0.07,1.14,0.71l4.22,8.64c1.16,2.38,3.62,3.92,6.27,3.92 h22.2v-3.81H26.38z"/>
			<rect x="25.75" y="8" width="22.84" height="3.81"/>
			<path d="M25.75,14.67c0,1.05,0.85,1.9,1.9,1.9h20.93v-3.81H25.75V14.67L25.75,14.67z"/>
			<path d="M51.55,18.05c0,2.8,2.27,5.08,5.08,5.08h16.91v-3.81H56.63c-0.7,0-1.27-0.57-1.27-1.27v-1.48h18.18v-3.81 h-21.99C51.55,12.76,51.55,18.05,51.55,18.05z"/>
			<rect x="51.55" y="8" width="21.99" height="3.81"/>
			<path d="M92.67,12.76h-11.31c-0.82,0-1.48-0.66-1.48-1.48s0.66-1.48,1.48-1.48h15.43V8H81.36c-2.92,0-5.28,2.37-5.28,5.28 s2.37,5.28,5.28,5.28h11.31c0.76,0,1.37,0.62,1.37,1.37s-0.62,1.37-1.37,1.37H76.73v3.81h15.96c2.86,0,5.18-2.32,5.18-5.18 s-2.32-5.18-5.18-5.18L92.67,12.76z"/>
			<path d="M115.93,12.76h-11.31c-0.82,0-1.48-0.66-1.48-1.48s0.66-1.48,1.48-1.48h15.43V8h-15.43c-2.92,0-5.28,2.37-5.28,5.28 s2.37,5.28,5.28,5.28h11.31c0.76,0,1.37,0.62,1.37,1.37s-0.62,1.37-1.37,1.37h-15.96v3.81h15.96c2.86,0,5.18-2.32,5.18-5.18 s-2.32-5.18-5.18-5.18L115.93,12.76z"/>
		</g>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
	name: "Logo",
});
</script>
<style scoped>
.letter {
	fill: #1c2445;
}
html.dark .letter {
	fill: var(--bs-white);
}
</style>
