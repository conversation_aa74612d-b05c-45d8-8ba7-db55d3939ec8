<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			fill="currentColor"
			d="M5.333,24l18.667,0l0,-5.433c0.467,-0.067 0.922,-0.167 1.367,-0.3c0.444,-0.134 0.877,-0.3 1.3,-0.5l-0,10.233c-0,0.378 -0.128,0.695 -0.384,0.951c-0.256,0.256 -0.573,0.383 -0.95,0.382l-1.333,0c-0.378,0 -0.694,-0.128 -0.949,-0.384c-0.255,-0.256 -0.383,-0.572 -0.384,-0.949l-0,-1.333l-16,-0l-0,1.333c-0,0.378 -0.128,0.695 -0.384,0.951c-0.256,0.256 -0.573,0.383 -0.95,0.382l-1.333,0c-0.378,0 -0.694,-0.128 -0.949,-0.384c-0.255,-0.256 -0.383,-0.572 -0.384,-0.949l-0,-10.667l2.8,-8c0.133,-0.4 0.372,-0.722 0.717,-0.966c0.345,-0.245 0.728,-0.367 1.149,-0.367l6.1,0c-0.066,0.444 -0.1,0.889 -0.1,1.333c0,0.445 0.034,0.889 0.1,1.334l-5.633,-0l-1.4,4l8.633,-0c0.378,0.533 0.8,1.028 1.267,1.484c0.467,0.456 0.989,0.85 1.567,1.182l-12.534,0l0,6.667Zm15.334,-1.333c-0.552,0.003 -1.024,-0.191 -1.416,-0.583c-0.392,-0.392 -0.587,-0.864 -0.584,-1.417c0.002,-0.553 0.197,-1.025 0.584,-1.416c0.386,-0.391 0.858,-0.586 1.416,-0.584c0.557,0.001 1.029,0.196 1.417,0.584c0.388,0.387 0.582,0.859 0.583,1.416c0.001,0.556 -0.194,1.029 -0.583,1.417c-0.389,0.388 -0.862,0.583 -1.417,0.583Zm-12,-0c-0.552,0.003 -1.024,-0.191 -1.416,-0.583c-0.392,-0.392 -0.587,-0.864 -0.584,-1.417c0.002,-0.553 0.197,-1.025 0.584,-1.416c0.386,-0.391 0.858,-0.586 1.416,-0.584c0.557,0.001 1.029,0.196 1.417,0.584c0.388,0.387 0.582,0.859 0.583,1.416c0.001,0.556 -0.194,1.029 -0.583,1.417c-0.389,0.388 -0.862,0.583 -1.417,0.583Zm13.968,-20.009c1.844,-0 3.417,0.65 4.717,1.95c1.301,1.301 1.95,2.873 1.949,4.716c-0,1.844 -0.651,3.416 -1.95,4.718c-1.3,1.301 -2.872,1.951 -4.716,1.949c-1.845,-0.002 -3.417,-0.652 -4.716,-1.951c-1.3,-1.298 -1.95,-2.87 -1.951,-4.716c-0.001,-1.845 0.649,-3.417 1.951,-4.716c1.301,-1.298 2.873,-1.948 4.716,-1.95Zm-0.934,9.666l4.734,-4.733l-1,-1l-3.734,3.733l-1.866,-1.866l-1,1l2.866,2.866Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "VehicleLimitReached",
	mixins: [icon],
});
</script>
