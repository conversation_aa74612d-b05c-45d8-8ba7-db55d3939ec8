<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			d="M16,28c-1.844,0 -3.416,-0.65 -4.716,-1.951c-1.3,-1.3 -1.95,-2.872 -1.951,-4.716c0,-1.066 0.234,-2.061 0.7,-2.984c0.467,-0.922 1.123,-1.705 1.967,-2.349l0,-8c0,-1.111 0.389,-2.056 1.167,-2.833c0.777,-0.778 1.722,-1.167 2.833,-1.167c1.111,0 2.056,0.389 2.833,1.167c0.778,0.777 1.167,1.722 1.167,2.833l0,8c0.844,0.644 1.5,1.428 1.967,2.351c0.466,0.922 0.7,1.917 0.7,2.982c-0,1.845 -0.651,3.417 -1.951,4.718c-1.3,1.3 -2.872,1.95 -4.716,1.949m-1.333,-18l2.666,0l0,-2c0,-0.378 -0.128,-0.694 -0.384,-0.949c-0.256,-0.255 -0.572,-0.383 -0.949,-0.384c-0.377,-0.001 -0.693,0.127 -0.949,0.384c-0.256,0.257 -0.384,0.573 -0.384,0.949l-0,2Z"
			fill="currentColor"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "TempLimit",
	mixins: [icon],
});
</script>
