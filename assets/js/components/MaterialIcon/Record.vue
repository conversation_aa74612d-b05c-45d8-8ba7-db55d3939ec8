<template>
	<svg :style="svgStyle" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M12 17q-2.075 0-3.537-1.463T7 12t1.463-3.537T12 7t3.538 1.463T17 12t-1.463 3.538T12 17m8-5q0-1.05-.25-2.025t-.725-1.85q-.2-.375-.162-.8t.387-.675t.775-.15t.625.45q.65 1.125 1 2.388T22 12t-.363 2.675t-1.012 2.4q-.2.35-.612.438t-.763-.163t-.4-.675t.15-.8q.475-.875.738-1.838T20 12m-8-8q-1.075 0-2.037.263T8.125 5q-.375.2-.8.15t-.675-.4t-.162-.763t.437-.612q1.125-.65 2.4-1.012T12 2t2.675.363t2.4 1.012q.375.2.463.613t-.163.762t-.675.4t-.8-.15q-.875-.475-1.85-.737T12 4m-8 8q0 1.075.263 2.038T5 15.875q.2.375.15.8t-.4.675t-.763.163t-.612-.438q-.65-1.125-1.012-2.4T2 12t.35-2.662t1-2.388q.2-.35.625-.45t.775.15t.388.675t-.163.8Q4.5 9 4.25 9.975T4 12m8 8q1.05 0 2.025-.25t1.85-.725q.375-.2.788-.15t.662.4t.163.763t-.438.612q-1.125.65-2.387 1T12 22t-2.662-.35t-2.388-1q-.35-.2-.45-.625t.15-.775t.675-.387t.8.162q.875.475 1.85.725T12 20"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Record",
	mixins: [icon],
});
</script>
