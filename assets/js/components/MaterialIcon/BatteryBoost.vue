<template>
	<svg :style="svgStyle" viewBox="0 0 48 48">
		<g>
			<path
				fill="currentColor"
				d="M35,9.996l-3,0l0,-4c0,-1.097 -0.903,-2 -2,-2l-12,0c-1.097,0 -2,0.903 -2,2l0,4l-3,0c-1.097,0 -2,0.903 -2,2l0,30c0,1.097 0.903,2 2,2l22,0c1.097,0 2,-0.903 2,-2l0,-30c0,-1.097 -0.903,-2 -2,-2Zm-15,-2l8,-0l0,2l-8,-0l0,-2Zm13,32l-18,0l0,-26l18,0l0,26Z"
			/>
			<path
				fill="currentColor"
				d="M24.741,18.029c-0.395,-0.103 -0.812,0.073 -1.012,0.43l-5.077,9.05c-0.157,0.278 -0.154,0.619 0.008,0.895c0.162,0.275 0.458,0.445 0.777,0.445l3.727,-0l-0,6.251c-0,0.425 0.297,0.792 0.712,0.88c0.063,0.014 0.126,0.02 0.188,0.02c0.349,-0 0.675,-0.204 0.822,-0.534c0.966,-2.174 1.917,-4.395 2.823,-6.603c0.571,-1.394 1.142,-2.82 1.693,-4.237c0.108,-0.277 0.072,-0.589 -0.096,-0.834c-0.167,-0.245 -0.445,-0.392 -0.742,-0.392l-3.15,-0l-0,-4.5c-0,-0.41 -0.277,-0.767 -0.673,-0.871Z"
			/>
		</g>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "BatteryBoost",
	mixins: [icon],
});
</script>
