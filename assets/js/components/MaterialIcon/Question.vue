<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			fill="currentColor"
			d="M16,24c0.467,0 0.861,-0.161 1.184,-0.484c0.323,-0.323 0.484,-0.717 0.483,-1.183c-0.001,-0.465 -0.163,-0.86 -0.484,-1.184c-0.322,-0.323 -0.716,-0.484 -1.183,-0.482c-0.467,0.001 -0.861,0.163 -1.184,0.484c-0.323,0.321 -0.484,0.715 -0.483,1.182c0.001,0.468 0.163,0.863 0.484,1.184c0.322,0.322 0.716,0.483 1.183,0.483m0,-13.733c0.578,-0 1.084,0.177 1.517,0.533c0.434,0.356 0.651,0.811 0.65,1.367c-0,0.511 -0.162,0.966 -0.484,1.366c-0.323,0.4 -0.673,0.767 -1.05,1.1c-0.577,0.511 -1.016,0.995 -1.316,1.451c-0.299,0.456 -0.472,1.006 -0.517,1.649c-0.022,0.311 0.089,0.584 0.333,0.818c0.245,0.233 0.534,0.35 0.867,0.349c0.311,0 0.595,-0.111 0.851,-0.333c0.256,-0.223 0.405,-0.5 0.449,-0.834c0.044,-0.377 0.178,-0.711 0.4,-1c0.222,-0.289 0.544,-0.644 0.967,-1.066c0.777,-0.778 1.294,-1.406 1.55,-1.883c0.256,-0.477 0.384,-1.05 0.383,-1.717c0,-1.2 -0.433,-2.178 -1.3,-2.934c-0.867,-0.755 -1.967,-1.133 -3.3,-1.133c-0.911,0 -1.728,0.206 -2.449,0.617c-0.722,0.412 -1.272,0.995 -1.651,1.75c-0.133,0.266 -0.139,0.539 -0.016,0.817c0.123,0.278 0.328,0.472 0.616,0.583c0.288,0.11 0.583,0.11 0.884,-0c0.301,-0.111 0.54,-0.288 0.716,-0.534c0.244,-0.311 0.528,-0.549 0.851,-0.716c0.322,-0.166 0.672,-0.249 1.049,-0.25"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Question",
	mixins: [icon],
});
</script>
