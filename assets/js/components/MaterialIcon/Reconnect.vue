<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			fill="currentColor"
			d="M5.505,24.609c-0.595,-0.935 -0.94,-2.041 -0.94,-3.222c-0,-1.632 0.661,-3.198 1.831,-4.336l1.995,-1.994c0.52,-0.521 1.364,-0.521 1.885,0l0.391,0.391l1.724,-1.724c0.52,-0.52 1.365,-0.52 1.885,-0c0.52,0.52 0.52,1.365 0,1.885l-1.724,1.724l2.115,2.115l1.724,-1.724c0.52,-0.52 1.365,-0.52 1.885,-0c0.52,0.52 0.52,1.365 0,1.885l-1.724,1.724l0.391,0.391c0.521,0.521 0.521,1.365 -0,1.885c-0,0 -1.994,1.995 -1.994,1.995c-1.138,1.17 -2.704,1.831 -4.336,1.831c-1.181,0 -2.287,-0.345 -3.222,-0.94l-2.448,2.448c-0.521,0.52 -1.365,0.52 -1.886,-0c-0.52,-0.521 -0.52,-1.365 0,-1.886l2.448,-2.448Zm4.219,-6.333l-0.391,-0.39l-1.057,1.057c-0.005,0.005 -0.01,0.01 -0.015,0.015c-0.658,0.637 -1.03,1.514 -1.03,2.429c0,0.797 0.282,1.533 0.75,2.114c0.106,0.059 0.205,0.133 0.295,0.223c0.09,0.09 0.164,0.189 0.223,0.295c0.581,0.468 1.317,0.75 2.114,0.75c0.915,-0 1.792,-0.372 2.429,-1.03c0.005,-0.005 0.01,-0.01 0.015,-0.015c0,-0 1.057,-1.057 1.057,-1.057l-4.39,-4.391Zm14.885,-12.771l2.448,-2.448c0.521,-0.52 1.365,-0.52 1.886,0c0.52,0.521 0.52,1.365 -0,1.886l-2.448,2.448c0.595,0.935 0.94,2.041 0.94,3.222c0,1.632 -0.661,3.198 -1.831,4.336l-1.995,1.994c-0.52,0.521 -1.364,0.521 -1.885,-0l-6.667,-6.667c-0.521,-0.521 -0.521,-1.365 0,-1.885c0,-0 1.994,-1.995 1.994,-1.995c1.138,-1.17 2.704,-1.831 4.336,-1.831c1.181,-0 2.287,0.345 3.222,0.94Zm-0.59,2.994c-0.106,-0.059 -0.205,-0.133 -0.295,-0.223c-0.09,-0.09 -0.164,-0.189 -0.223,-0.295c-0.581,-0.468 -1.317,-0.75 -2.114,-0.75c-0.915,0 -1.792,0.372 -2.429,1.03c-0.005,0.005 -0.01,0.01 -0.015,0.015c-0,0 -1.057,1.057 -1.057,1.057l4.781,4.781l1.057,-1.057c0.005,-0.005 0.01,-0.01 0.015,-0.015c0.658,-0.637 1.03,-1.514 1.03,-2.429c-0,-0.797 -0.282,-1.533 -0.75,-2.114Z"
		/>
		<path
			fill="currentColor"
			d="M25.333,20l0.781,-0l-0.39,-0.391c-0.52,-0.52 -0.52,-1.365 -0,-1.885c0.52,-0.52 1.365,-0.52 1.885,-0l2.667,2.667c0.387,0.386 0.492,0.962 0.289,1.453c-0.066,0.161 -0.164,0.308 -0.289,0.432l-2.667,2.667c-0.52,0.52 -1.365,0.52 -1.885,-0c-0.52,-0.521 -0.52,-1.365 -0,-1.886l0.39,-0.39l-0.781,-0c-1.463,-0 -2.666,1.203 -2.666,2.666c-0,1.463 1.203,2.667 2.666,2.667c0.656,-0 1.289,-0.242 1.778,-0.679c0.549,-0.491 1.392,-0.444 1.883,0.105c0.49,0.548 0.443,1.392 -0.105,1.882c-0.978,0.875 -2.244,1.359 -3.556,1.359c-2.926,-0 -5.333,-2.408 -5.333,-5.334c-0,-2.925 2.408,-5.333 5.333,-5.333Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Reconnect",
	mixins: [icon],
});
</script>
