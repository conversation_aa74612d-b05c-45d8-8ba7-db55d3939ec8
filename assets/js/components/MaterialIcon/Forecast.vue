<template>
	<svg :style="svgStyle" viewBox="0 0 48 48">
		<path
			fill="currentColor"
			d="M5.78,31c1.791,-3.381 3.675,-6.042 5.643,-9.059c1.422,-2.18 2.889,-4.118 4.384,-5.67l2.881,2.774c-1.338,1.39 -2.642,3.13 -3.914,5.081c-1.523,2.335 -3,4.445 -4.418,6.874l-4.576,-0Zm32.023,0c-0.661,-1.3 -1.346,-2.567 -2.051,-3.781l3.458,-2.011c1.067,1.836 2.089,3.787 3.055,5.792l-4.462,0Zm-16.172,-14.32l-1.915,-3.512c1.409,-0.768 2.842,-1.169 4.286,-1.168c1.35,0.001 2.702,0.328 4.04,0.954l-1.696,3.623c-0.78,-0.365 -1.562,-0.576 -2.348,-0.577c-0.8,-0.001 -1.586,0.254 -2.367,0.68Zm7.78,2.072l2.729,-2.924c1.591,1.484 3.149,3.369 4.639,5.53l-3.292,2.271c-1.313,-1.902 -2.675,-3.57 -4.076,-4.877Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Forecast",
	mixins: [icon],
});
</script>
