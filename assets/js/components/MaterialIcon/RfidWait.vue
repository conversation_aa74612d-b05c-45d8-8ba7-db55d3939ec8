<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			d="M26.667,26.667l-5.4,-0c-0.378,-0 -0.695,-0.128 -0.95,-0.384c-0.255,-0.256 -0.383,-0.573 -0.384,-0.95c-0.001,-0.377 0.127,-0.693 0.384,-0.949c0.257,-0.256 0.574,-0.384 0.95,-0.384l5.4,0l-0,-5.333c-0,-0.378 0.128,-0.695 0.384,-0.95c0.256,-0.255 0.572,-0.383 0.949,-0.384c0.377,-0.001 0.694,0.127 0.951,0.384c0.257,0.257 0.384,0.574 0.382,0.95l0,5.333c0,0.733 -0.261,1.361 -0.782,1.884c-0.522,0.523 -1.15,0.784 -1.884,0.783m-22.667,-17.134c-0.378,0 -0.694,-0.128 -0.949,-0.384c-0.255,-0.256 -0.383,-0.572 -0.384,-0.949l-0,-0.2c-0,-0.733 0.261,-1.361 0.784,-1.883c0.522,-0.521 1.15,-0.783 1.882,-0.784l8,0c0.378,0 0.695,0.128 0.951,0.384c0.256,0.256 0.384,0.573 0.383,0.95c-0.001,0.377 -0.129,0.693 -0.384,0.95c-0.255,0.257 -0.572,0.385 -0.95,0.383l-8,-0l0,0.2c0,0.378 -0.128,0.695 -0.384,0.951c-0.256,0.256 -0.572,0.383 -0.949,0.382m0.667,17.134c-0.556,-0 -1.028,-0.195 -1.416,-0.583c-0.389,-0.388 -0.583,-0.861 -0.584,-1.417c-0.001,-0.557 0.193,-1.029 0.584,-1.416c0.39,-0.388 0.862,-0.583 1.416,-0.584c0.553,-0.002 1.026,0.193 1.417,0.584c0.391,0.391 0.585,0.863 0.583,1.416c-0.003,0.553 -0.197,1.025 -0.583,1.417c-0.386,0.392 -0.858,0.586 -1.417,0.583m6,-0c-0.356,-0 -0.667,-0.106 -0.934,-0.318c-0.266,-0.211 -0.433,-0.494 -0.5,-0.849c-0.244,-1.4 -0.872,-2.589 -1.882,-3.567c-1.011,-0.977 -2.217,-1.589 -3.618,-1.833c-0.333,-0.044 -0.594,-0.205 -0.782,-0.483c-0.189,-0.277 -0.283,-0.594 -0.284,-0.95c-0,-0.378 0.117,-0.695 0.35,-0.95c0.234,-0.255 0.517,-0.361 0.85,-0.317c2.089,0.267 3.877,1.144 5.366,2.633c1.489,1.489 2.378,3.278 2.667,5.367c0.044,0.356 -0.056,0.656 -0.3,0.9c-0.244,0.244 -0.556,0.367 -0.933,0.367m5.333,-0c-0.378,-0 -0.694,-0.123 -0.949,-0.367c-0.255,-0.244 -0.406,-0.556 -0.451,-0.933c-0.311,-2.845 -1.472,-5.256 -3.483,-7.234c-2.01,-1.977 -4.438,-3.111 -7.284,-3.4c-0.355,-0.044 -0.639,-0.2 -0.85,-0.466c-0.212,-0.267 -0.317,-0.578 -0.316,-0.934c-0,-0.377 0.117,-0.7 0.35,-0.966c0.234,-0.267 0.517,-0.378 0.85,-0.334c3.577,0.289 6.622,1.684 9.133,4.184c2.511,2.501 3.933,5.528 4.267,9.083c0.044,0.378 -0.061,0.7 -0.316,0.967c-0.255,0.266 -0.572,0.4 -0.951,0.4"
			fill="currentColor"
		/>
		<path
			d="M23.989,14.594c-1.844,-0 -3.416,-0.651 -4.716,-1.951c-1.3,-1.3 -1.95,-2.872 -1.951,-4.716c-0.001,-1.844 0.65,-3.416 1.951,-4.716c1.301,-1.3 2.873,-1.951 4.716,-1.951c1.843,0 3.415,0.651 4.717,1.951c1.303,1.3 1.952,2.872 1.95,4.716c-0.003,1.844 -0.653,3.416 -1.951,4.717c-1.298,1.302 -2.87,1.951 -4.716,1.95m0.667,-6.934l-0,-3.066c-0,-0.178 -0.067,-0.334 -0.2,-0.467c-0.134,-0.133 -0.289,-0.2 -0.467,-0.2c-0.178,-0 -0.333,0.067 -0.467,0.2c-0.133,0.133 -0.2,0.289 -0.2,0.467l0,3.033c0,0.178 0.034,0.35 0.1,0.517c0.067,0.167 0.167,0.317 0.3,0.45l2,2c0.134,0.133 0.289,0.2 0.467,0.2c0.178,-0 0.333,-0.067 0.467,-0.2c0.133,-0.134 0.2,-0.289 0.2,-0.467c-0,-0.178 -0.067,-0.333 -0.2,-0.467l-2,-2Z"
			fill="currentColor"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "RfidWait",
	mixins: [icon],
});
</script>
