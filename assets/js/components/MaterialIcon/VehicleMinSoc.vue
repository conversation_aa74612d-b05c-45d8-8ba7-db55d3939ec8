<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			fill="currentColor"
			d="M8,20.167l0,0.666c0,0.556 -0.194,1.028 -0.583,1.418c-0.388,0.389 -0.861,0.583 -1.417,0.582c-0.556,-0.001 -1.028,-0.195 -1.416,-0.582c-0.388,-0.388 -0.582,-0.86 -0.584,-1.418l0,-9.533c0,-0.156 0.011,-0.311 0.033,-0.467c0.023,-0.155 0.056,-0.3 0.1,-0.433l2.5,-7.1c0.178,-0.533 0.5,-0.967 0.967,-1.3c0.467,-0.333 0.989,-0.5 1.567,-0.5l13.666,-0c0.578,-0 1.1,0.167 1.567,0.5c0.467,0.333 0.789,0.767 0.967,1.3l2.5,7.1c0.044,0.133 0.077,0.278 0.1,0.433c0.022,0.156 0.033,0.311 0.033,0.467l0,9.533c0,0.556 -0.194,1.028 -0.583,1.418c-0.388,0.389 -0.861,0.583 -1.417,0.582c-0.556,-0.001 -1.028,-0.195 -1.416,-0.582c-0.388,-0.388 -0.582,-0.86 -0.584,-1.418l0,-0.666l-16,-0Zm-0.267,-12l16.534,-0l-1.4,-4l-13.734,-0l-1.4,4Zm-1.066,2.666l-0,6.667l-0,-6.667Zm3.333,5.334c0.556,-0 1.028,-0.195 1.417,-0.583c0.39,-0.388 0.584,-0.861 0.583,-1.417c-0.001,-0.557 -0.195,-1.029 -0.583,-1.416c-0.387,-0.388 -0.86,-0.583 -1.417,-0.584c-0.557,-0.002 -1.029,0.193 -1.416,0.584c-0.387,0.391 -0.581,0.863 -0.584,1.416c-0.003,0.553 0.192,1.025 0.584,1.417c0.392,0.392 0.864,0.586 1.416,0.583m12,-0c0.556,-0 1.028,-0.195 1.417,-0.583c0.39,-0.388 0.584,-0.861 0.583,-1.417c-0.001,-0.557 -0.195,-1.029 -0.583,-1.416c-0.387,-0.388 -0.86,-0.583 -1.417,-0.584c-0.557,-0.002 -1.029,0.193 -1.416,0.584c-0.387,0.391 -0.581,0.863 -0.584,1.416c-0.003,0.553 0.192,1.025 0.584,1.417c0.392,0.392 0.864,0.586 1.416,0.583m-15.333,1.333l18.666,0l0,-6.667l-18.666,0l-0,6.667Z"
			style="fill-rule: nonzero"
		/>
		<path
			fill="currentColor"
			d="M23.267,25.167l0.666,-0c0.178,-0 0.334,0.066 0.467,0.2c0.133,0.133 0.2,0.289 0.2,0.466l0,1.334c0,0.177 -0.067,0.333 -0.2,0.466c-0.133,0.134 -0.289,0.2 -0.467,0.2l-0.666,0l-0,1.334c-0,0.377 -0.128,0.694 -0.384,0.95c-0.256,0.256 -0.573,0.384 -0.95,0.383l-12,-0c-0.377,-0 -0.694,-0.128 -0.95,-0.384c-0.256,-0.256 -0.384,-0.572 -0.383,-0.949l0,-5.334c0,-0.377 0.128,-0.694 0.384,-0.949c0.256,-0.255 0.572,-0.383 0.949,-0.384l12,-0c0.378,-0 0.695,0.128 0.95,0.384c0.255,0.256 0.383,0.572 0.384,0.949l-0,1.334Zm-12,-0l-0,2.666l-0,-2.666Zm1.54,-0l-0,2.666l7.793,0l0,-2.666l-7.793,-0Z"
			style="fill-rule: nonzero"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "VehicleMinSoc",
	mixins: [icon],
});
</script>
