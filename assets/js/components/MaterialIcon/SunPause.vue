<template>
	<svg :style="svgStyle" viewBox="0 0 48 48">
		>
		<path
			fill="currentColor"
			d="M14.216,26.071c-0.144,-0.681 -0.217,-1.375 -0.216,-2.071c-0,-5.514 4.486,-10 10,-10c5.514,0 10,4.486 10,10c0,0.71 -0.074,1.403 -0.216,2.071l-4.152,0c0.242,-0.662 0.366,-1.361 0.366,-2.065c0,-3.292 -2.706,-6.002 -5.998,-6.006c-3.292,0.004 -5.998,2.714 -5.998,6.006c-0,0.704 0.124,1.403 0.366,2.065l-4.152,0Zm7.784,-22.071l4,0l0,6l-4,0l0,-6Zm-18,18l6,0l0,4l-6,0l0,-4<PERSON>m34,0l6,0l0,4l-6,0l0,-4Zm-22.268,-16.32l3,5.196l-3.464,2l-3,-5.196l3.464,-2Zm24.589,6.589l2,3.466l-5.196,2.999l-2,-3.465l5.196,-3Zm-32.642,-0.002l5.197,2.999l-2,3.466l-5.197,-3l2,-3.465Zm24.59,-6.587l3.465,2.001l-3,5.195l-3.465,-2.001l3,-5.195Zm-10.225,35.581l-4.088,-0l0,-10.261l4.088,-0l0,10.261Zm8.088,-0l-4.088,-0l0,-10.261l4.088,-0l0,10.261Z"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "SunPause",
	mixins: [icon],
});
</script>
