<template>
	<svg :style="svgStyle" viewBox="0 0 32 32">
		<path
			d="M15.998,8.095c1.369,-0 2.669,0.219 3.902,0.657c1.232,0.438 2.349,1.04 3.352,1.807c0.365,0.273 0.553,0.634 0.562,1.081c0.01,0.448 -0.15,0.835 -0.48,1.163c-0.31,0.31 -0.693,0.47 -1.149,0.48c-0.456,0.009 -0.867,-0.114 -1.232,-0.37c-0.693,-0.475 -1.46,-0.849 -2.299,-1.123c-0.84,-0.273 -1.725,-0.41 -2.656,-0.41c-0.93,-0 -1.815,0.137 -2.655,0.41c-0.839,0.274 -1.606,0.648 -2.299,1.123c-0.365,0.255 -0.776,0.374 -1.232,0.356c-0.456,-0.019 -0.84,-0.183 -1.15,-0.493c-0.31,-0.329 -0.465,-0.716 -0.465,-1.163c-0,-0.447 0.182,-0.807 0.547,-1.082c1.004,-0.766 2.122,-1.364 3.354,-1.793c1.232,-0.43 2.532,-0.644 3.9,-0.643m0,-6.57c2.281,0 4.43,0.374 6.447,1.123c2.017,0.748 3.828,1.806 5.433,3.175c0.365,0.31 0.557,0.693 0.575,1.15c0.019,0.456 -0.137,0.848 -0.465,1.177c-0.31,0.31 -0.693,0.47 -1.15,0.479c-0.456,0.01 -0.867,-0.132 -1.232,-0.425c-1.313,-1.076 -2.787,-1.911 -4.42,-2.504c-1.633,-0.592 -3.362,-0.889 -5.188,-0.89c-1.825,-0.001 -3.554,0.296 -5.186,0.89c-1.633,0.595 -3.106,1.429 -4.422,2.504c-0.365,0.292 -0.775,0.434 -1.232,0.425c-0.456,-0.008 -0.839,-0.168 -1.149,-0.479c-0.329,-0.329 -0.484,-0.721 -0.466,-1.177c0.019,-0.457 0.21,-0.84 0.575,-1.15c1.606,-1.369 3.418,-2.427 5.434,-3.175c2.017,-0.749 4.166,-1.123 6.446,-1.123"
			fill="currentColor"
		/>
		<path
			d="M15.992,23.961c-1.368,0 -2.669,-0.219 -3.901,-0.657c-1.232,-0.437 -2.35,-1.04 -3.353,-1.806c-0.365,-0.274 -0.552,-0.635 -0.561,-1.082c-0.01,-0.447 0.15,-0.835 0.479,-1.163c0.31,-0.31 0.694,-0.47 1.15,-0.479c0.456,-0.01 0.867,0.113 1.232,0.37c0.693,0.474 1.46,0.848 2.299,1.122c0.84,0.274 1.725,0.411 2.655,0.411c0.931,-0 1.816,-0.137 2.656,-0.411c0.839,-0.274 1.605,-0.648 2.299,-1.122c0.365,-0.256 0.776,-0.374 1.232,-0.356c0.456,0.018 0.839,0.182 1.149,0.493c0.311,0.328 0.466,0.716 0.466,1.162c-0,0.447 -0.183,0.808 -0.548,1.082c-1.003,0.767 -2.121,1.365 -3.353,1.794c-1.233,0.429 -2.533,0.643 -3.901,0.642m0,6.57c-2.281,0 -4.43,-0.374 -6.447,-1.122c-2.017,-0.748 -3.828,-1.807 -5.433,-3.176c-0.365,-0.31 -0.556,-0.693 -0.575,-1.149c-0.018,-0.456 0.137,-0.849 0.466,-1.177c0.31,-0.311 0.693,-0.47 1.149,-0.48c0.457,-0.009 0.867,0.132 1.232,0.425c1.314,1.077 2.788,1.911 4.421,2.504c1.632,0.593 3.362,0.89 5.187,0.89c1.826,0.001 3.555,-0.296 5.187,-0.89c1.632,-0.594 3.106,-1.429 4.421,-2.504c0.365,-0.292 0.776,-0.434 1.232,-0.425c0.456,0.009 0.84,0.169 1.15,0.48c0.328,0.328 0.484,0.721 0.465,1.177c-0.018,0.456 -0.21,0.839 -0.575,1.149c-1.605,1.369 -3.417,2.428 -5.434,3.176c-2.017,0.748 -4.165,1.122 -6.446,1.122"
			fill="currentColor"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Eebus",
	mixins: [icon],
});
</script>
