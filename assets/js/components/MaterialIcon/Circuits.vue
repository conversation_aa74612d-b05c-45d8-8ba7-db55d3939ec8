<template>
	<svg :style="svgStyle" viewBox="0 0 24 24">
		<path
			fill="currentColor"
			d="M4 21q-1.25 0-2.125-.875T1 18t.875-2.125T4 15q.225 0 .438.038t.412.087L8.9 9.55q-.425-.525-.663-1.175T8 7q0-1.65 1.175-2.825T12 3t2.825 1.175T16 7q0 .725-.25 1.375t-.675 1.175l4.075 5.575q.2-.05.413-.088T20 15q1.25 0 2.125.875T23 18t-.875 2.125T20 21t-2.125-.875T17 18q0-.475.138-.913t.387-.787l-4.05-5.575q-.125.05-.237.075t-.238.075v4.3q.875.3 1.438 1.075T15 18q0 1.25-.875 2.125T12 21t-2.125-.875T9 18q0-.975.563-1.737T11 15.174v-4.3q-.125-.05-.238-.075t-.237-.075L6.475 16.3q.25.35.388.788T7 18q0 1.25-.875 2.125T4 21m0-2q.425 0 .713-.288T5 18t-.288-.712T4 17t-.712.288T3 18t.288.713T4 19m8 0q.425 0 .713-.288T13 18t-.288-.712T12 17t-.712.288T11 18t.288.713T12 19m8 0q.425 0 .713-.288T21 18t-.288-.712T20 17t-.712.288T19 18t.288.713T20 19M12 9q.825 0 1.413-.587T14 7t-.587-1.412T12 5t-1.412.588T10 7t.588 1.413T12 9"
		/>
	</svg>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import icon from "@/mixins/icon";

export default defineComponent({
	name: "Circuits",
	mixins: [icon],
});
</script>
