<template>
	<div class="container px-4">
		<TopHeader />
		
		<!-- Date Selector and Controls -->
		<div class="row mt-4">
			<div class="col-12">
				<div class="card">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h5 class="mb-0">Site Power Monitoring</h5>
						<div class="d-flex gap-2 align-items-center">
							<DateSelector 
								:selectedDate="selectedDate" 
								@update-date="updateSelectedDate"
							/>
							<button 
								class="btn btn-sm btn-outline-primary" 
								@click="refreshData" 
								:disabled="isRefreshing"
							>
								<span v-if="isRefreshing" class="spinner-border spinner-border-sm me-1"></span>
								Refresh
							</button>
						</div>
					</div>
					<div class="card-body">
						<div v-if="loading.sitepower" class="text-center py-3">
							<div class="spinner-border" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
						</div>
						<div v-else-if="error.sitepower" class="alert alert-danger">
							{{ error.sitepower }}
						</div>
						<div v-else>
							<PowerChart 
								:data="sitePowerData" 
								:dataType="'sitepower'" 
								:selectedDate="selectedDate"
							/>
							<div class="mt-3">
								<div class="row">
									<div class="col-md-3">
										<div class="text-center">
											<h6 class="text-muted mb-1">Total Records</h6>
											<span class="h5">{{ sitePowerData.length }}</span>
										</div>
									</div>
									<div class="col-md-3" v-if="latestRecord">
										<div class="text-center">
											<h6 class="text-muted mb-1">Latest Power</h6>
											<span class="h5">{{ formatPower(latestRecord.powerKW) }}</span>
										</div>
									</div>
									<div class="col-md-3" v-if="averagePower !== null">
										<div class="text-center">
											<h6 class="text-muted mb-1">Average Power</h6>
											<span class="h5">{{ formatPower(averagePower) }}</span>
										</div>
									</div>
									<div class="col-md-3">
										<div class="text-center">
											<h6 class="text-muted mb-1">Last Updated</h6>
											<small class="text-muted">{{ formatTimestamp(lastUpdate) }}</small>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- System Health Status -->
		<div class="row mt-4">
			<div class="col-md-6">
				<div class="card">
					<div class="card-header">
						<h6 class="mb-0">System Health</h6>
					</div>
					<div class="card-body">
						<div v-if="loading.health" class="text-center py-2">
							<div class="spinner-border spinner-border-sm" role="status"></div>
						</div>
						<div v-else-if="error.health" class="alert alert-danger py-2">
							{{ error.health }}
						</div>
						<div v-else-if="healthData">
							<div class="d-flex justify-content-between align-items-center">
								<span>Status:</span>
								<span :class="healthStatusClass">{{ healthStatus }}</span>
							</div>
							<div class="d-flex justify-content-between align-items-center mt-2" v-if="healthData.uptime">
								<span>Uptime:</span>
								<span>{{ formatUptime(healthData.uptime) }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<div class="card">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h6 class="mb-0">System Actions</h6>
					</div>
					<div class="card-body">
						<div class="d-grid gap-2">
							<button 
								class="btn btn-sm btn-outline-warning" 
								@click="clearCache" 
								:disabled="loading.clearCache"
							>
								<span v-if="loading.clearCache" class="spinner-border spinner-border-sm me-1"></span>
								Clear Cache
							</button>
							<button 
								class="btn btn-sm btn-outline-danger" 
								@click="cleanupOldRecords" 
								:disabled="loading.cleanup"
							>
								<span v-if="loading.cleanup" class="spinner-border spinner-border-sm me-1"></span>
								Cleanup Old Records
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Header from "../components/Top/Header.vue";
import DateSelector from "../components/Monitoring/DateSelector.vue";
import PowerChart from "../components/Monitoring/PowerChart.vue";
import api from "../api";

export default {
	components: {
		TopHeader: Header,
		DateSelector,
		PowerChart,
	},
	props: {
		selectedDate: {
			type: String,
			default: () => new Date().toISOString().split('T')[0]
		}
	},
	data() {
		return {
			healthData: null,
			sitePowerData: [],
			lastUpdate: new Date(),
			loading: {
				health: false,
				sitepower: false,
				clearCache: false,
				cleanup: false,
			},
			error: {
				health: null,
				sitepower: null,
			},
		};
	},
	computed: {
		healthStatus() {
			if (!this.healthData) return 'Unknown';
			return this.healthData.healthy !== false ? 'Healthy' : 'Unhealthy';
		},
		healthStatusClass() {
			const status = this.healthStatus;
			return {
				'text-success': status === 'Healthy',
				'text-danger': status === 'Unhealthy',
				'text-warning': status === 'Unknown',
			};
		},
		latestRecord() {
			if (!this.sitePowerData || this.sitePowerData.length === 0) return null;
			return this.sitePowerData[this.sitePowerData.length - 1];
		},
		averagePower() {
			if (!this.sitePowerData || this.sitePowerData.length === 0) return null;
			const total = this.sitePowerData.reduce((sum, record) => sum + (record.powerKW || 0), 0);
			return total / this.sitePowerData.length;
		},
		isRefreshing() {
			return this.loading.health || this.loading.sitepower;
		},
	},
	methods: {
		formatUptime(uptime) {
			if (!uptime) return 'N/A';
			
			const seconds = Math.floor(uptime);
			const days = Math.floor(seconds / 86400);
			const hours = Math.floor((seconds % 86400) / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			
			if (days > 0) {
				return `${days}d ${hours}h ${minutes}m`;
			} else if (hours > 0) {
				return `${hours}h ${minutes}m`;
			} else {
				return `${minutes}m`;
			}
		},
		formatTimestamp(timestamp) {
			return timestamp.toLocaleString();
		},
		formatPower(power) {
			if (power === null || power === undefined) return 'N/A';
			return `${power.toFixed(2)} kW`;
		},
		updateSelectedDate(date) {
			this.$emit('update-date', date);
			this.loadSitePowerData();
		},
		async loadHealthData() {
			this.loading.health = true;
			this.error.health = null;
			
			try {
				const response = await api.get('/health');
				this.healthData = response.data;
			} catch (err) {
				this.error.health = err.message || 'Failed to load health data';
				console.error('Error loading health data:', err);
			} finally {
				this.loading.health = false;
			}
		},
		async loadSitePowerData() {
			this.loading.sitepower = true;
			this.error.sitepower = null;
			
			try {
				const params = {
					site: 'evcc' // 默认站点名称，可以从配置中获取
				};
				
				// 如果选择了特定日期，设置时间范围
				if (this.selectedDate) {
					const date = new Date(this.selectedDate);
					const from = new Date(date.setHours(0, 0, 0, 0));
					const to = new Date(date.setHours(23, 59, 59, 999));
					params['from'] = Math.floor(from.getTime() / 1000);
					params['to'] = Math.floor(to.getTime() / 1000);
				}
				
				const response = await api.get('/sitepower/records', { params });
				this.sitePowerData = response.data.records || [];
				this.lastUpdate = new Date();
			} catch (err) {
				this.error.sitepower = err.message || 'Failed to load site power data';
				console.error('Error loading site power data:', err);
			} finally {
				this.loading.sitepower = false;
			}
		},
		async refreshData() {
			await Promise.all([
				this.loadHealthData(),
				this.loadSitePowerData(),
			]);
		},
		async clearCache() {
			this.loading.clearCache = true;
			
			try {
				await api.delete('/system/cache');
				// Refresh data after clearing cache
				await this.refreshData();
			} catch (err) {
				console.error('Error clearing cache:', err);
				alert('Failed to clear cache: ' + (err.message || 'Unknown error'));
			} finally {
				this.loading.clearCache = false;
			}
		},
		async cleanupOldRecords() {
			this.loading.cleanup = true;
			
			try {
				// 清理30天前的数据
				await api.post('/sitepower/cleanup', {
					daysToKeep: 30
				});
				// Refresh data after cleanup
				await this.refreshData();
				alert('Old records cleaned up successfully!');
			} catch (err) {
				console.error('Error cleaning up old records:', err);
				alert('Failed to cleanup old records: ' + (err.message || 'Unknown error'));
			} finally {
				this.loading.cleanup = false;
			}
		},
	},
	mounted() {
		this.refreshData();
	},
};
</script>

<style scoped>
/* Add any specific styles for monitoring page here */
</style>