<template>
	<div class="container px-4">
		<TopHeader />

		<!-- Main Monitoring Card -->
		<div class="row mt-4">
			<div class="col-12">
				<div class="card">
					<!-- Tab Navigation -->
					<div class="card-header p-0">
						<div class="d-flex justify-content-between align-items-center p-3 pb-0">
							<div class="nav nav-tabs border-0" role="tablist">
								<button
									class="nav-link px-3 py-2 border-0 rounded-top"
									:class="{ active: activeTab === 'sitepower' }"
									@click="setActiveTab('sitepower')"
									type="button"
								>
									Site Power
								</button>
								<button
									class="nav-link px-3 py-2 border-0 rounded-top"
									:class="{ active: activeTab === 'battery' }"
									@click="setActiveTab('battery')"
									type="button"
								>
									Battery Charging
								</button>
							</div>
							<div class="d-flex gap-2 align-items-center">
								<DateSelector
									:selectedDate="selectedDate"
									@update-date="updateSelectedDate"
								/>
							</div>
						</div>
					</div>

					<!-- Tab Content -->
					<div class="card-body">
						<!-- Site Power Tab -->
						<div v-if="activeTab === 'sitepower'">
							<div class="d-flex justify-content-between align-items-center mb-3">
								<h5 class="mb-0">Site Power Data <span class="text-muted fs-6">{{ formattedSelectedDate }}</span></h5>
							</div>

							<div v-if="loading.sitepower" class="text-center py-5">
								<div class="spinner-border" role="status">
									<span class="visually-hidden">Loading...</span>
								</div>
							</div>
							<div v-else-if="error.sitepower" class="alert alert-danger">
								{{ error.sitepower }}
							</div>
							<div v-else>
								<PowerChart
									:data="chartData"
									:dataType="'sitepower'"
									:selectedDate="selectedDate"
								/>

								<!-- Statistics Section -->
								<div class="mt-4">
									<h6 class="mb-3">Statistics</h6>
									<div class="row text-center">
										<div class="col-md-3">
											<div class="stat-item">
												<div class="stat-label">MAXIMUM</div>
												<div class="stat-value">{{ formatPower(maxPower) }}</div>
											</div>
										</div>
										<div class="col-md-3">
											<div class="stat-item">
												<div class="stat-label">MINIMUM</div>
												<div class="stat-value">{{ formatPower(minPower) }}</div>
											</div>
										</div>
										<div class="col-md-3">
											<div class="stat-item">
												<div class="stat-label">AVERAGE</div>
												<div class="stat-value">{{ formatPower(averagePower) }}</div>
											</div>
										</div>
										<div class="col-md-3">
											<div class="stat-item">
												<div class="stat-label">DATA POINTS</div>
												<div class="stat-value">{{ sitePowerData.length }}</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Battery Charging Tab -->
						<div v-if="activeTab === 'battery'">
							<div class="d-flex justify-content-between align-items-center mb-3">
								<h5 class="mb-0">Battery Charging Data <span class="text-muted fs-6">{{ formattedSelectedDate }}</span></h5>
							</div>
							<div class="text-center py-5 text-muted">
								<p>Battery charging data will be available soon.</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

	</div>
</template>

<script lang="ts">
import Header from "../components/Top/Header.vue";
import DateSelector from "../components/Monitoring/DateSelector.vue";
import PowerChart from "../components/Monitoring/PowerChart.vue";
import api from "../api";

export default {
	components: {
		TopHeader: Header,
		DateSelector,
		PowerChart,
	},
	props: {
		selectedDate: {
			type: String,
			default: () => new Date().toISOString().split('T')[0]
		}
	},
	data() {
		return {
			activeTab: 'sitepower',
			sitePowerData: [],
			lastUpdate: new Date(),
			loading: {
				sitepower: false,
			},
			error: {
				sitepower: null,
			},
		};
	},
	computed: {
		formattedSelectedDate() {
			const date = new Date(this.selectedDate);
			return date.toLocaleDateString('en-US', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit'
			});
		},
		chartData() {
			return this.sitePowerData.map(record => ({
				time: record.createdAt,
				power: record.powerKW
			}));
		},
		maxPower() {
			if (!this.sitePowerData || this.sitePowerData.length === 0) return null;
			return Math.max(...this.sitePowerData.map(record => record.powerKW));
		},
		minPower() {
			if (!this.sitePowerData || this.sitePowerData.length === 0) return null;
			return Math.min(...this.sitePowerData.map(record => record.powerKW));
		},
		averagePower() {
			if (!this.sitePowerData || this.sitePowerData.length === 0) return null;
			const total = this.sitePowerData.reduce((sum, record) => sum + (record.powerKW || 0), 0);
			return total / this.sitePowerData.length;
		},
		isRefreshing() {
			return this.loading.sitepower;
		},
	},
	methods: {
		formatPower(power) {
			if (power === null || power === undefined) return 'N/A';
			return `${power.toFixed(2)} kW`;
		},
		setActiveTab(tab) {
			this.activeTab = tab;
		},
		updateSelectedDate(date) {
			this.$emit('update-date', date);
			this.loadSitePowerData();
		},
		async loadSitePowerData() {
			this.loading.sitepower = true;
			this.error.sitepower = null;

			try {
				const params = {
					site: 'Zuhause' // Use the site name from the API response
				};

				// Set time range for the selected date
				if (this.selectedDate) {
					const date = new Date(this.selectedDate);
					const from = new Date(date);
					from.setHours(0, 0, 0, 0);
					const to = new Date(date);
					to.setHours(23, 59, 59, 999);

					// Convert to ISO string format as expected by the API
					params['from'] = from.toISOString();
					params['to'] = to.toISOString();
				}

				const response = await api.get('/sitepower/records', { params });
				this.sitePowerData = response.data.records || [];
				this.lastUpdate = new Date();
			} catch (err) {
				this.error.sitepower = err.message || 'Failed to load site power data';
				console.error('Error loading site power data:', err);
			} finally {
				this.loading.sitepower = false;
			}
		},
		async refreshData() {
			await this.loadSitePowerData();
		},
	},
	mounted() {
		this.loadSitePowerData();
	},
};
</script>

<style scoped>
.nav-tabs .nav-link {
	background-color: var(--evcc-background);
	color: var(--evcc-default-text);
	border: 1px solid var(--evcc-box-border);
	border-bottom: none;
	margin-right: 2px;
}

.nav-tabs .nav-link.active {
	background-color: var(--evcc-primary);
	color: white;
	border-color: var(--evcc-primary);
}

.nav-tabs .nav-link:hover:not(.active) {
	background-color: var(--evcc-gray-light);
	border-color: var(--evcc-box-border);
}

.stat-item {
	padding: 1rem;
	border: 1px solid var(--evcc-box-border);
	border-radius: 8px;
	background-color: var(--evcc-background);
	margin-bottom: 1rem;
}

.stat-label {
	font-size: 0.75rem;
	font-weight: 600;
	color: var(--evcc-gray);
	text-transform: uppercase;
	letter-spacing: 0.5px;
	margin-bottom: 0.5rem;
}

.stat-value {
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--evcc-default-text);
}

@media (max-width: 768px) {
	.stat-item {
		margin-bottom: 0.5rem;
	}

	.stat-value {
		font-size: 1.25rem;
	}
}
</style>