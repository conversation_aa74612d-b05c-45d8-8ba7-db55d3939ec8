{"name": "controlbox-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"qrcode.vue": "^3.6.0", "vue": "^3.5.13", "vue-facing-decorator": "^3.0.4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.6.2", "vite": "^6.0.5", "vue-tsc": "^2.2.0", "vue3-select-component": "^0.7.0"}}