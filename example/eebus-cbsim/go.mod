module github.com/meisel2000/eebus-cbsim

go 1.24.0

require (
	github.com/enbility/eebus-go v0.0.0-unpublished
	github.com/enbility/ship-go v0.0.0-20241118145930-d68708c5f1c0
	github.com/enbility/spine-go v0.0.0-20241209160856-1aed917e83e7
	github.com/gorilla/websocket v1.5.3
)

require (
	github.com/ahmetb/go-linq/v3 v3.2.0 // indirect
	github.com/enbility/go-avahi v0.0.0-20240909195612-d5de6b280d7a // indirect
	github.com/enbility/zeroconf/v2 v2.0.0-20240920094356-be1cae74fda6 // indirect
	github.com/godbus/dbus/v5 v5.1.0 // indirect
	github.com/golanguzb70/lrucache v1.2.0 // indirect
	github.com/miekg/dns v1.1.62 // indirect
	github.com/rickb777/date v1.21.1 // indirect
	github.com/rickb777/plural v1.4.2 // indirect
	gitlab.com/c0b/go-ordered-json v0.0.0-20201030195603-febf46534d5a // indirect
	golang.org/x/mod v0.21.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/tools v0.25.0 // indirect
)

replace github.com/enbility/eebus-go v0.0.0-unpublished => github.com/meisel2000/eebus-go v0.0.0-20250207202336-05ec98d2aad4
