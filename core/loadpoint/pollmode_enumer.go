// Code generated by "enumer -type PollMode -trimprefix Poll -transform=lower -text"; DO NOT EDIT.

package loadpoint

import (
	"fmt"
	"strings"
)

const _PollModeName = "chargingconnectedalways"

var _PollModeIndex = [...]uint8{0, 8, 17, 23}

const _PollModeLowerName = "chargingconnectedalways"

func (i PollMode) String() string {
	if i < 0 || i >= PollMode(len(_PollModeIndex)-1) {
		return fmt.Sprintf("PollMode(%d)", i)
	}
	return _PollModeName[_PollModeIndex[i]:_PollModeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _PollModeNoOp() {
	var x [1]struct{}
	_ = x[PollCharging-(0)]
	_ = x[PollConnected-(1)]
	_ = x[PollAlways-(2)]
}

var _PollModeValues = []PollMode{PollCharging, PollConnected, PollAlways}

var _PollModeNameToValueMap = map[string]PollMode{
	_PollModeName[0:8]:        PollCharging,
	_PollModeLowerName[0:8]:   PollCharging,
	_PollModeName[8:17]:       PollConnected,
	_PollModeLowerName[8:17]:  PollConnected,
	_PollModeName[17:23]:      PollAlways,
	_PollModeLowerName[17:23]: PollAlways,
}

var _PollModeNames = []string{
	_PollModeName[0:8],
	_PollModeName[8:17],
	_PollModeName[17:23],
}

// PollModeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func PollModeString(s string) (PollMode, error) {
	if val, ok := _PollModeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _PollModeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to PollMode values", s)
}

// PollModeValues returns all values of the enum
func PollModeValues() []PollMode {
	return _PollModeValues
}

// PollModeStrings returns a slice of all String values of the enum
func PollModeStrings() []string {
	strs := make([]string, len(_PollModeNames))
	copy(strs, _PollModeNames)
	return strs
}

// IsAPollMode returns "true" if the value is listed in the enum definition. "false" otherwise
func (i PollMode) IsAPollMode() bool {
	for _, v := range _PollModeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalText implements the encoding.TextMarshaler interface for PollMode
func (i PollMode) MarshalText() ([]byte, error) {
	return []byte(i.String()), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for PollMode
func (i *PollMode) UnmarshalText(text []byte) error {
	var err error
	*i, err = PollModeString(string(text))
	return err
}
