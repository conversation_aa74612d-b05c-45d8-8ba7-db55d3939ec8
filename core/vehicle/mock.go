// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/evcc-io/evcc/core/vehicle (interfaces: API)
//
// Generated by this command:
//
//	mockgen -package vehicle -destination mock.go -mock_names API=MockAPI github.com/evcc-io/evcc/core/vehicle API
//

// Package vehicle is a generated GoMock package.
package vehicle

import (
	reflect "reflect"
	time "time"

	api "github.com/evcc-io/evcc/api"
	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// GetLimitSoc mocks base method.
func (m *MockAPI) GetLimitSoc() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitSoc")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetLimitSoc indicates an expected call of GetLimitSoc.
func (mr *MockAPIMockRecorder) GetLimitSoc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitSoc", reflect.TypeOf((*MockAPI)(nil).GetLimitSoc))
}

// GetMinSoc mocks base method.
func (m *MockAPI) GetMinSoc() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinSoc")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetMinSoc indicates an expected call of GetMinSoc.
func (mr *MockAPIMockRecorder) GetMinSoc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinSoc", reflect.TypeOf((*MockAPI)(nil).GetMinSoc))
}

// GetPlanSoc mocks base method.
func (m *MockAPI) GetPlanSoc() (time.Time, time.Duration, int) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlanSoc")
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(int)
	return ret0, ret1, ret2
}

// GetPlanSoc indicates an expected call of GetPlanSoc.
func (mr *MockAPIMockRecorder) GetPlanSoc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlanSoc", reflect.TypeOf((*MockAPI)(nil).GetPlanSoc))
}

// GetRepeatingPlans mocks base method.
func (m *MockAPI) GetRepeatingPlans() []api.RepeatingPlanStruct {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepeatingPlans")
	ret0, _ := ret[0].([]api.RepeatingPlanStruct)
	return ret0
}

// GetRepeatingPlans indicates an expected call of GetRepeatingPlans.
func (mr *MockAPIMockRecorder) GetRepeatingPlans() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepeatingPlans", reflect.TypeOf((*MockAPI)(nil).GetRepeatingPlans))
}

// Instance mocks base method.
func (m *MockAPI) Instance() api.Vehicle {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Instance")
	ret0, _ := ret[0].(api.Vehicle)
	return ret0
}

// Instance indicates an expected call of Instance.
func (mr *MockAPIMockRecorder) Instance() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Instance", reflect.TypeOf((*MockAPI)(nil).Instance))
}

// Name mocks base method.
func (m *MockAPI) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockAPIMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockAPI)(nil).Name))
}

// SetLimitSoc mocks base method.
func (m *MockAPI) SetLimitSoc(soc int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLimitSoc", soc)
}

// SetLimitSoc indicates an expected call of SetLimitSoc.
func (mr *MockAPIMockRecorder) SetLimitSoc(soc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLimitSoc", reflect.TypeOf((*MockAPI)(nil).SetLimitSoc), soc)
}

// SetMinSoc mocks base method.
func (m *MockAPI) SetMinSoc(soc int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetMinSoc", soc)
}

// SetMinSoc indicates an expected call of SetMinSoc.
func (mr *MockAPIMockRecorder) SetMinSoc(soc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMinSoc", reflect.TypeOf((*MockAPI)(nil).SetMinSoc), soc)
}

// SetPlanSoc mocks base method.
func (m *MockAPI) SetPlanSoc(arg0 time.Time, arg1 time.Duration, arg2 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPlanSoc", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPlanSoc indicates an expected call of SetPlanSoc.
func (mr *MockAPIMockRecorder) SetPlanSoc(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPlanSoc", reflect.TypeOf((*MockAPI)(nil).SetPlanSoc), arg0, arg1, arg2)
}

// SetRepeatingPlans mocks base method.
func (m *MockAPI) SetRepeatingPlans(arg0 []api.RepeatingPlanStruct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRepeatingPlans", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRepeatingPlans indicates an expected call of SetRepeatingPlans.
func (mr *MockAPIMockRecorder) SetRepeatingPlans(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRepeatingPlans", reflect.TypeOf((*MockAPI)(nil).SetRepeatingPlans), arg0)
}
