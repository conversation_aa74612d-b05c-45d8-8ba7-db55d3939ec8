package prioritizer

import (
	"testing"

	"github.com/evcc-io/evcc/core/loadpoint"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestPrioritzer(t *testing.T) {
	ctrl := gomock.NewController(t)

	p := New(nil)

	lo := loadpoint.NewMockAPI(ctrl)
	lo.EXPECT().GetTitle().AnyTimes()
	lo.EXPECT().GetPriority().Return(0).AnyTimes()       // prio 0
	lo.EXPECT().EffectivePriority().Return(0).AnyTimes() // prio 0

	hi := loadpoint.NewMockAPI(ctrl)
	hi.EXPECT().GetTitle().AnyTimes()
	hi.EXPECT().GetPriority().Return(1).AnyTimes()       // prio 1
	hi.EXPECT().EffectivePriority().Return(1).AnyTimes() // prio 1

	// no additional power available
	lo.EXPECT().GetChargePowerFlexibility(nil).Return(300.0)
	p.UpdateChargePowerFlexibility(lo, nil)
	assert.Equal(t, 0.0, p.GetChargePowerFlexibility(lo))

	// additional power available
	hi.EXPECT().GetChargePowerFlexibility(nil).Return(1e3)
	p.UpdateChargePowerFlexibility(hi, nil)
	assert.Equal(t, 300.0, p.GetChargePowerFlexibility(hi))

	// additional power removed
	lo.EXPECT().GetChargePowerFlexibility(nil).Return(0.0)
	p.UpdateChargePowerFlexibility(lo, nil)
	assert.Equal(t, 0.0, p.GetChargePowerFlexibility(hi))
}
