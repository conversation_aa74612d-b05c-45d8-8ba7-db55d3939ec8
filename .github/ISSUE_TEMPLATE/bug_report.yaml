name: Bug report
description: Create a report to help us improve

body:
  - type: markdown
    attributes:
      value: |
        This issue form is for reporting issues only! If you want to start a discussion or ask a question, please use the [discussion forum][df].

        **NOTE** Due to amount of issues raised we will close incomplete issues (unclear description what the issue actually is, missing log file, unsupported platform) without further comments.

        [df]: https://github.com/evcc-io/evcc/discussions
  - type: textarea
    validations:
      required: true
    attributes:
      label: Describe the bug
      description: >-
        A clear and concise description of what the bug is.

  - type: textarea
    validations:
      required: true
    attributes:
      value: |
        1.
        2.
        3.
        ...
      label: Steps to reproduce
      description: >-
        Steps to reproduce the behavior.

  - type: textarea
    validations:
      required: true
    attributes:
      label: Configuration details
      render: yaml
      description: >
        Show evcc configuration file <code>evcc.yaml</code> and if using Config UI the output of `evcc config`

        Please make sure your report does NOT contain **passwords**, **sponsor token** or other **credentials**!

        To quickly dump a redacted configuration without secrets, you can use the `evcc dump --cfg` command.

  - type: textarea
    validations:
      required: true
    attributes:
      label: Log details
      render: text
      description: >
        Show evcc log output of the issue, see https://docs.evcc.io/en/docs/faq#how-do-i-create-a-log-file-for-error-analysis for instructions.
        In case of issues with physical devices like chargers, meters or vehicles, make sure that the log file has level `trace` enabled for the device.

  - type: dropdown
    validations:
      required: true
    attributes:
      label: What type of operating system or environment does evcc run on?
      description: >
        **NOTE** if you're using HomeAssistant or Docker we ask you to reproduce the problem on plain Linux or Windows first.
      options:
        - Linux
        - Windows
        - Docker container
        - HomeAssistant Add-on
        - macOS
        - other

  - type: checkboxes
    id: external
    attributes:
      label: External automation
      description: Make sure the observed issue is caused by evcc and not by external automation
      options:
        - label: I have made sure that no external automation like HomeAssistant or Node-RED is active or accessing any of the mentioned devices when this issue occurs.
          required: true

  - type: checkboxes
    id: nightly
    attributes:
      label: Nightly build
      description: Check if the issue has not already been fixed in the latest nightly build
      options:
        - label: I have verified that the issue is reproducible with the latest nightly build
          required: true

  - type: input
    attributes:
      label: Version
      description: >
        Show output of <code>evcc -v</code>
