name: Nightly Build

on:
  schedule: # runs on the default branch: master
    - cron: "0 2 * * *" # run at 2 AM UTC
  workflow_dispatch:

jobs:
  check_date:
    runs-on: depot-ubuntu-24.04-arm
    name: Check latest commit
    outputs:
      should_run: ${{ steps.should_run.outputs.should_run }}
    steps:
      - uses: actions/checkout@v4
      - name: print latest_commit
        run: echo ${{ github.sha }}

      - id: should_run
        continue-on-error: true
        name: check latest commit is less than a day
        if: ${{ github.event_name == 'schedule' }}
        run: test -z $(git rev-list  --after="24 hours" ${{ github.sha }}) && echo "should_run=false" >> $GITHUB_OUTPUT

  call-build-workflow:
    name: Call Build
    needs: check_date
    if: |
      ${{ needs.check_date.outputs.should_run != 'false' }}
      && startsWith(github.ref, 'refs/heads/master')
      && ! contains(github.head_ref, 'refs/heads/chore/')
    uses: evcc-io/evcc/.github/workflows/default.yml@master

  docker:
    name: Publish Docker :nightly
    needs:
      - call-build-workflow
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4
        with:
          ref: refs/heads/master # force master
          fetch-depth: 0

      - name: Get dist from cache
        uses: actions/cache/restore@v4
        id: cache-dist
        with:
          path: dist
          key: ${{ runner.os }}-${{ github.sha }}-dist

      - name: Login
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Setup Buildx
        uses: docker/setup-buildx-action@v3

      - name: Define tags
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: evcc/evcc
          tags: |
            type=raw,value=nightly
            type=raw,value=nightly.{{date 'YYYYMMDD'}}-{{sha}}

      - name: Publish
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64,linux/arm/v6
          push: true
          tags: ${{ steps.meta.outputs.tags }}

      - name: Delete old nightly.* tags
        run: |
          old_tags=$(curl -s "https://hub.docker.com/v2/repositories/evcc/evcc/tags/?page_size=100" | jq -r '.results | map(select(.name | startswith("nightly."))) | sort_by(.last_updated) | reverse | .[1:] | .[].name')
          for tag in $old_tags; do
            echo "Deleting tag: $tag"
            curl -s -H "Authorization: Bearer ${{ secrets.DOCKER_PASS }}" -X DELETE "https://hub.docker.com/v2/repositories/evcc/evcc/tags/$tag/"
          done

  hassio:
    name: Hassio Addon :nightly
    needs:
      - docker
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - name: Checkout
        uses: actions/checkout@master
        with:
          repository: evcc-io/hassio-addon
          token: ${{ secrets.GH_TOKEN }}
          path: ./hassio

      - name: Update version
        run: |
          current_date=$(date +%Y%m%d)
          short_sha=$(echo "${{ github.sha }}" | cut -c 1-7)
          sed -i -e "s/version:.*/version: nightly.${current_date}-${short_sha}/" ./hassio/evcc-nightly/config.yaml

      - name: Push
        run: |
          cd ./hassio
          git add .
          git config user.name github-actions
          git config user.email <EMAIL>
          git commit -am "Mirror evcc nightly release"
          git push

  apt:
    name: Publish APT nightly
    needs:
      - call-build-workflow
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
        id: go

      - name: Patch ASN1
        run: make patch-asn1-sudo

      - name: Get dist from cache
        uses: actions/cache/restore@v4
        id: cache-dist
        with:
          path: dist
          key: ${{ runner.os }}-${{ github.sha }}-dist

      - name: Create nightly build
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: --snapshot -f .goreleaser-nightly.yml --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install Cloudsmith CLI
        run: pip install --upgrade cloudsmith-cli

      - name: Publish .deb to Cloudsmith
        env:
          CLOUDSMITH_API_KEY: ${{ secrets.CLOUDSMITH_API_KEY }}
        run: make apt-nightly
