name: Default

on:
  push:
    branches:
      - master
  pull_request:
  workflow_call:

jobs:
  clean:
    name: Clean
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
          cache: false # avoid cache thrashing
        id: go

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-clean-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-clean-
            ${{ runner.os }}-go-

      - name: Install tools
        run: make install

      - name: Assets
        run: make assets

      - name: Docs
        run: make docs

      - name: Porcelain
        run: make porcelain

  build:
    name: Build
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
          cache: false
        id: go

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-build-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-build-
            ${{ runner.os }}-go-

      - uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"

      - run: mkdir dist && touch dist/empty

      - name: Build
        run: make build

  test:
    name: Test
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
          cache: false
        id: go

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-test-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-test-
            ${{ runner.os }}-go-

      - name: Test
        run: mkdir dist && touch dist/empty && make test

  lint:
    name: Lint
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
          cache: false # avoid cache thrashing
        id: go

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-lint-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-lint-
            ${{ runner.os }}-go-

      - run: mkdir dist && touch dist/empty

      - name: Lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: latest
          args: --timeout 5m

  ui:
    name: UI
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"

      - name: Install
        run: make install-ui

      - name: Lint
        run: make lint-ui

      - name: Test
        run: make test-ui

      - name: Build UI
        run: make ui

      - name: Cache dist
        uses: actions/cache/save@v4
        id: cache-dist
        with:
          path: dist
          key: ${{ runner.os }}-${{ github.sha }}-dist

      - name: Porcelain
        run: |
          test -z "$(git status --porcelain)" || (git status; git diff; false)

  integration:
    name: Integration
    runs-on: depot-ubuntu-24.04-arm-16

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
          cache: false
        id: go

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-integration-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-integration-
            ${{ runner.os }}-go-

      - uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"

      - name: Build UI
        run: make install-ui ui

      - name: Build Go
        run: make build

      - name: Get Playwright version
        id: playwright-version
        run: echo "PLAYWRIGHT_VERSION=$(node -e "console.log(require('./package-lock.json').packages['node_modules/@playwright/test'].version)")" >> $GITHUB_ENV

      - name: Cache Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}

      - name: Install Playwright (browsers + deps)
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: npx playwright install --with-deps chromium

      - name: Install Playwright (deps only)
        if: steps.playwright-cache.outputs.cache-hit == 'true'
        run: npx playwright install-deps chromium

      - name: Run tests
        run: npx playwright test
        timeout-minutes: 20
        env:
          TZ: Europe/Berlin

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 14
