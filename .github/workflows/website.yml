name: Deploy data to website

on:
  release:
    types: [created]
  workflow_dispatch:

jobs:
  brandupdate:
    name: Deploy data to website
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
        id: go

      - name: Build docs
        run: make install docs

      - name: Remove .gitignore to allow brands.json to be committed
        run: rm templates/evcc.io/.gitignore

      - name: Deploy to evcc.io repo
        uses: peaceiris/actions-gh-pages@v4
        with:
          personal_token: ${{ secrets.GH_TOKEN }}
          publish_dir: ./templates/evcc.io/
          external_repository: evcc-io/evcc.io
          publish_branch: main
          destination_dir: data
          allow_empty_commit: false
          commit_message: Brand data update
        if: success()
