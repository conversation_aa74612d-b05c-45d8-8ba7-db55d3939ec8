name: Release

on:
  push:
    tags:
      - "*"

jobs:
  call-build-workflow:
    if: startsWith(github.ref, 'refs/tags')
    uses: evcc-io/evcc/.github/workflows/default.yml@master

  docker:
    name: Publish Docker :release
    needs:
      - call-build-workflow
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: <PERSON><PERSON>
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Setup Buildx
        uses: docker/setup-buildx-action@v3

      - name: Meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            evcc/evcc

      - name: Publish
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64,linux/arm/v6
          push: true
          build-args: |
            RELEASE=1
          tags: ${{ steps.meta.outputs.tags }}

  apt:
    name: Gith<PERSON> & APT
    needs:
      - call-build-workflow
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
        id: go

      - name: Patch ASN1
        run: make patch-asn1-sudo

      - name: Get dist from cache
        uses: actions/cache/restore@v4
        id: cache-dist
        with:
          path: dist
          key: ${{ runner.os }}-${{ github.sha }}-dist

      # gokrazy image
      # - name: Prepare Image
      #   run: |
      #     make prepare-image
      #     sed -i -e 's#-ld.*$#& -X github.com/evcc-io/evcc/server/updater.Password=${{ secrets.IMAGE_PASS }}#' buildflags/github.com/evcc-io/evcc/buildflags.txt
      #     mkdir /home/<USER>/.config/gokrazy
      #     echo ${{ secrets.IMAGE_PASS }}> /home/<USER>/.config/gokrazy/http-password.txt

      # - name: Build Image
      #   run: make image

      # - name: Build Root Filesystem
      #   run: make image-rootfs

      - name: Create Github Release
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          # use GH_TOKEN for access to evcc-io/homebrew-tap
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}

      - uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install Cloudsmith CLI
        run: pip install --upgrade cloudsmith-cli

      - name: Publish .deb to Cloudsmith
        env:
          CLOUDSMITH_API_KEY: ${{ secrets.CLOUDSMITH_API_KEY }}
        run: make apt-release

  demo:
    name: Demo
    needs:
      - docker
    runs-on: depot-ubuntu-24.04-arm
    env:
      FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: flyctl deploy --local-only --config packaging/fly.toml

  hassio:
    name: Hassio Addon
    needs:
      - docker
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - name: Checkout
        uses: actions/checkout@master
        with:
          repository: evcc-io/hassio-addon
          token: ${{ secrets.GH_TOKEN }}
          path: ./hassio

      - name: Update version
        run: |
          sed -i -e s#version.*#version\:\ $(echo ${{ github.ref }} | sed -e s#refs/tags/##)# ./hassio/evcc/config.yaml

      - name: Push
        run: |
          cd ./hassio
          git add .
          git config user.name github-actions
          git config user.email <EMAIL>
          git commit -am "Mirror evcc release"
          git push
