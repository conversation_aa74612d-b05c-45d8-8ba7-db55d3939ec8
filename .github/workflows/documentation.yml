name: Deploy updated templates

on:
  schedule:
    - cron: "0 2 * * *" # same time as nightly is triggered
  release:
    types: [created]
  workflow_dispatch:

jobs:
  docupdate:
    name: Deploy updated templates
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"
        id: go

      - name: Build docs
        run: make install docs

      - name: Deploy to docs repo
        uses: peaceiris/actions-gh-pages@v4
        with:
          personal_token: ${{ secrets.GH_TOKEN }}
          publish_dir: ./templates/docs
          external_repository: evcc-io/docs
          publish_branch: main
          destination_dir: ${{ github.event_name == 'release' && 'templates/release' || github.event_name == 'schedule' && 'templates/nightly' || 'templates/unknown_trigger' }}
          allow_empty_commit: false
          commit_message: ${{ github.event_name == 'release' && 'Templates update for release' || github.event_name == 'schedule' && 'Templates update for nightly' || 'Templates update unknown trigger' }}
        if: success()
