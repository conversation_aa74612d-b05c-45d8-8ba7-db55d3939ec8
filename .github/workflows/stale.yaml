name: "Stale issue handler"

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"
  issue_comment:
    types: [created]

jobs:
  stale:
    runs-on: depot-ubuntu-24.04-arm
    steps:
      - uses: actions/stale@v9
        id: stale
        with:
          days-before-stale: 7
          days-before-close: 5
          exempt-issue-labels: "pinned,security,backlog,bug"
          exempt-pr-labels: "pinned,security,backlog,bug"
