name: <PERSON> Issue Triage

on:
  issues:
    types: [opened]

jobs:
  triage-issue:
    runs-on: depot-ubuntu-24.04-arm
    timeout-minutes: 10
    permissions:
      contents: read
      issues: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run Claude Code
        uses: anthropics/claude-code-base-action@beta
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          mcp_config: |
            {
              "mcpServers": {
                "github": {
                  "command": "npx",
                  "args": ["-y", "@modelcontextprotocol/server-github"],
                  "env": {
                  "GITHUB_PERSONAL_ACCESS_TOKEN": "${{ secrets.GITHUB_TOKEN }}"
                  }
                }
              }
            }
          prompt: |
            You're an issue triage assistant for GitHub issues. Your task is to analyze the issue and select appropriate labels from the provided list.

            IMPORTANT: Don't post any comments or messages to the issue. Your only action should be to apply labels.

            Issue Information:
            - REPO: ${{ github.repository }}
            - ISSUE_NUMBER: ${{ github.event.issue.number }}

            TASK OVERVIEW:

            1. First, fetch the list of labels available in this repository by running: `gh label list`. Run exactly this command with nothing else.

            2. Next, use the GitHub tools to get context about the issue:
                - You have access to these tools:
                - mcp__github__get_issue: Use this to retrieve the current issue's details including title, description, and existing labels
                - mcp__github__get_issue_comments: Use this to read any discussion or additional context provided in the comments
                - mcp__github__update_issue: Use this to apply labels to the issue or set issue type (do not use this for commenting)
                - mcp__github__search_issues: Use this to find similar issues that might provide context for proper categorization and to identify potential duplicate issues
                - mcp__github__list_issues: Use this to understand patterns in how other issues are labeled
                - Start by using mcp__github__get_issue to get the issue details

            3. Analyze the issue content, considering:
                - The issue title and description
                - The type of issue (bug report, feature request, question, etc.)
                - Technical areas mentioned
                - Severity or priority indicators
                - User impact
                - Components affected

            4. Select appropriate labels from the available labels list provided above:
                - Choose labels that accurately reflect the issue's nature
                - Be specific but comprehensive
                - If you find similar issues using mcp__github__search_issues, consider using a "duplicate" label if appropriate. Only do so if the issue is a duplicate of another OPEN issue.

            5. Use issue type instead of labels if the issue is a feature request or bug report:
                - If the issue is a feature request, set the issue type to "feature request". Do not apply the "enhancement" label.
                - If the issue is a bug report, set the issue type to "bug". Do not apply the "bug" label.

            6. Apply the selected labels:
                - Use mcp__github__update_issue to apply your selected labels
                - DO NOT post any comments explaining your decision
                - DO NOT communicate directly with users
                - If no labels are clearly applicable, do not apply any labels

            IMPORTANT GUIDELINES:
            - Be thorough in your analysis
            - Only select labels from the provided list above
            - DO NOT post any comments to the issue
            - Your ONLY action should be to apply labels using mcp__github__update_issue
            - It's okay to not add any labels if none are clearly applicable

          allowed_tools: "Bash(gh label list),mcp__github__get_issue,mcp__github__get_issue_comments,mcp__github__update_issue,mcp__github__search_issues,mcp__github__list_issues"
          timeout_minutes: "5"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
