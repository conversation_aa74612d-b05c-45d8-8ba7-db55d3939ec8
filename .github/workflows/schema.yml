name: <PERSON><PERSON><PERSON>

on:
  push:
    paths:
      - "*.yaml"
      - "*.json"
  pull_request:
    paths:
      - "*.yaml"
      - "*.json"

jobs:
  build:
    runs-on: depot-ubuntu-24.04-arm

    steps:
      - uses: actions/checkout@v4
      - uses: nwisbeta/validate-yaml-schema@v2.0.0
        with:
          yamlSchemasJson: |
            {
                "./schema.json": ["evcc.dist.yaml"]
            }
