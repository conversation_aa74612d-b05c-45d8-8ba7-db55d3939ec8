name: Create Documentation Issue

on:
  pull_request_target:
    types: [closed]
    branches: [master]

jobs:
  check-label-and-create-issue:
    runs-on: depot-ubuntu-24.04-arm
    if: github.event.pull_request.merged == true
    steps:
      - name: Check for 'needs documentation' label
        id: check-label
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_TOKEN }}
          script: |
            const { data: labels } = await github.rest.issues.listLabelsOnIssue({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            const hasLabel = labels.some(label => label.name === 'needs documentation');
            return hasLabel;
          result-encoding: string

      - name: Create Docs Issue
        if: steps.check-label.outputs.result == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_TOKEN }}
          script: |
            const title = `Document: ${context.payload.pull_request.title}`;
            const body = `We need to document the new feature introduced in this PR: ${context.payload.pull_request.html_url}`;

            await github.rest.issues.create({
              owner: 'evcc-io',
              repo: 'docs',
              title: title,
              body: body
            });
