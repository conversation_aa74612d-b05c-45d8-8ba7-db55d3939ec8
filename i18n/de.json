{"batterySettings": {"batteryLevel": "Ladestand der Batterie", "bufferStart": {"above": "wenn über {soc}.", "full": "wenn auf {soc}.", "never": "nur mit genug PV-Überschuss."}, "capacity": "{energy} von {total}", "control": "Batteriesteuerung", "discharge": "Verhindere Entladung im Schnell-Modus und bei geplantem Laden.", "disclaimerHint": "<PERSON><PERSON><PERSON><PERSON>:", "disclaimerText": "Nur relevant im PV-Modus. Das Ladeverhalten wird entsprechend angepasst.", "gridChargeTab": "Netzladen", "legendBottomName": "Priorisiere die Hausbatterie", "legendBottomSubline": "bis sie {soc} erreicht hat.", "legendMiddleName": "Priorisiere Fahrzeugladen,", "legendMiddleSubline": "wenn Hausbatterie über {soc} ist.", "legendTopAutostart": "Starte automatisch", "legendTopName": "Batterieunterstütztes Fahrzeugladen", "legendTopSubline": "wenn Hausbatterie über {soc} ist.", "modalTitle": "Hausbatterie", "usageTab": "Batterienutzung"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON>, das seinen Verbrauch basierend auf verfügbarem Überschuss (z. B. smarter Heizstab) selbstständig anpasst. evcc erwartet, dass dieses Gerät selbstständig seine Leistungsaufnahme reduziert, wenn es notwendig ist.", "titleAdd": "Intelligenten Verbraucher hinzufügen", "titleEdit": "Intelligenten Verbraucher bearbeiten"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON> bear<PERSON>ten"}, "charge": {"titleAdd": "Energiezähler hinzufügen", "titleEdit": "Energiezähler bearbeiten"}, "charger": {"chargers": "Wallboxen", "generic": "Generische Integrationen", "heatingdevices": "Wärmeerzeuger", "ocppHelp": "<PERSON><PERSON><PERSON> diese Adresse in die Konfiguration der Wallbox.", "ocppLabel": "OCPP-Server URL", "switchsockets": "Schaltbare Steckdosen", "template": "<PERSON><PERSON><PERSON>", "titleAdd": {"charging": "Wallbox hinzufügen", "heating": "Heizung hinzufügen"}, "titleEdit": {"charging": "Wallbox bearbeiten", "heating": "Heizung bearbeiten"}, "type": {"custom": {"charging": "Benutzerdefinierte Wallbox", "heating": "Benutzerdefinierte Heizung"}, "heatpump": "Benutzerdefinierte Wärmepumpe", "sgready": "Benutzerdefinierte Wärmepumpe (sg-ready, komplett)", "sgready-boost": "Benutzerdefinierte Wärmepumpe (sg-ready, boost)", "switchsocket": "Benutzerdefinierte schaltbare Steckdose"}}, "circuits": {"description": "<PERSON><PERSON><PERSON> sicher, dass die Summe aller Ladepunkte, die an einen Stromkreis angeschlossen sind, die konfigurierten Leistungs- und Stromgrenzen nicht überschreitet. Stromkreise können verschachtelt werden, um eine Hierarchie aufzubauen.", "title": "Lastmanagement"}, "control": {"description": "Normalerweise sind die Standardwerte in Ordnung. Ändere nur etwas, wenn du weißt, was du tust.", "descriptionInterval": "Regelkreis Aktualisierungszyklus in Sekunden. <PERSON><PERSON><PERSON><PERSON>, wie oft evcc Messdaten liest, die Ladeleistung anpasst und die Benutzeroberfläche aktualisiert. Kurze Intervalle (< 30s) können zu Oszillationen und unerwünschtem Verhalten führen.", "descriptionResidualPower": "Verschiebt den Betriebspunkt des Regelkreises. <PERSON><PERSON> du eine Hausbatterie hast, wird <PERSON><PERSON><PERSON><PERSON>, einen <PERSON> von 100 W einzustellen. Auf diese Weise erhält die Batterie eine leichte Priorität gegenüber dem Netzbezug.", "labelInterval": "Aktualisierungsintervall", "labelResidualPower": "Residualleistung", "title": "Regelverhalten"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON>", "broker": "Broker", "bucket": "Bucket", "capacity": "Kapazität", "chargeStatus": "Status", "chargeStatusA": "nicht verbunden", "chargeStatusB": "verbunden", "chargeStatusC": "<PERSON><PERSON><PERSON>", "chargeStatusE": "<PERSON><PERSON>", "chargeStatusF": "<PERSON><PERSON>", "chargedEnergy": "Geladen", "co2": "Netz-CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "Steuerbar", "currency": "Währung", "current": "<PERSON><PERSON>", "currentRange": "<PERSON><PERSON>", "enabled": "Ladebereit", "energy": "Energie", "feedinPrice": "Einspeisevergütung", "gridPrice": "Netzpreis", "heaterTempLimit": "Heizungslimit", "hemsType": "System", "identifier": "RFID-Kennung", "no": "<PERSON><PERSON>", "odometer": "Kilometerstand", "org": "Organisation", "phaseCurrents": "Strom L1, L2, L3", "phasePowers": "Leistung L1, L2, L3", "phaseVoltages": "Spannung L1, L2, L3", "phases1p3p": "Phasenumschaltung", "power": "Le<PERSON><PERSON>", "powerRange": "Le<PERSON><PERSON>", "range": "Reichweite", "singlePhase": "Einphasig", "soc": "Ladestand", "solarForecast": "PV-Vorhersage", "temp": "Temperatur", "topic": "<PERSON>a", "url": "URL", "vehicleLimitSoc": "Fahrzeuglimit", "yes": "<PERSON>a"}, "deviceValueChargeStatus": {"A": "A (nicht verbunden)", "B": "B (verbunden)", "C": "C (lädt)"}, "devices": {"auxMeter": "<PERSON><PERSON>", "batteryStorage": "<PERSON><PERSON>ie", "solarSystem": "PV-Anlage"}, "editor": {"loading": "<PERSON>de YAML-Editor …"}, "eebus": {"description": "Grundkonfiguration für die Kommunikation mit anderen EEBus-Geräten.", "title": "EEBus"}, "ext": {"description": "Kann für Lastmanagement oder Statistikzwecke verwendet werden.", "titleAdd": "Zusätzlichen Zähler hinzufügen", "titleEdit": "Zusätzlichen Zähler bearbeiten"}, "form": {"danger": "Achtung", "deprecated": "veraltet", "example": "Beispiel", "optional": "optional"}, "general": {"cancel": "Abbrechen", "customHelp": "<PERSON><PERSON><PERSON> ein benutzerdefiniertes Gerät mit evcc's Plugin-System.", "customOption": "Benutzerdefiniertes Gerät", "delete": "Löschen", "docsLink": "<PERSON><PERSON>e Do<PERSON>.", "experimental": "Experimentell", "hideAdvancedSettings": "Erweiterte Einstellungen ausblenden", "invalidFileSelected": "Ungültige Datei ausgewählt", "noFileSelected": "<PERSON><PERSON> ausgewählt.", "off": "aus", "on": "an", "password": "Passwort", "readFromFile": "Aus Datei lesen", "remove": "Entfernen", "save": "Speichern", "selectFile": "Durchsuchen", "showAdvancedSettings": "Erweiterte Einstellungen anzeigen", "telemetry": "Telemetrie", "templateLoading": "<PERSON>de …", "title": "Titel", "validateSave": "Überprüfen & speichern"}, "grid": {"title": "Netzzähler", "titleAdd": "Netzzähler hinzufügen", "titleEdit": "Netzzähler bearbeiten"}, "hems": {"description": "evcc mit einem anderen Heimenergiemanagementsystem verbinden.", "title": "HEMS"}, "icon": {"change": "ändern"}, "influx": {"description": "Schreibt Ladedaten und andere Metriken in InfluxDB. Verwende Grafana oder andere Tools, um die Daten zu visualisieren.", "descriptionToken": "<PERSON>ehe die InfluxDB-Dokumentation, um zu erfahren, wie du einen erstellst. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Erlaube unsichere Verbindungen", "labelDatabase": "Datenbank", "labelInsecure": "Zertifikatsüberprüfung", "labelOrg": "Organisation", "labelPassword": "Passwort", "labelToken": "API-Token", "labelUrl": "URL", "labelUser": "<PERSON><PERSON><PERSON>", "title": "InfluxDB", "v1Support": "Unterstützung für InfluxDB 1.x?", "v2Support": "Zurück zu InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Wallbox hinzufügen", "heating": "Heizung hinzufügen"}, "addMeter": "Zusätzlichen Energiezähler hinzufügen", "cancel": "Abbrechen", "chargerError": {"charging": "Wallbox muss konfiguriert sein.", "heating": "Heizung muss konfiguriert sein."}, "chargerLabel": {"charging": "Wallbox", "heating": "Heizung"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Verwendet einen Strombereich von 6 bis 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Verwendet einen Strombereich von 6 bis 32 A.", "chargerPowerCustom": "andere", "chargerPowerCustomHelp": "Definiere einen eigenen Strombereich.", "chargerTypeLabel": "Ladetyp", "chargingTitle": "Verhalten", "circuitHelp": "Lastmanagement-<PERSON><PERSON><PERSON><PERSON><PERSON>, um die Leistungs- und Stromgrenzen nicht zu überschreiten.", "circuitLabel": "Stromkreis", "circuitUnassigned": "nicht zugewiesen", "defaultModeHelp": {"charging": "Lademodus beim Anschließen des Fahrzeugs.", "heating": "Wird beim <PERSON>t gesetzt."}, "defaultModeHelpKeep": "Zuletzt ausgewählter Modus wird beibehalten.", "defaultModeLabel": "Standard-Modus", "delete": "Löschen", "electricalSubtitle": "Im Zweifelsfall Elektriker fragen.", "electricalTitle": "Elektrisch", "energyMeterHelp": "<PERSON>us<PERSON><PERSON><PERSON><PERSON>, wenn die Wallbox keinen integrierten hat.", "energyMeterLabel": "Energiezähler", "estimateLabel": "Ladestand zwischen API-Updates interpolieren", "maxCurrentHelp": "Muss größer als der Mindeststrom sein.", "maxCurrentLabel": "Maximaler Strom", "minCurrentHelp": "<PERSON><PERSON>e nur unter 6 A, wenn du weißt, was du tust.", "minCurrentLabel": "Minimaler Strom", "noVehicles": "<PERSON><PERSON> Fahrzeuge konfiguriert.", "option": {"charging": "Ladepunkt hinzufügen", "heating": "Heizungsgerät hinzufügen"}, "phases1p": "1-phasig", "phases3p": "3-phas<PERSON>", "phasesAutomatic": "Automatische Phasen", "phasesAutomaticHelp": "<PERSON><PERSON> unterstützt automatisches Umschalten zwischen 1- und 3-phasigem Laden. In der Hauptansicht kannst du das Phasenverhalten während des Ladens anpassen.", "phasesHelp": "Anzahl der angeschlossenen Phasen.", "phasesLabel": "Phasen", "pollIntervalDanger": "Regelmäßiges Abfragen des Fahrzeuges kann dazu führen, dass die Fahrzeugbatterie entleert wird. Einige Fahrzeughersteller sperren das Laden in solchen Fällen auch aktiv. Nicht empfohlen! Verwende diese Einstellung nur, wenn du dir der Risiken bewusst bist.", "pollIntervalHelp": "Zeit zwischen Fahrzeug-API-Updates. Kurze Intervalle können die Fahrzeugbatterie belasten.", "pollIntervalLabel": "Update-Intervall", "pollModeAlways": "immer", "pollModeAlwaysHelp": "Statusabfragen immer in regelmäßigen Abständen durchführen.", "pollModeCharging": "nur be<PERSON>", "pollModeChargingHelp": "Fahrzeugstatus nur während des Ladens abfragen.", "pollModeConnected": "wenn verbunden", "pollModeConnectedHelp": "Fahrzeugstatus in regelmäßigen Abständen abfragen, wenn verbunden.", "pollModeLabel": "Update-Verhalten", "priorityHelp": "Höherer Prioritäten haben Vorrang beim PV-Überschuss.", "priorityLabel": "Priorität", "save": "Speichern", "showAllSettings": "Alle Einstellungen anzeigen", "solarBehaviorCustomHelp": "Definiere eigene Ein- und Ausschaltschwellen und Verzögerungen.", "solarBehaviorDefaultHelp": "Start nach {enableDelay} ausreichend vorhandenem Überschuss. Stop wenn für {disableDelay} nicht genug Überschuss vorhanden ist.", "solarBehaviorLabel": "PV-Überschuss", "solarModeCustom": "manuell", "solarModeMaximum": "nur <PERSON><PERSON>", "thresholdDisableDelayLabel": "Ausschaltverzögerung", "thresholdDisableHelpInvalid": "<PERSON>te verwende einen positiven Wert.", "thresholdDisableHelpPositive": "<PERSON>den stoppen, wenn mehr als {power} für {delay} aus dem <PERSON>z bezogen wird.", "thresholdDisableHelpZero": "<PERSON><PERSON>, wenn minimale Mindestleistung für {delay} nicht erreicht werden kann.", "thresholdDisableLabel": "Netzleistung ausschalten", "thresholdEnableDelayLabel": "Einschaltverzögerung", "thresholdEnableHelpInvalid": "Bitte verwende einen negativen Wert.", "thresholdEnableHelpNegative": "<PERSON>en, wenn {surplus} Überschuss für {delay} verfügbar ist.", "thresholdEnableHelpZero": "<PERSON><PERSON>, wenn erforderliche Mindestleistung für {delay} als Überschuss verfügbar ist.", "thresholdEnableLabel": "Netzleistung einschalten", "titleAdd": {"charging": "Ladepunkt hinzufügen", "heating": "Heizung hinzufügen", "unknown": "Wallbox oder Heizung hinzufügen"}, "titleEdit": {"charging": "Ladepunkt bearbeiten", "heating": "Heizung bearbeiten", "unknown": "Wallbox oder Heizung bearbeiten"}, "titleExample": {"charging": "Garage, Carport, etc.", "heating": "Wärmepumpe, Heizung, etc."}, "titleLabel": "Titel", "vehicleAutoDetection": "automatische Erkennung", "vehicleHelpAutoDetection": "Wählt automatisch das plausibelste Fahrzeug aus. Manuelle Übersteuerung möglich.", "vehicleHelpDefault": "<PERSON>mmt immer an, dass dieses Fahrzeug hier lädt. Auto-Erkennung deaktiviert. Manuelle Übersteuerung möglich.", "vehicleLabel": "Standard-Fahrzeug", "vehiclesTitle": "Fahrzeuge"}, "main": {"addAdditional": "Zusätzlichen Zähler hinzufügen", "addGrid": "Netzzähler hinzufügen", "addLoadpoint": "Wallbox oder Heizung hinzufügen", "addPvBattery": "PV oder Speicher hinzufügen", "addTariffs": "<PERSON><PERSON><PERSON>", "addVehicle": "Fahrzeug hinzufügen", "configured": "konfiguriert", "edit": "bearbeiten", "loadpointRequired": "Mindestens ein Ladepunkt muss konfiguriert werden.", "name": "Name", "title": "Konfiguration", "unconfigured": "nicht konfiguriert", "vehicles": "<PERSON><PERSON>", "yaml": "Geräte aus evcc.yaml sind nicht editierbar."}, "messaging": {"description": "Benachrichtigungen über Ladevorgänge und andere Ereignisse erhalten.", "title": "Benachrichtigungen"}, "meter": {"cancel": "Abbrechen", "delete": "Löschen", "generic": "Generische Integrationen", "option": {"aux": "Intelligenten Verbraucher hinzufügen", "battery": "Hausbatterie hinzufügen", "ext": "Zusätzlichen Zähler hinzufügen", "pv": "PV-<PERSON><PERSON>"}, "save": "Speichern", "specific": "Spezifische Integrationen", "template": "<PERSON><PERSON><PERSON>", "titleChoice": "Was möchtest du hinzufügen?", "validateSave": "Überprüfen & speichern"}, "modbus": {"baudrate": "Baudrate", "comset": "ComSet", "connection": "Modbus Verbindung", "connectionHintSerial": "Das Gerät ist direkt an evcc über eine RS485 Schnittstelle angeschlossen.", "connectionHintTcpip": "Das Gerät ist von evcc aus über LAN/Wifi erreichbar.", "connectionValueSerial": "Seriell / USB", "connectionValueTcpip": "Netzwerk", "device": "G<PERSON><PERSON><PERSON><PERSON>", "deviceHint": "Beispiel: /dev/ttyUSB0", "host": "IP Adresse oder Hostname", "hostHint": "Beispiel: *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus Protokoll", "protocolHintRtu": "Verbindung über eine RS485 Schnittstelle an einem Ethernet Adapter ohne Protokollübersetzung.", "protocolHintTcp": "Gerät hat native LAN/Wifi Unterstützung oder ist über eine RS485 Schnittstelle an einen Ethernet Adapter mit Protokollübersetzung angeschlossen.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Erlaubt mehreren Clients den Zugriff auf ein einzelnes Modbus-Gerät.", "title": "Modbus-Proxy"}, "mqtt": {"authentication": "Authentifizierung", "description": "Verbinde evcc mit einem MQTT-Broker, um Daten mit anderen Systemen in deinem Netzwerk auszutauschen.", "descriptionClientId": "<PERSON>r <PERSON> Nachrichten. <PERSON><PERSON>, wird `evcc-[rand]` verwen<PERSON>.", "descriptionTopic": "<PERSON><PERSON>, um das Publizieren zu deaktivieren.", "labelBroker": "Broker", "labelCaCert": "Serverzertifikat (CA)", "labelCheckInsecure": "Erlaube unsichere Verbindungen", "labelClientCert": "Clientzertifikat", "labelClientId": "Client ID", "labelClientKey": "Client-Key", "labelInsecure": "Zertifikatüberprüfung", "labelPassword": "Passwort", "labelTopic": "<PERSON>a", "labelUser": "<PERSON><PERSON><PERSON>", "publishing": "Veröffentlichen", "title": "MQTT"}, "network": {"descriptionHost": "Verwende den .local-Suffix, um mDNS zu aktivieren. Wird zur Erkennung der mobilen App und einiger OCPP-Wallboxen benötigt.", "descriptionPort": "Port für die Web-Oberfläche und API. Du musst deine Browser-URL aktualisieren, wenn du dies änderst.", "descriptionSchema": "Beeinflusst nur die URL-Generierung. Die Auswahl von HTTPS aktiviert keine Verschlüsselung.", "labelHost": "Hostname", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Netzwerk"}, "options": {"boolean": {"no": "nein", "yes": "ja"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "Heizen", "standby": "Standby"}, "schema": {"http": "HTTP (unverschlüsselt)", "https": "HTTPS (verschlüsselt)"}, "status": {"A": "A (nicht verbunden)", "B": "B (verbunden)", "C": "C (lädt)"}}, "pv": {"titleAdd": "PV-<PERSON><PERSON>", "titleEdit": "PV-<PERSON><PERSON> bear<PERSON>"}, "section": {"additionalMeter": "Zusät<PERSON><PERSON>", "general": "Allgemein", "grid": "Netzanschlus<PERSON>", "integrations": "Integrationen", "loadpoints": "Laden & Heizen", "meter": "PV & Batterie", "system": "System", "vehicles": "Fahrzeuge"}, "sponsor": {"addToken": "Token e<PERSON>ben", "changeToken": "Token <PERSON>", "description": "Das Sponsoring-<PERSON><PERSON> hilft uns, das Projekt zu pflegen und nachhaltig neue und spannende Funktionen zu entwickeln. Als Sponsor erhältst du Zugriff auf alle Wallbox-Implementierungen.", "descriptionToken": "Du erhältst das Token von {url}. Wir bieten auch ein Testtoken für Tests an.", "error": "Das Sponsortoken ist ungültig.", "labelToken": "Sponsortoken", "title": "Sponsoring", "tokenRequired": "Du musst ein Sponsortoken konfigurieren, bevor du dieses Gerät anlegen kannst.", "tokenRequiredLearnMore": "<PERSON><PERSON> erfahren.", "tokenRequiredShort": "<PERSON><PERSON> konfiguriert.", "trialToken": "Testtoken"}, "system": {"backupRestore": {"backup": {"action": "Sicherung herunterladen...", "confirmationButton": "Sicherung herunterladen", "confirmationText": "Bitte gib dein Passwort ein, um die Datenbankdatei herunterzuladen.", "description": "Sichere deine Daten in eine Datei. Diese Datei kann verwendet werden, um deine Daten im Falle eines Systemausfalls wiederherzustellen.", "title": "<PERSON><PERSON><PERSON>"}, "cancel": "Abbrechen", "description": "<PERSON><PERSON><PERSON>, wied<PERSON><PERSON><PERSON><PERSON> und zurücksetzen deiner Daten. <PERSON><PERSON><PERSON><PERSON>, wenn du deine Daten auf ein anderes System übertragen möchtest.", "note": "Hinweis: Alle oben genannten Aktionen betreffen nur deine Datenbankdaten. Die evcc.yaml-Konfigurationsdatei bleibt unverändert.", "reset": {"action": "Zurücksetzen...", "confirmationButton": "Zurücksetzen & Neustart", "confirmationText": "Dies löscht deine ausgewählten Daten dauerhaft. <PERSON><PERSON> sic<PERSON>, dass du zu<PERSON>t eine Sicherung heruntergeladen hast.", "description": "Hast du Probleme mit der Konfiguration und möchtest von vorne beginnen? Lösche alle Daten und beginne neu.", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sessionsDescription": "Löscht den Verlauf deiner Ladevorgänge.", "settings": "Konfiguration & Einstellungen", "settingsDescription": "Löscht alle konfigurierten Geräte, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Caches, usw.", "title": "Z<PERSON>ücksetzen"}, "restore": {"action": "Wiederherstellen...", "confirmationButton": "Wiederherstellen & Neustart", "confirmationText": "Dies überschreibt deine komplette Datenbank. <PERSON><PERSON> sic<PERSON>, dass du zuerst eine Sicherung heruntergeladen hast.", "description": "Stelle deine Daten aus einer Sicherungsdatei wieder her. Dies überschreibt alle deine aktuellen Daten.", "labelFile": "Sicherungsdatei", "title": "Wiederherstellen"}, "title": "Sichern & Wiederherstellen"}, "logs": "Logs", "restart": "<PERSON>eu starten", "restartRequiredDescription": "Bitte neu starten, um die Änderungen zu übernehmen.", "restartRequiredMessage": "Konfiguration geändert.", "restartingDescription": "Bitte warten …", "restartingMessage": "evcc wird neu gestartet."}, "tariffs": {"description": "Hinterlege deine Stromtarife, um die Kosten zu berechnen.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Wird in der Hauptansicht und im Browser-Tab angezeigt.", "label": "Titel", "title": "Titel bearbeiten"}, "validation": {"failed": "fehlgeschlagen", "label": "Status", "running": "pr<PERSON><PERSON> …", "success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "unbekannt", "validate": "prüfen"}, "vehicle": {"cancel": "Abbrechen", "chargingSettings": "Ladeeinstellungen", "defaultMode": "Standard-Modus", "defaultModeHelp": "Lademodus beim Anschließen des Fahrzeugs.", "delete": "Fahrzeug löschen", "generic": "<PERSON><PERSON>e Integrationen", "identifiers": "RFID-Kennungen", "identifiersHelp": "Liste der RFID-Kennungen, um das Fahrzeug zu identifizieren. Ein Eintrag pro Zeile. Auf der Übersichtsseite siehst du die aktuelle RFID-Kennung am jeweiligen Ladepunkt.", "maximumCurrent": "Maximaler Strom", "maximumCurrentHelp": "Muss größer als der minimale Strom sein.", "maximumPhases": "Maximale Phasen", "maximumPhasesHelp": "Mit wie vielen Phasen kann dieses Fahrzeug laden? Wird zur Berechnung der Mindestladeleistung und im Planer verwendet.", "minimumCurrent": "Minimaler Strom", "minimumCurrentHelp": "<PERSON><PERSON> unter 6A, wenn du weißt, was du tust.", "online": "Fahrzeuge mit Schnittstelle", "primary": "Generische Integrationen", "priority": "Priorität", "priorityHelp": "Höhere Priorität bedeutet, dass dieses Fahrzeug Vorrang vor anderen Fahrzeugen hat.", "save": "Speichern", "scooter": "Elektroroller", "template": "<PERSON><PERSON><PERSON>", "titleAdd": "Fahrzeug hinzufügen", "titleEdit": "Fahrzeug bearbeiten", "validateSave": "Prüfen & speichern"}}, "footer": {"community": {"greenEnergy": "<PERSON><PERSON>", "greenEnergySub1": "über evcc geladen", "greenEnergySub2": "seit Oktober 2022", "greenShare": "Sonnenanteil", "greenShareSub1": "Leistung bereitgestellt durch", "greenShareSub2": "PV und Speicher", "power": "Ladeleistung", "powerSub1": "{activeClients} von {totalClients} Nutz<PERSON>", "powerSub2": "laden…", "tabTitle": "Live-Community"}, "savings": {"co2Saved": "{value} e<PERSON><PERSON>art", "co2Title": "CO₂ Emission", "configurePriceCo2": "<PERSON><PERSON> und CO₂-Emissionen zu konfigurieren.", "footerLong": "{percent} Sonnenenergie", "footerShort": "{percent} <PERSON><PERSON>", "modalTitle": "Auswertung Ladeenergie", "moneySaved": "{value} gespart", "percentGrid": "{grid} kWh Netz", "percentSelf": "{self} <PERSON>h <PERSON>", "percentTitle": "Sonnenenergie", "period": {"30d": "letzte 30 Tage", "365d": "letzte 365 Tage", "thisYear": "<PERSON><PERSON>", "total": "gesamt"}, "periodLabel": "Zeitraum:", "priceTitle": "Energiepreis", "referenceGrid": "Netz", "referenceLabel": "Referenzdaten:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "<PERSON>rde Sponsor", "becomeSponsorExtended": "Unterstütze uns direkt. Es gibt auch Sticker.", "confetti": "Lust auf Konfetti?", "confettiPromise": "<PERSON><PERSON> gibt auch Sticker und digitales Konfetti", "sticker": "… oder evcc Sticker?", "supportUs": "Unsere Mission: Sonne tanken zum Standard machen. Zusammen mit Ihrer finanziellen Unterstützung, können wir es ermöglichen.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! <PERSON><PERSON>, evcc weiterzuentwickeln.", "titleNoSponsor": "Unterstütze uns", "titleSponsor": "Du bist Unterstützer", "titleTrial": "Testmodus", "titleVictron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Victron Energy", "trial": "Du befindest dich im Testmodus und kannst alle Funktionen nutzen. Wir freuen uns, wenn du Sponsor wirst.", "victron": "Du verwendest evcc auf Victron Energy Hardware und hast Zugriff auf alle Funktionen."}, "telemetry": {"optIn": "Ich möchte meine Laded<PERSON>n teilen.", "optInMoreDetails": "Mehr Details {0}.", "optInMoreDetailsLink": "hier", "optInSponsorship": "Sponsoring <PERSON><PERSON><PERSON><PERSON>."}, "version": {"availableLong": "neue Version verfügbar", "modalCancel": "Abbrechen", "modalDownload": "Download", "modalInstalledVersion": "Installierte Version", "modalNoReleaseNotes": "<PERSON>ine Versionshinweise verfügbar. Mehr Informationen zur neuen Version:", "modalTitle": "Neue Version verfügbar", "modalUpdate": "Installieren", "modalUpdateNow": "Jetzt installieren", "modalUpdateStarted": "Starte die neue Version von evcc…", "modalUpdateStatusStart": "Installation gestartet:"}}, "forecast": {"co2": {"average": "Durchschnitt", "lowestHour": "Sauberste Stunde", "range": "<PERSON><PERSON><PERSON>"}, "modalTitle": "Vorhersage", "price": {"average": "Durchschnitt", "lowestHour": "Günstigste Stunde", "range": "<PERSON><PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "Übermorgen", "partly": "teilweise", "remaining": "verbleibend", "today": "<PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>"}, "solarAdjust": "Solarprognose anhand der realen Produktionsdaten anpassen{percent}.", "type": {"co2": "CO₂", "price": "Pre<PERSON>", "solar": "Solar"}}, "header": {"about": "<PERSON><PERSON>", "authProviders": {"confirmLogout": "<PERSON><PERSON>, dass du {title} trennen möchtest?", "title": "Autorisierungsstatus"}, "blog": "Blog", "docs": "Dokumentation", "github": "GitHub", "logout": "Abmelden", "nativeSettings": "<PERSON>", "needHelp": "<PERSON><PERSON><PERSON>?", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "help": {"discussionsButton": "GitHub Discussions", "documentationButton": "Dokumentation", "issueButton": "<PERSON>ug melden", "issueDescription": "Komisches Verhalten gefunden?", "logsButton": "Logs ansehen", "logsDescription": "Überprüfe die Logs auf Fehler.", "modalTitle": "<PERSON><PERSON><PERSON>?", "primaryActions": "Funktioniert etwas nicht so, wie es soll? Dies sind gute Anlaufstellen, um Hilfe zu erhalten.", "restart": {"cancel": "Abbrechen", "confirm": "Ja, neu starten!", "description": "Unter normalen Umständen sollte ein Neustart nicht notwendig sein. Mel<PERSON> einen <PERSON>ug, wenn du evcc regelmäßig neu starten musst.", "disclaimer": "Hinweis: evcc beendet sich und verlässt sich darauf, vom Betriebssystem neu gestartet zu werden.", "modalTitle": "<PERSON><PERSON>, dass du neu starten möchtest?"}, "restartButton": "<PERSON>eu starten", "restartDescription": "<PERSON><PERSON> versucht, das Gerät aus- und wieder einzuschalten?", "secondaryActions": "Noch keine Lösung gefunden? Hier sind noch ein paar weitere Möglichkeiten."}, "log": {"areaLabel": "<PERSON><PERSON> Bereich filtern", "areas": "Alle Bereiche", "download": "Komplettes Log herunterladen", "levelLabel": "Nach Log-Level filtern", "nAreas": "{count} <PERSON><PERSON><PERSON>", "noResults": "<PERSON>ine passenden Einträge gefunden.", "search": "<PERSON><PERSON>", "selectAll": "alle wählen", "showAll": "Alle Einträge anzeigen", "title": "Logs", "update": "Aktualisieren"}, "loginModal": {"cancel": "Abbrechen", "demoMode": "Login ist im Demo-Modus nicht verfügbar.", "iframeHint": "Öffne evcc in einem neuen Tab.", "iframeIssue": "Das Passwort ist korrekt, aber dein Browser hat das Authentifizierungscookie abgelehnt. Dies kann passieren, wenn du evcc in einem iframe über HTTP verwendest.", "invalid": "Passwort ist ungültig.", "login": "Anmelden", "password": "Administrator <PERSON><PERSON><PERSON>", "reset": "Passwort zurücksetzen?", "title": "Authentifizierung"}, "main": {"chargingPlan": {"active": "Aktiv", "addRepeatingPlan": "Wiederholenden Plan hinzufügen", "arrivalTab": "Ankunft", "day": "Tag", "departureTab": "Abfahrt", "goal": "Ladeziel", "modalTitle": "Ladeplanung", "none": "keiner", "planNumber": "Plan {number}", "preconditionDescription": "Lade {duration} vor Abfahrt zur Batterie-Vorkonditionierung.", "preconditionLong": "Sp<PERSON><PERSON>", "preconditionOptionAll": "alles", "preconditionOptionNo": "nein", "preconditionShort": "Spät", "remove": "Entfernen", "repeating": "wied<PERSON><PERSON><PERSON>", "repeatingPlans": "Wiederholende Pläne", "selectAll": "Alle wählen", "time": "Zeit", "title": "Plan", "titleMinSoc": "<PERSON><PERSON>", "titleTargetCharge": "Abfahrt", "unsavedChanges": "Ungespeicherte Änderungen vorhanden. Jetzt anwenden?", "update": "<PERSON><PERSON><PERSON>", "weekdays": "Tage"}, "energyflow": {"battery": "<PERSON><PERSON>ie", "batteryCharge": "Batterie laden", "batteryDischarge": "<PERSON><PERSON><PERSON> en<PERSON>", "batteryGridChargeActive": "Netzladen aktiv", "batteryGridChargeLimit": "<PERSON><PERSON><PERSON><PERSON> wenn", "batteryHold": "Batterie (gesperrt)", "batteryTooltip": "{energy} von {total} ({soc})", "forecastTooltip": "Vorhersage: verbleibende PV-Produktion heute", "gridImport": "Netzbezug", "homePower": "<PERSON><PERSON><PERSON><PERSON>", "loadpoints": "Ladepunkt | Ladepunkt | {count} Ladepunkte", "noEnergy": "<PERSON><PERSON>", "pv": "<PERSON><PERSON><PERSON>", "pvExport": "Einspeisung", "pvProduction": "Erzeugung", "selfConsumption": "Eigenverbrauch"}, "heatingStatus": {"charging": "Heize …", "connected": "Standby.", "vehicleLimit": "Heizungslimit", "waitForVehicle": "Bereit. Warte auf Heizung …"}, "loadpoint": {"avgPrice": "⌀ Preis", "charged": "Geladen", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON><PERSON>", "fallbackName": "Ladepunkt", "finished": "<PERSON><PERSON><PERSON><PERSON>", "power": "Le<PERSON><PERSON>", "price": "<PERSON><PERSON>", "remaining": "Restzeit", "remoteDisabledHard": "{source}: Deaktiviert", "remoteDisabledSoft": "{source}: Adaptives PV-Laden deaktiviert", "solar": "<PERSON><PERSON>"}, "loadpointSettings": {"batteryBoost": {"description": "<PERSON><PERSON><PERSON> aus Hausbatterie laden.", "label": "<PERSON><PERSON><PERSON>", "mode": "Nur im PV- und Min+PV-Modus verfügbar.", "once": "Boost ist für diesen Ladevorgang aktiviert."}, "batteryUsage": "Hausbatterie", "currents": "<PERSON><PERSON><PERSON>", "default": "default", "disclaimerHint": "<PERSON><PERSON><PERSON><PERSON>:", "limitSoc": {"description": "Dieses Ladelimit wird verwendet, wenn das Fahrzeug angeschlossen wird.", "label": "Standard Ladelimit"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Fahrzeug wird im PV-Modus „Schnell“ auf {0} geladen. Danach weiter mit PV-Überschuss. N<PERSON><PERSON><PERSON>, um auch an dunklen Tagen eine Mindestreichweite zu gewährleisten.", "label": "<PERSON>. <PERSON> %"}, "onlyForSocBasedCharging": "Diese Optionen sind nur für Fahrzeuge mit bekanntem Ladestand verfügbar.", "phasesConfigured": {"label": "Phasen", "no1p3pSupport": "Wie ist deine Wallbox angeschlossen?", "phases_0": "automatischer Wechsel", "phases_1": "1-phasig", "phases_1_hint": "({min} bis {max})", "phases_3": "3-phas<PERSON>", "phases_3_hint": "({min} bis {max})"}, "smartCostCheap": "günstige Netzladung", "smartCostClean": "<PERSON><PERSON><PERSON><PERSON>", "title": "Einstellungen {0}", "vehicle": "Fahrzeug"}, "mode": {"minpv": "Min+PV", "now": "<PERSON><PERSON><PERSON>", "off": "Aus", "pv": "PV", "smart": "Intelligent"}, "provider": {"login": "anmelden", "logout": "abmelden"}, "startConfiguration": "Konfiguration beginnen", "targetCharge": {"activate": "Aktivieren", "co2Limit": "CO₂-<PERSON><PERSON><PERSON> {co2}", "costLimitIgnore": "Die eingestellte {limit} wird in diesem Zeitraum ignoriert.", "currentPlan": "Aktiver Plan", "descriptionEnergy": "Bis wann sollen {targetEnergy} ins Fahrzeug geladen sein?", "descriptionSoc": "<PERSON>n soll das Fahrzeug auf {targetSoc} geladen sein?", "goalReached": "Ladeziel bereits erreicht", "inactiveLabel": "Zielzeit", "nextPlan": "Nächster Plan", "notReachableInTime": "Zielzeit wird {overrun} später erreicht.", "onlyInPvMode": "Ladeplanung ist nur im PV-Modus aktiv.", "planDuration": "<PERSON><PERSON><PERSON>", "planPeriodLabel": "Zeitraum", "planPeriodValue": "{start} bis {end}", "planUnknown": "noch unbekannt", "preview": "Plan Vorschau", "priceLimit": "Preisgrenze von {price}", "remove": "Entfernen", "setTargetTime": "keine", "targetIsAboveLimit": "Das eingestellte Ladelimit von {limit} wird in diesem Zeitraum ignoriert.", "targetIsAboveVehicleLimit": "Fahrzeuglimit ist kleiner als das Ladeziel.", "targetIsInThePast": "<PERSON><PERSON>hle einen Zeitpunkt in der Zukunft, Marty.", "targetIsTooFarInTheFuture": "Wir passen den Plan an, sobald wir mehr über die Zukunft wissen.", "title": "Zielzeit", "today": "heute", "tomorrow": "morgen", "update": "Aktualisieren", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON><PERSON>, wie du sie konfigurierst.", "vehicleCapacityRequired": "Die Batteriekapazität des Fahrzeugs wird benötigt, um die Ladedauer zu schätzen."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON>", "co2Label": "CO₂-Emission ⌀", "priceLabel": "Energiepreis", "timeRange": "{day} {range} Uhr", "unknownPrice": "noch unbekannt"}, "targetEnergy": {"label": "Ladelimit", "noLimit": "keins"}, "vehicle": {"addVehicle": "Fahrzeug hinzufügen", "changeVehicle": "Fahrzeug ändern", "detectionActive": "Fahrzeugerkennung läuft …", "fallbackName": "Fahrzeug", "moreActions": "Weitere Aktionen", "none": "<PERSON><PERSON>", "notReachable": "Fahrzeug war nicht erreichbar. Versuche evcc neu zu starten.", "targetSoc": "Ladelimit", "temp": "Temperatur", "tempLimit": "Temperaturlimit", "unknown": "Gastfahrzeug", "vehicleSoc": "Ladestand"}, "vehicleStatus": {"awaitingAuthorization": "Warte auf Autorisierung.", "batteryBoost": "Batterie Boost aktiv.", "charging": "Ladevorgang aktiv …", "cheapEnergyCharging": "Günstige Energie verfügbar.", "cheapEnergyNextStart": "Günstige Energie in {duration}.", "cheapEnergySet": "Preislimit gesetzt.", "cleanEnergyCharging": "Saubere Energie verfügbar.", "cleanEnergyNextStart": "Saubere Energie in {duration}.", "cleanEnergySet": "CO₂-Limit gesetzt.", "climating": "Vorklimatisierung erkannt.", "connected": "Verbunden.", "disconnectRequired": "Vorgang abgebrochen. Erneut verbinden.", "disconnected": "Nicht verbunden.", "feedinPriorityNextStart": "Hohe Einspeisevergütungen beginnen in {duration}.", "feedinPriorityPausing": "<PERSON><PERSON><PERSON> pausiert, um die Einspeisung zu maximieren.", "finished": "Abgeschlossen.", "minCharge": "Mindestladung bis {soc}.", "pvDisable": "<PERSON><PERSON> wenig Überschuss. Ladung wird gleich pausiert.", "pvEnable": "Überschuss verfügbar. Ladung wird gleich fortgesetzt.", "scale1p": "Reduziere gleich auf 1-phasiges Laden.", "scale3p": "<PERSON>rhö<PERSON> gleich auf 3-phasiges Laden.", "targetChargeActive": "Ladeplan aktiv. Geschätztes Ende in {duration}.", "targetChargePlanned": "Ladeplan startet in {duration}.", "targetChargeWaitForVehicle": "Ladeplan bereit. Warte auf Fahrzeug …", "vehicleLimit": "Fahrzeuglimit", "vehicleLimitReached": "Fahrzeuglimit erreicht.", "waitForVehicle": "Ladebereit. Warte auf Fahrzeug …", "welcome": "Kurze initiale Ladung zur Bestätigung der Verbindung."}, "vehicles": "Parkplatz", "welcome": "<PERSON><PERSON><PERSON> willkommen!"}, "notifications": {"dismissAll": "Meldungen entfernen", "logs": "Vollständiges Log an<PERSON>hen", "modalTitle": "Meldungen"}, "offline": {"configurationError": "Fehler beim Starten. Überprüfe deine Konfiguration und starte neu.", "message": "<PERSON><PERSON> Verbindung zum Server.", "restart": "Neustart", "restartNeeded": "<PERSON><PERSON><PERSON>erlich, um Änderungen zu übernehmen.", "restarting": "Server ist gleich wieder verfügbar."}, "passwordModal": {"description": "Setze ein Passwort, um die Konfiguration zu schützen. Die Hauptansicht bleibt ohne Login zugänglich.", "empty": "Passwort darf nicht leer sein", "error": "<PERSON><PERSON>: ", "labelCurrent": "Aktuelles Passwort", "labelNew": "Neues Passwort", "labelRepeat": "Neues Passwort wiederholen", "newPassword": "Passwort setzen", "noMatch": "Passwörter stimmen nicht überein", "titleNew": "Administrator <PERSON><PERSON><PERSON> <PERSON>zen", "titleUpdate": "Administrator <PERSON><PERSON><PERSON>", "updatePassword": "Passwort ändern"}, "session": {"cancel": "Abbrechen", "co2": "CO₂", "date": "Zeitraum", "delete": "Löschen", "finished": "Endzeit", "meter": "Zählerstand", "meterstart": "Anfangszählerstand", "meterstop": "Endzählerstand", "odometer": "Kilometerstand", "price": "Pre<PERSON>", "started": "Startzeit", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sessions": {"avgPower": "⌀ Le<PERSON>ung", "avgPrice": "⌀ Preis", "chargeDuration": "<PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Preis {byGroup}", "byGroupLoadpoint": "je <PERSON>", "byGroupVehicle": "je Fahrzeug", "energy": "Geladene Energie", "energyGrouped": "Sonnen- vs. Netzenergie", "energyGroupedByGroup": "Energie {byGroup}", "energySubSolar": "{value} Sonne", "energySubTotal": "{value} gesamt", "groupedCo2ByGroup": "CO₂-Menge {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON> {byGroup}", "historyCo2": "CO₂-Emissionen", "historyCo2Sub": "{value} gesamt", "historyPrice": "Ladekosten", "historyPriceSub": "{value} gesamt", "solar": "Sonnenanteil über das Jahr", "solarByGroup": "Sonnenanteil {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energie (kWh)", "chargeduration": "<PERSON><PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "Startzeit", "finished": "Endzeit", "identifier": "<PERSON><PERSON><PERSON>", "loadpoint": "Ladepunkt", "meterstart": "Anfangszählerstand (kWh)", "meterstop": "Endzählerstand (kWh)", "odometer": "Kilometerstand (km)", "price": "Pre<PERSON>", "priceperkwh": "Preis/kWh", "solarpercentage": "Sonne (%)", "vehicle": "Fahrzeug"}, "csvPeriod": "Download {period} CSV", "csvTotal": "Gesamte CSV herunterladen", "date": "<PERSON><PERSON><PERSON>", "energy": "Geladen", "filter": {"allLoadpoints": "Alle Ladepunkte", "allVehicles": "Alle Fahrzeuge", "filter": "Filtern"}, "group": {"co2": "Emissionen", "grid": "Netz", "price": "Pre<PERSON>", "self": "<PERSON><PERSON>"}, "groupBy": {"loadpoint": "Ladepunkt", "none": "Gesamt", "vehicle": "Fahrzeug"}, "loadpoint": "Ladepunkt", "noData": "Noch keine Ladevorgänge in diesem Monat.", "overview": "Übersicht", "period": {"month": "<PERSON><PERSON>", "total": "Gesamt", "year": "<PERSON><PERSON><PERSON>"}, "price": "<PERSON><PERSON>", "reallyDelete": "Möchtest du diesen Ladevorgang wirklich löschen?", "showIndividualEntries": "Einzelne Ladevorgänge anzeigen", "solar": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Insgesamt", "type": {"co2": "CO₂", "price": "Pre<PERSON>", "solar": "<PERSON><PERSON>"}, "vehicle": "Fahrzeug"}, "settings": {"fullscreen": {"enter": "Vollbild starten", "exit": "<PERSON><PERSON><PERSON><PERSON> beenden", "label": "Vollbild"}, "hiddenFeatures": {"label": "Experimentell", "value": "Experimentelle UI-Funktionen zeigen."}, "language": {"auto": "Automatisch", "label": "<PERSON><PERSON><PERSON>"}, "sponsorToken": {"expires": "Dein Sponsortoken läuft {inXDays} ab. {getNewToken} und aktualisiere es hier.", "getNew": "Hol dir ein <PERSON>", "hint": "Hinweis: Wir werden das zukünftig automatisieren."}, "telemetry": {"label": "Telemetrie"}, "theme": {"auto": "System", "dark": "<PERSON><PERSON><PERSON>", "label": "Design", "light": "Hell"}, "time": {"12h": "12h", "24h": "24h", "label": "Zeitformat"}, "title": "Darstellung", "unit": {"km": "Kilometer", "label": "Einheit", "mi": "<PERSON><PERSON>"}}, "smartCost": {"activeHours": "{active} von {total}", "activeHoursLabel": "Aktive Stunden", "applyToAll": "Überall anwenden?", "batteryDescription": "Lädt die Hausbatterie aus dem Netz.", "cheapTitle": "Günstiges Netzladen", "cleanTitle": "Sauberes Netzladen", "co2Label": "CO₂-Emission", "co2Limit": "CO₂-Grenze", "loadpointDescription": "Aktiviert vorübergehendes Schnellladen im PV-Modus.", "modalTitle": "Smartes Netzladen", "none": "keine", "priceLabel": "Energiepreis", "priceLimit": "Preisgrenze", "resetAction": "<PERSON><PERSON><PERSON> l<PERSON>", "resetWarning": "Es ist kein dynamischer Netzpreis und keine CO₂-Quelle konfiguriert. Dennoch ist eine Grenze von {limit} eingerichtet. Konfiguration aufräumen?", "saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "smartFeedInPriority": {"activeHoursLabel": "Pausierte Stunden", "description": "Pausiert den Ladevorgang bei hohen Preisen, um der profitablen Netzeinspeisung Vorrang zu geben.", "priceLabel": "Einspeisetarif", "priceLimit": "Einspeisegrenze", "resetWarning": "Es ist kein dynamischer Einspeisetarif konfiguriert. Dennoch ist ein G<PERSON>ze von {limit} eingerichtet. Konfiguration aufräumen?", "title": "Einspeisung priorisieren"}, "startupError": {"configFile": "Verwendete Konfigurationsdatei:", "configuration": "Konfiguration", "description": "Bitte überprüfe deine Konfigurationsdatei. Sollte dir die Fehlermeldung nicht weiterhelfen, suche in unseren {0} nach einer Lösung.", "discussions": "GitHub Diskussionen", "fixAndRestart": "Behebe das Problem und starte den Server neu.", "hint": "Hinweis: Ein weiterer Grund könnte ein fehlerhaftes Gerät (Wechselrichter, Zähler, …) sein. Überprüfe deine Netzwerkverbindungen.", "lineError": "In {0} wurde ein Fehler gefunden.", "lineErrorLink": "<PERSON><PERSON><PERSON> {0}", "restartButton": "<PERSON>eu starten", "title": "<PERSON><PERSON> beim <PERSON>"}}