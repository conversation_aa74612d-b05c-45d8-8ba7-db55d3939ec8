{"batterySettings": {"batteryLevel": "<PERSON>llo batteria", "bufferStart": {"above": "quando supera il {soc}.", "full": "quando è al {soc}.", "never": "solo con abbastanza surplus."}, "capacity": "{energy} di {total}", "control": "<PERSON><PERSON>ia", "discharge": "Previeni la scarica della batteria in modalità veloce oppure quando è attiva una pianificazione.", "disclaimerHint": "Nota:", "disclaimerText": "Queste impostazioni riguardano solamente la modalità solare. Il comportamento di ricarica è adattato di conseguenza.", "gridChargeTab": "Carica in corso dalla rete", "legendBottomName": "Priorità alla carica della batteria di casa", "legendBottomSubline": "fino al {soc}.", "legendMiddleName": "Priorità alla carica del veicolo", "legendMiddleSubline": "quando la batteria supera il {soc}.", "legendTopAutostart": "Parti automaticamente", "legendTopName": "Ricarica del veicolo assistita dalla batteria", "legendTopSubline": "quando la batteria supera il {soc}.", "modalTitle": "Batteria di casa", "usageTab": "Uso della batteria"}, "config": {"aux": {"description": "Dispositivo che modifica il suo consumo sulla base del surplus di produzione disponibile (es: boiler smart). evcc si aspetta che questo dispositivo riduca il suo consumo se richiesto.", "titleAdd": "Aggiungi consumatore autoregolato", "titleEdit": "Modifica dispositivo con consumo auto-regolato"}, "battery": {"titleAdd": "Aggiungi Batteria", "titleEdit": "Modifica Batteria"}, "charge": {"titleAdd": "“Aggiungi un contatore di carica\"", "titleEdit": "Modifica contatore di carica"}, "charger": {"chargers": "Caricatori EV", "generic": "Integrazioni generiche", "heatingdevices": "Dispositivi di riscaldamento", "ocppHelp": "Copia questo indirizzo nella configurazione dei tuoi caricatori", "ocppLabel": "URL Server OCPP", "switchsockets": "Prese commutabili", "template": "Produttore", "titleAdd": {"charging": "Aggiungi caricabatterie"}, "titleEdit": {"charging": "Modifica caricabatterie"}}, "circuits": {"description": "Garan<PERSON>ce che la somma dei carichi connessi ad un circuito non superi i limiti di corrente configurati. I circuiti possono essere annidati in una struttura gerarchica.", "title": "Gestione del carico"}, "control": {"description": "Normalmente le impostazioni di default sono adeguate. Modificale solo se sai cosa stai facendo.", "descriptionInterval": "Intervallo ciclo di aggiornamento in secondi. Definisce quanto spesso evcc rileva i dati, modifica la potenza di carica e aggiorna l'interfaccia utente. Intervalli ridotti (<30s) possono causare oscillazioni e comportamenti indesiderati.", "descriptionResidualPower": "Modifica la soglia di corrente per il ciclo di controllo. Se hai una batteria è consigliabile impostare un valore minimo di 100W: in questo modo la batteria riceverà una priorità rispetto all'uso della rete elettrica", "labelInterval": "Intervallo di aggiornamento", "labelResidualPower": "Potenza residua", "title": "Comportamento di controllo"}, "deviceValue": {"amount": "Quantità", "broker": "Broker", "bucket": "Bucket", "capacity": "Capacità", "chargeStatus": "Stato", "chargeStatusA": "non connesso", "chargeStatusB": "connesso", "chargeStatusC": "in carica", "chargeStatusE": "corrente assente", "chargeStatusF": "errore", "chargedEnergy": "Carico", "co2": "CO₂ rete elettrica", "configured": "Configurato", "controllable": "Controllabile", "currency": "Valuta", "current": "<PERSON><PERSON><PERSON>", "currentRange": "<PERSON><PERSON><PERSON>", "enabled": "Abilitato", "energy": "Energia", "feedinPrice": "Prezzo di vendita", "gridPrice": "Prezzo d'acquisto", "heaterTempLimit": "Limite del dispositivo di riscaldamento", "hemsType": "Sistema", "identifier": "Identificatore RFID", "no": "no", "odometer": "Chilometraggio", "org": "Organizzazione", "phaseCurrents": "Corrente L1, L2, L3", "phasePowers": "Potenza L1, L2, L3", "phaseVoltages": "Voltaggio L1, L2, L3", "phases1p3p": "Commutazione di fase", "power": "Potenza", "powerRange": "Potenza", "range": "Intervallo", "singlePhase": "Singola fase", "soc": "Carica", "solarForecast": "Previsioni solari", "temp": "Temperatura", "topic": "Argomento", "url": "URL", "vehicleLimitSoc": "Limite del veicolo", "yes": "sì"}, "deviceValueChargeStatus": {"A": "A (non collegato)", "B": "B (collegato)", "C": "C (in carica)"}, "devices": {"auxMeter": "Dispositivo smart", "batteryStorage": "Batteria di accumulo", "solarSystem": "Sistema fotovoltaico"}, "eebus": {"description": "Configurazione che permette a evcc di comunicare con altri dispositivi EEBus", "title": "EEBus"}, "ext": {"description": "<PERSON><PERSON>ò essere utilizzato per la gestione del carico o per le statistiche.", "titleAdd": "Aggiungi contatore esterno", "titleEdit": "Modifica contatore esterno"}, "form": {"danger": "<PERSON><PERSON><PERSON>", "deprecated": "deprecato", "example": "Esempio", "optional": "facoltativo"}, "general": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Can<PERSON><PERSON>", "docsLink": "Vedi la documentazione.", "experimental": "Sperimentale", "hideAdvancedSettings": "Nascondi impostazioni avanzate", "off": "off", "on": "on", "password": "Password", "readFromFile": "<PERSON><PERSON><PERSON> da file", "remove": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "showAdvancedSettings": "Mostra impostazioni avanzate", "telemetry": "Telemetria", "templateLoading": "Caricamento...", "title": "<PERSON><PERSON>", "validateSave": "Conferma e salva"}, "grid": {"title": "Contatore di rete", "titleAdd": "Aggiungi contatore di rete", "titleEdit": "Modifica contatore di rete"}, "hems": {"description": "Connetti evcc ad un altro sistema di gestione della corrente di casa.", "title": "HEMS"}, "influx": {"description": "Scrive i dati di ricarica e le altre metriche su InfluxDB. Usa Grafana o altri strumenti per visualizzare i dati.", "descriptionToken": "Controlla la documentazione di InfluxDB per imparare a crearne uno. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Permetti certificati self-signed", "labelDatabase": "Database", "labelInsecure": "Validazione certificato", "labelOrg": "Società", "labelPassword": "Password", "labelToken": "Token API", "labelUrl": "URL", "labelUser": "Nome utente", "title": "InfluxDB", "v1Support": "Serve supporto per InfluxDB 1.x?", "v2Support": "Torna a InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Aggiungi caricabatterie"}, "addMeter": "Aggiungi un contatore dedicato per il caricabatterie", "cancel": "<PERSON><PERSON><PERSON>", "chargerError": {"charging": "È necessario configurare un caricatore."}, "chargerLabel": {"charging": "Caricabatterie"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Utilizzerà un intervallo di corrente tra 6 e 16A", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Utilizzerà un intervallo di corrente tra 6 e 32A", "chargerPowerCustom": "altro", "chargerPowerCustomHelp": "Definire un intervallo di corrente personalizzato", "chargerTypeLabel": "Tipo di caricabatterie", "chargingTitle": "In carica", "circuitLabel": "Circuito", "circuitUnassigned": "non assegnato", "defaultModeHelp": {"charging": "Modalità di ricarica quando si collega il veicolo."}, "defaultModeHelpKeep": "Mantiene l'ultima modalità di ricarica selezionata.", "defaultModeLabel": "Modalità predefinita", "delete": "Can<PERSON><PERSON>", "electricalSubtitle": "Se non sei sicuro, chiedi al tuo elettricista.", "electricalTitle": "Elettricità", "energyMeterHelp": "Contatore supplementare se il caricabatterie non ne possiede uno integrato.", "energyMeterLabel": "Misuratore di energia", "estimateLabel": "Stima il livello di carica tra gli aggiornamenti dell'API", "maxCurrentHelp": "Deve essere maggiore della corrente minima.", "maxCurrentLabel": "<PERSON><PERSON><PERSON>", "minCurrentHelp": "<PERSON><PERSON>e al di sotto dei 6A solo se si sa quello che si sta facendo.", "minCurrentLabel": "Corrente minima", "noVehicles": "<PERSON><PERSON><PERSON>lo configurato.", "phases1p": "Monofase", "phases3p": "Trifase", "phasesAutomatic": "Fasi automatiche", "phasesAutomaticHelp": "Il tuo caricabatterie supporta il passaggio automatico tra la carica monofase e quella trifase. Nella schermata principale è possibile modificare la gestione della fase durante la ricarica.", "phasesHelp": "Numero delle fasi collegate al caricabatterie.", "phasesLabel": "Fasi", "pollIntervalDanger": "L'aggiornamento ripetuto dei dati dal veicolo può scaricarne la batteria. Alcune case automobilistiche possono in questi casi impedire attivamente la carica. Non raccomandato! Usare solo se si è consapevoli dei rischi.", "pollIntervalHelp": "Intervallo tra gli aggiornamenti tramite API del veicolo. Intervalli più brevi possono scaricare la batteria.", "pollIntervalLabel": "Intervallo di aggiornamento", "pollModeAlways": "sempre", "pollModeAlwaysHelp": "Aggiorna sempre lo stato del veicolo ad intervalli regolari.", "pollModeCharging": "in carica", "pollModeChargingHelp": "Aggiorna lo stato del veicolo soltanto durante la carica.", "pollModeConnected": "collegato", "pollModeConnectedHelp": "Aggiorna lo stato del veicolo ad intervalli regolari quando collegato.", "pollModeLabel": "Modalità di aggiornamento", "priorityHelp": "I punti di ricarica a maggiore priorità hanno un accesso preferenziale al surplus di produzione solare.", "priorityLabel": "Priorità", "save": "<PERSON><PERSON>", "showAllSettings": "Mostra tutte le impostazioni", "solarBehaviorCustomHelp": "Definisci soglie personalizzate di attivazione e disattivazione della ricarica.", "solarBehaviorDefaultHelp": "Carica soltanto con surplus solare. Inizia se il surplus è mantenuto per {enableDelay}. Interrompi quando non c'è sufficiente surplus per {disableDelay}.", "solarBehaviorLabel": "Comportamento modalità solare", "solarModeCustom": "<PERSON><PERSON><PERSON><PERSON>", "solarModeMaximum": "massimo solare", "thresholdDisableDelayLabel": "Ritardo di disattivazione", "thresholdDisableHelpInvalid": "Inserisci un valore positivo.", "thresholdDisableHelpPositive": "Interrompi la ricarica in caso di prelievo dalla rete di più di {power} per più di {delay}.", "thresholdDisableHelpZero": "Interrompi quando la potenza minima di ricarica non può essere sostenuta per {delay}.", "thresholdDisableLabel": "Soglia di disattivazione (W)", "thresholdEnableDelayLabel": "Ritardo di attivazione", "thresholdEnableHelpInvalid": "Inserisci un valore negativo.", "thresholdEnableHelpNegative": "Inizia la ricarica quando un surplus di {surplus} è disponibile per {delay}.", "thresholdEnableHelpZero": "Inizia la ricarica quando la potenza di ricarica minima è disponibile per {delay}.", "thresholdEnableLabel": "Soglia di attivazione (W)", "titleAdd": "Aggiungi punto di ricarica", "titleEdit": "Modifica punto di ricarica", "titleExample": "Garage, posto auto, ecc.", "titleLabel": "<PERSON><PERSON>", "vehicleAutoDetection": "identificazione automatica", "vehicleHelpAutoDetection": "Seleziona automaticamente il veicolo più plausibile. E' comunque possibile la modifica manuale.", "vehicleHelpDefault": "Seleziona sempre questo veicolo in questo punto di ricarica. Identificazione automatica disabilitata. E' comunque possibile la modifica manuale.", "vehicleLabel": "<PERSON><PERSON><PERSON><PERSON> predefinito", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "Aggiungi un contatore aggiuntivo", "addGrid": "Aggiungi contatore di rete", "addLoadpoint": "Aggiungi punto di ricarica", "addPvBattery": "Aggiungi fotovoltaico o batteria", "addTariffs": "Aggiungi tariffe", "addVehicle": "Aggiungi veicolo", "configured": "configurato", "edit": "modifica", "loadpointRequired": "E' necessario configurare almeno un punto di carica.", "name": "Nome", "title": "Configurazione", "unconfigured": "non configurato", "vehicles": "I miei veicoli", "yaml": "I dispositivi configurati in evcc.yaml non sono modificabili tramite UI."}, "messaging": {"description": "<PERSON>vi messaggi sulle sessioni di ricarica.", "title": "Notifiche"}, "meter": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Elimina", "option": {"aux": "Aggiungi un consumatore autoregolato", "battery": "Aggiungi contatore batteria", "ext": "Aggiungi contatore esterno", "pv": "Aggiungi contatore fotovoltaico"}, "save": "<PERSON><PERSON>", "template": "Produttore", "titleChoice": "Cosa vuoi aggiungere?", "validateSave": "Conferma e salva"}, "modbus": {"baudrate": "Baud rate", "comset": "ComSet", "connection": "Connessione Modbus", "connectionHintSerial": "Il dispositivo è collegato direttamente a evcc tramite un'interfaccia RS485.", "connectionHintTcpip": "Il dispositivo è controllabile da evcc via LAN/Wifi.", "connectionValueSerial": "Seriale / USB", "connectionValueTcpip": "Rete", "device": "Nome del dispositivo", "deviceHint": "Esempio: /dev/ttyUSB0", "host": "Indirizzo IP o hostname", "hostHint": "Esempio: *********", "id": "Modbus ID", "port": "Porta", "protocol": "<PERSON><PERSON>", "protocolHintRtu": "Collegamento tramite un adattatore da RS485 a Ethernet senza traduzione di protocollo.", "protocolHintTcp": "Il dispositivo è dotato di supporto LAN/Wifi nativo o è collegato tramite un adattatore da RS485 a Ethernet con traduzione di protocollo.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Permetti a più client di accedere ad un singolo dispositivo Modbus", "title": "Proxy Modbus"}, "mqtt": {"authentication": "Autenticazione", "description": "Connettiti ad un broker MQTT per permettere lo scambio di dati con altri sistemi nella tua rete.", "descriptionClientId": "Autore del messaggio. Viene usato `evcc-[rand]` se il campo è lasciato vuoto.", "descriptionTopic": "Lasciare vuoto per disabilitare la pubblicazione.", "labelBroker": "Broker", "labelCaCert": "Certificato server (CA)", "labelCheckInsecure": "Permetti certificati self-signed", "labelClientCert": "Certificato client", "labelClientId": "ID Client", "labelClientKey": "Chiave client", "labelInsecure": "Validazione certificato", "labelPassword": "Password", "labelTopic": "Argomento", "labelUser": "Username", "publishing": "Pubblicazione", "title": "MQTT"}, "network": {"descriptionHost": "Usa il suffisso .local per abilitare mDNS. Utilizzato nell'app mobile e in alcune stazioni OCPP.", "descriptionPort": "Porta per l'interfaccia web e l'API. Dovrai modificare l'URL se modifichi questo.", "descriptionSchema": "Modifica solo come vengono generati gli URL. Selezionare HTTPS non abiliterà la crittografia.", "labelHost": "Hostname", "labelPort": "Porta", "labelSchema": "Tipo", "title": "Rete"}, "options": {"boolean": {"no": "no", "yes": "sì"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (non crittografato)", "https": "HTTPS (crittografato)"}, "status": {"A": "A (non collegato)", "B": "B (collegato)", "C": "C (in carica)"}}, "pv": {"titleAdd": "Aggiungi Contatore Fotovoltaico", "titleEdit": "Modifica Contatore Fotovoltaico"}, "section": {"additionalMeter": "Contatori aggiuntivi", "general": "Generali", "grid": "Rete Elettrica", "integrations": "Integrazioni", "loadpoints": "Stazioni di ricarica", "meter": "Fotovoltaico e Batterie", "system": "Sistema", "vehicles": "<PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "Inserisci il token", "changeToken": "Modifica il token", "description": "Il modello di sponsorizzazione ci aiuta a mantenere il progetto e a introdurre in modo sostenibile nuove ed entusiasmanti funzionalità. Come sponsor hai accesso a tutte le implementazioni del caricabatterie.", "descriptionToken": "<PERSON><PERSON> il token da {url}. Offriamo anche un token di prova per i test.", "error": "Il token sponsor non è valido.", "labelToken": "Token sponsor", "title": "Sponsorizzazione", "tokenRequired": "Devi configurare un token sponsor prima di poter creare questo dispositivo.", "tokenRequiredLearnMore": "Maggiori informazioni", "trialToken": "Token di prova"}, "system": {"logs": "Logs", "restart": "Riavvia", "restartRequiredDescription": "Riavvia per vedere l'effetto.", "restartRequiredMessage": "La configurazione è cambiata.", "restartingDescription": "Attendere prego…", "restartingMessage": "Riavvio evcc in corso."}, "tariffs": {"description": "Definisci le tue tariffe energetiche per calcolare i costi delle tue sessioni di ricarica.", "title": "Tariffe"}, "title": {"description": "Visualizzato nella schermata principale e nella scheda del browser.", "label": "<PERSON><PERSON>", "title": "Modifica Titolo"}, "validation": {"failed": "fallito", "label": "Stato", "running": "in corso di verifica...", "success": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "scon<PERSON><PERSON><PERSON>", "validate": "convalidato"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "chargingSettings": "Impostazioni di carica", "defaultMode": "Modalità predefinita", "defaultModeHelp": "Modo di carica predefinito alla connessione del veicolo.", "delete": "Can<PERSON><PERSON>", "generic": "Altre integrazioni", "identifiers": "Identificatori RFID", "identifiersHelp": "Lista di stringhe RFID per l'identificazione del veicolo. Una stringa per riga. L'identificativo corrente è indicato nella pagina riassuntiva del punto di carica.", "maximumCurrent": "<PERSON><PERSON><PERSON>", "maximumCurrentHelp": "Deve essere maggiore della corrente minima.", "maximumPhases": "Fasi massime", "maximumPhasesHelp": "Con quante fasi può essere caricato questo veicolo? Questo dato è usato per calcolare il surplus minimo e la durata della pianificazione.", "minimumCurrent": "Corrente minima", "minimumCurrentHelp": "<PERSON>endi al di sotto di 6A solo se sai cosa stai facendo.", "online": "Veicoli con API online", "priority": "Priorità", "priorityHelp": "Con una priorità maggiore il veicolo ha un accesso prioritario al surplus solare.", "save": "<PERSON><PERSON>", "scooter": "<PERSON>ooter", "template": "Produttore", "titleAdd": "Aggiungi Veicolo", "titleEdit": "Modifica Veicolo", "validateSave": "Verifica e salva"}}, "footer": {"community": {"greenEnergy": "<PERSON><PERSON>", "greenEnergySub1": "ricaricato con evcc", "greenEnergySub2": "da ottobre 2022", "greenShare": "Quota solare", "greenShareSub1": "energia fornita da", "greenShareSub2": "Solare e accumulo di batterie", "power": "Potenza di carica", "powerSub1": "{activeClients} di {totalClients} partecipanti", "powerSub2": "in carica...", "tabTitle": "Comunità online"}, "savings": {"co2Saved": "{value} di risparmio", "co2Title": "Emissioni di CO₂", "configurePriceCo2": "Sc<PERSON><PERSON> come configurare i dati su prezzi e CO₂.", "footerLong": "{percent} di energia solare", "footerShort": "{percent} solare", "modalTitle": "Riepilogo energia caricata", "moneySaved": "{value} di risparmio", "percentGrid": "{grid} kWh rete", "percentSelf": "{self} kWh solare", "percentTitle": "Energia solare", "period": {"30d": "ultimi 30 giorni", "365d": "ultimi 365 giorni", "thisYear": "quest'anno", "total": "tutto il tempo"}, "periodLabel": "Periodo:", "priceTitle": "Prezzo energia", "referenceGrid": "rete", "referenceLabel": "Dati di riferimento:", "tabTitle": "I miei dati"}, "sponsor": {"becomeSponsor": "Diventa uno sponsor", "becomeSponsorExtended": "Sostienici direttamente per ottenere adesivi.", "confetti": "Pronti per i coriandoli?", "confettiPromise": "Si ottengono adesivi e coriandoli digitali", "sticker": "... o adesivi evcc?", "supportUs": "La nostra missione è rendere la ricarica solare una cosa totalmente automatica e normale. Aiuta evcc pagando quello che ritieni sia giusto.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Il tuo contributo aiuta a sviluppare ulteriormente evcc.", "titleNoSponsor": "Sost<PERSON><PERSON>", "titleSponsor": "Sei un sostenitore", "titleTrial": "Modalità di prova", "titleVictron": "Sponsorizzato da Victron Energy", "trial": "Sei in modalità di prova e puoi utilizzare tutte le funzionalità. Ti invitiamo a sostenere il progetto.", "victron": "Stai utilizzando evcc sull'hardware Victron Energy e hai accesso a tutte le funzionalità."}, "telemetry": {"optIn": "<PERSON>oglio contribuire con i miei dati.", "optInMoreDetails": "<PERSON><PERSON><PERSON><PERSON> {0}.", "optInMoreDetailsLink": "qui", "optInSponsorship": "È necessaria una sponsorizzazione."}, "version": {"availableLong": "aggiornamento disponibile", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "Download", "modalInstalledVersion": "Versione correntemente installata", "modalNoReleaseNotes": "Non ci sono note di rilascio disponibili. Altre informazioni circa la nuova versione si trovano qui:", "modalTitle": "Aggiornamento disponibile", "modalUpdate": "Installare", "modalUpdateNow": "Installa ora", "modalUpdateStarted": "Evcc ripartirà dopo l'aggiornamento...", "modalUpdateStatusStart": "Installazione avviata:"}}, "forecast": {"co2": {"average": "Media", "lowestHour": "Ora più ecologica", "range": "Intervallo"}, "modalTitle": "Previsioni", "price": {"average": "Media", "lowestHour": "Ora più economica", "range": "Intervallo"}, "solar": {"dayAfterTomorrow": "<PERSON><PERSON><PERSON><PERSON>", "partly": "<PERSON><PERSON><PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>"}, "solarAdjust": "Modifica la previsione sulla base dei dati reali{percent}.", "type": {"co2": "CO₂", "price": "Prezzo", "solar": "<PERSON><PERSON>"}}, "header": {"about": "<PERSON><PERSON><PERSON><PERSON>", "blog": "Blog", "docs": "Documentazione", "github": "GitHub", "login": "Iscrizioni Veicolo", "logout": "Logout", "nativeSettings": "Cambia Server", "needHelp": "Hai bisogno di aiuto?", "sessions": "Sessioni di ricarica"}, "help": {"discussionsButton": "Discussioni su GitHub", "documentationButton": "Documentazione", "issueButton": "<PERSON><PERSON><PERSON> un <PERSON>", "issueDescription": "Hai trovato un comportamento strano o sbagliato?", "logsButton": "Vedi i logs", "logsDescription": "Controlla la presenza di errori nei log.", "modalTitle": "Bisogno di aiuto?", "primaryActions": "Qualcosa non funziona come dovrebbe? Questi sono ottimi punti dove poter ottenere un aiuto.", "restart": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Sì, riavvia!", "description": "In circostanze normali il riavvio non dovrebbe essere necessario. Considera l'idea di segnalare un bug se è necessario riavviare evcc regolarmente.", "disclaimer": "Nota: evcc verrà chiuso e si affiderà al sistema operativo per riavviare il servizio.", "modalTitle": "Sei sicuro di voler riavviare?"}, "restartButton": "Riavvia", "restartDescription": "Hai provato a spegnerlo e riac<PERSON>lo?", "secondaryActions": "Non sei ancora in grado di risolvere il tuo problema? Ecco alcune opzioni più complesse."}, "log": {"areaLabel": "Filtrato per area", "areas": "<PERSON><PERSON> le aree", "download": "Scarica log completo", "levelLabel": "Filtra per livello log", "nAreas": "{count} aree", "noResults": "Nessun log corrispondente", "search": "Cerca", "selectAll": "<PERSON><PERSON><PERSON>na tutti", "showAll": "<PERSON><PERSON> tutto", "title": "Log", "update": "Aggiornamento automatico"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON>", "error": "Login fallito: ", "iframeHint": "Apri evcc in una nuova scheda.", "iframeIssue": "La tua password è corretta, ma sembra che il cookie di autenticazione sia stato eliminato. <PERSON>o può succedere quando usi evcc su un iframe su HTTP.", "invalid": "Password errata.", "login": "<PERSON><PERSON>", "password": "Password", "reset": "Reimpostare la password?", "title": "Autenticazione"}, "main": {"chargingPlan": {"active": "Attivo", "addRepeatingPlan": "Aggiungi piano ricorrente", "arrivalTab": "Arrivo", "day": "<PERSON><PERSON><PERSON>", "departureTab": "Partenza", "goal": "Obiettivo di ricarica", "modalTitle": "Piano di ricarica", "none": "nessuno", "planNumber": "Piano {number}", "preconditionDescription": "Carica {duration} prima della partenza per pre-condizionare la batteria.", "preconditionLong": "Carica ritardata", "preconditionOptionAll": "tutto", "preconditionOptionNo": "no", "preconditionShort": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "repeating": "ricorrente", "repeatingPlans": "<PERSON><PERSON> r<PERSON>", "selectAll": "Se<PERSON><PERSON>na tutti", "time": "<PERSON>a", "title": "Piano", "titleMinSoc": "Carica minima", "titleTargetCharge": "Partenza", "unsavedChanges": "Ci sono modifiche non salvate. Vuoi salvarle adesso?", "update": "Applica", "weekdays": "<PERSON><PERSON><PERSON>"}, "energyflow": {"battery": "Batteria", "batteryCharge": "Carica della batteria", "batteryDischarge": "Scarico della batteria", "batteryGridChargeActive": "Ricarica dalla rete attiva", "batteryGridChargeLimit": "Ricarica dalla rete quando", "batteryHold": "<PERSON><PERSON>ia (locked)", "batteryTooltip": "{energy} di {total} ({soc})", "forecastTooltip": "previsioni: produzione solare rimanente per oggi", "gridImport": "Uso della rete", "homePower": "Consu<PERSON>", "loadpoints": "Caricabatterie| Caricabatterie | {count} caricabatterie", "noEnergy": "<PERSON><PERSON>un valore", "pv": "Fotovoltaico", "pvExport": "Immissione in rete", "pvProduction": "Produzione", "selfConsumption": "Autoconsumo"}, "heatingStatus": {"charging": "Scaldando. . .", "connected": "Standby.", "vehicleLimit": "Limite riscaldam<PERSON>o", "waitForVehicle": "Pronto. In attesa del calorifero. . ."}, "loadpoint": {"avgPrice": "⌀ Prezzo", "charged": "Caricato", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON>", "fallbackName": "Punto di ricarica", "finished": "<PERSON>a di fine", "power": "Potenza", "price": "Costo", "remaining": "<PERSON><PERSON><PERSON><PERSON>", "remoteDisabledHard": "{source}: Disabilitato", "remoteDisabledSoft": "{source}: Ricarica FV adattiva disabilitata", "solar": "<PERSON><PERSON>"}, "loadpointSettings": {"batteryBoost": {"description": "Carica rapida dalla batteria di casa.", "label": "<PERSON>ost da batter<PERSON>", "mode": "Di<PERSON><PERSON><PERSON><PERSON> solo in modalità solare e min+solare.", "once": "Boost attivo per questa sessione di ricarica."}, "batteryUsage": "Batteria di casa", "currents": "Corrente di carica", "default": "default", "disclaimerHint": "Nota:", "limitSoc": {"description": "Limite di carica applicato quando il veicolo è connesso.", "label": "Limite di default"}, "maxCurrent": {"label": "<PERSON><PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>nte min."}, "minSoc": {"description": "Per le emergenze. Il veicolo viene caricato 'velocemente' al {0} da tutto il solare disponibile, e poi continua con solo il surplus solare.", "label": "Carica min. %"}, "onlyForSocBasedCharging": "Questa opzione è solo per i veicoli dei quali si sa lo stato di carica.", "phasesConfigured": {"label": "Fasi", "no1p3pSupport": "Com'è connessa la tua stazione di ricarica?", "phases_0": "switch automatico", "phases_1": "1 fase", "phases_1_hint": "({min} a {max})", "phases_3": "3 fasi", "phases_3_hint": "({min} al {max})"}, "smartCostCheap": "Ricarica in Rete Economica", "smartCostClean": "Ricarica <PERSON>uli<PERSON>", "title": "Impostazioni {0}", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+<PERSON>e", "now": "Veloce", "off": "Off", "pv": "<PERSON><PERSON>", "smart": "Intelligente"}, "provider": {"login": "accesso", "logout": "log out"}, "startConfiguration": "Iniziamo la configurazione", "targetCharge": {"activate": "<PERSON><PERSON><PERSON><PERSON>", "co2Limit": "Limite CO₂ di {co2}", "costLimitIgnore": "Il limite configurato ({limit}) sarà ignorato durante questo intervallo.", "currentPlan": "Piano attivo", "descriptionEnergy": "Fino a quando dovrebbero essere {targetEnergy} caricati nel veicolo?", "descriptionSoc": "Quando dovrebbe essere caricato il veicolo al {targetSoc}?", "goalReached": "Obiettivo già raggiunto", "inactiveLabel": "Tempo target", "nextPlan": "Prossimo piano", "notReachableInTime": "L'obiettivo sarà raggiunto con un ritardo di {overrun}.", "onlyInPvMode": "Il piano di ricarica funziona solo in modalità Solare.", "planDuration": "Tempo di carica", "planPeriodLabel": "Periodo", "planPeriodValue": "{start} fino a {end}", "planUnknown": "ancora sconos<PERSON>", "preview": "Anteprima del piano", "priceLimit": "limite di prezzo di {price}", "remove": "<PERSON><PERSON><PERSON><PERSON>", "setTargetTime": "nessuno", "targetIsAboveLimit": "Il limite di carica configurato ({limit}) sarà ignorato durante questo intervallo.", "targetIsAboveVehicleLimit": "Limite dell'auto è inferiore all'obbiettivo di carica.", "targetIsInThePast": "<PERSON><PERSON><PERSON> un orario nel futuro, <PERSON>.", "targetIsTooFarInTheFuture": "Adatteremo il piano non appena prevederemo il futuro", "title": "Tempo target", "today": "oggi", "tomorrow": "domani", "update": "Aggiornare", "vehicleCapacityDocs": "Impara a configurarlo.", "vehicleCapacityRequired": "La capienza della batteria del veicolo è necessaria per stimare il tempo di ricarica."}, "targetChargePlan": {"chargeDuration": "Tempo di carica", "co2Label": "Emissione media CO₂", "priceLabel": "Prezzo energia", "timeRange": "{day} {range} h", "unknownPrice": "ancora sconos<PERSON>"}, "targetEnergy": {"label": "Target carica", "noLimit": "nessuno"}, "vehicle": {"addVehicle": "Aggiungi un veicolo", "changeVehicle": "Cambia veicolo", "detectionActive": "Rilevamento del veicolo...", "fallbackName": "<PERSON><PERSON><PERSON><PERSON>", "moreActions": "altre azioni", "none": "<PERSON><PERSON><PERSON>", "notReachable": "Veicolo non raggiungibile. Prova a riavviare evcc.", "targetSoc": "Limite", "temp": "Temp.", "tempLimit": "Limite temp.", "unknown": "<PERSON>ei<PERSON>lo ospite", "vehicleSoc": "Carica"}, "vehicleStatus": {"awaitingAuthorization": "Aspettando l'autorizzazione.", "batteryBoost": "Boost da batteria attivo", "charging": "In carica...", "cheapEnergyCharging": "Corrente economica disponibile.", "cheapEnergyNextStart": "Corrente economica in {duration}.", "cheapEnergySet": "Impostato limite di prezzo.", "cleanEnergyCharging": "Energia pulita disponibile.", "cleanEnergyNextStart": "Energia pulita in {duration}.", "cleanEnergySet": "Limite CO₂ impostato.", "climating": "Pre-climatizzazione rilevata.", "connected": "Collegato.", "disconnectRequired": "Sessione terminata. Per favore riconnettersi.", "disconnected": "Disconnesso.", "finished": "<PERSON><PERSON>.", "minCharge": "Ricarica minima a {soc}.", "pvDisable": "Eccedenza insufficiente. In pausa {remaining}…", "pvEnable": "Eccedenza disponibile tra poco", "scale1p": "Riduzione alla potenza monofase in {remaining}…", "scale3p": "Aumento a potenza trifase in {remaining}…", "targetChargeActive": "Piano di ricarica attivo. Fine prevista tra {duration}.", "targetChargePlanned": "Il piano di ricarica inizia tra {duration}.", "targetChargeWaitForVehicle": "Piano di ricarica pronto. In attesa del veicolo...", "vehicleLimit": "Limite del veicolo", "vehicleLimitReached": "Limite del veicolo raggiunto.", "waitForVehicle": "Pronto. In attesa del veicolo...", "welcome": "Piccola carica iniziale per confermare la connessione."}, "vehicles": "Parcheggio", "welcome": "Benvenuto!"}, "notifications": {"dismissAll": "<PERSON><PERSON><PERSON><PERSON> tutte", "logs": "Vedi tutti i log", "modalTitle": "Notifiche"}, "offline": {"configurationError": "Errore durante l'avvio. Controlla la configurazione e riavvia.", "message": "Non connesso a un server.", "restart": "Riavvia", "restartNeeded": "Necessario per applicare le modifiche", "restarting": "I server ritor<PERSON><PERSON><PERSON> presto"}, "passwordModal": {"description": "Imposta una password per proteggere la configurazione. L'uso della interfaccia principale è consentito anche senza login", "empty": "La password non deve essere vuota", "error": "Errore: ", "labelCurrent": "Password attuale", "labelNew": "Nuova password", "labelRepeat": "<PERSON><PERSON><PERSON> la password", "newPassword": "Crea password", "noMatch": "Le password non corrispondono", "titleNew": "Imposta Password Amministratore", "titleUpdate": "Aggiorna Password Amministratore", "updatePassword": "Aggiorna la password"}, "session": {"cancel": "Can<PERSON><PERSON>", "co2": "CO₂", "date": "Periodo", "delete": "Elimina", "finished": "<PERSON><PERSON>", "meter": "Contatore", "meterstart": "<PERSON><PERSON><PERSON> contatore", "meterstop": "Stop contatore", "odometer": "Contachilometri", "price": "Prezzo", "started": "Iniziato", "title": "Sessione di carica"}, "sessions": {"avgPower": "⌀ Potenza", "avgPrice": "⌀ Prezzo", "chargeDuration": "<PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Prezzo {byGroup}", "byGroupLoadpoint": "per Punto di Ricarica", "byGroupVehicle": "<PERSON>", "energy": "Energia ricaricata", "energyGrouped": "Energia Solare vs Rete", "energyGroupedByGroup": "Energia {byGroup}", "energySubSolar": "{value} solare", "energySubTotal": "{value} totale", "groupedCo2ByGroup": "Quantità CO₂ {byGroup}", "groupedPriceByGroup": "Costo Totale {byGroup}", "historyCo2": "Emissioni CO₂", "historyCo2Sub": "{value} totale", "historyPrice": "Costi di ricarica", "historyPriceSub": "{value} totale", "solar": "Perc. Solare annua", "solarByGroup": "<PERSON><PERSON><PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energia (kWh)", "chargeduration": "<PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "<PERSON><PERSON><PERSON>", "finished": "<PERSON><PERSON>", "identifier": "Identificativo", "loadpoint": "Punto di ricarica", "meterstart": "Inizio contatore (kWh)", "meterstop": "Ferma contatore (kWh)", "odometer": "Chilometraggio (km)", "price": "Prezzo", "priceperkwh": "Prezzo/kWh", "solarpercentage": "<PERSON>e (%)", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "csvPeriod": "Scarica il CSV di {period}", "csvTotal": "Scarica il CSV complessivo", "date": "<PERSON><PERSON><PERSON>", "energy": "Caricato", "filter": {"allLoadpoints": "tutte le stazioni di ricarica", "allVehicles": "tutte i veicoli", "filter": "Filtra"}, "group": {"co2": "Emissioni", "grid": "Rete", "price": "Prezzo", "self": "<PERSON><PERSON>"}, "groupBy": {"loadpoint": "Punto di ricarica", "none": "Totale", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Punto di ricarica", "noData": "Nessuna sessione di ricarica questo mese.", "overview": "Riepilogo", "period": {"month": "Mese", "total": "Totale", "year": "<PERSON><PERSON>"}, "price": "Costo", "reallyDelete": "Vuoi veramente eliminare questa sessione?", "showIndividualEntries": "<PERSON><PERSON> singole <PERSON>i", "solar": "<PERSON><PERSON>", "title": "Sessioni di ricarica", "total": "Totale", "type": {"co2": "CO₂", "price": "Prezzo", "solar": "<PERSON><PERSON>"}, "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Entra in schermo intero", "exit": "<PERSON><PERSON><PERSON> dallo schermo intero", "label": "Schermo intero"}, "hiddenFeatures": {"label": "Sperimentale", "value": "Mostra caratteristiche sperimentali della UI"}, "language": {"auto": "Automatico", "label": "<PERSON><PERSON>"}, "sponsorToken": {"expires": "Il tuo token sponsor scadrà in {inXDays}. {getNewToken} e inseriscilo qua.", "getNew": "Prendine uno nuovo", "hint": "Nota: questa funzione verrà automatizzata."}, "telemetry": {"label": "Telemetria"}, "theme": {"auto": "sistema", "dark": "scuro", "label": "Design", "light": "chiaro"}, "time": {"12h": "12h", "24h": "24h", "label": "Formato ora"}, "title": "Interfaccia Utente", "unit": {"km": "km", "label": "Unità", "mi": "miglia"}}, "smartCost": {"activeHours": "{active} di {total}", "activeHoursLabel": "Ore attive", "applyToAll": "Applicare su tutti?", "batteryDescription": "Carica la batteria di casa con la corrente della rete", "cheapTitle": "Ricarica economica con la rete", "cleanTitle": "Ricarica pulita con la rete", "co2Label": "Emissioni di CO₂", "co2Limit": "limite CO₂", "loadpointDescription": "Attiva temporaneamente la ricarica rapida in modalità fotovoltaico.", "modalTitle": "Ricarica intelligente con la rete", "none": "nessuno", "priceLabel": "Costo corrente", "priceLimit": "Limite di prezzo", "resetAction": "Rimuovi limite", "resetWarning": "Tariffa dinamica o sorgente di CO₂ non configurate. Tuttavia, c'è ancora un limite di {limit}. Annullare la configurazione?", "saved": "Salvato."}, "startupError": {"configFile": "File di configurazione utilizzato:", "configuration": "Configura", "description": "Controlla il tuo file di configurazione. Se il messaggio di errore non aiuta, controlla {0}.", "discussions": "GitHub Discussioni", "fixAndRestart": "Risolvere il problema e riavviare il server.", "hint": "Nota: potrebbe anche trattarsi di un dispositivo difettoso (inverter, contatore, ...). Controllare le connessioni di rete.", "lineError": "Errore in {0}.", "lineErrorLink": "linea {0}", "restartButton": "Ricomincia", "title": "Errore di avvio"}}