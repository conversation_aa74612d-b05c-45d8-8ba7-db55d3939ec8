{"batterySettings": {"batteryLevel": "Poziom baterii", "bufferStart": {"above": "kiedy powyżej {soc}.", "full": "kiedy na {soc}.", "never": "tylko z wystarczającą nadwyżką."}, "capacity": "{energy} z {total}", "control": "<PERSON><PERSON><PERSON><PERSON>i", "discharge": "Zapobiegaj rozładowaniu w trybie szybkim i planowemu ładowaniu.", "disclaimerHint": "Uwaga:", "disclaimerText": "te ustawienia mają wpływ tylko na tryb solarny. Sposób ładowania jest odpowiednio dostosowywany.", "gridChargeTab": "Ładowanie z sieci", "legendBottomName": "<PERSON><PERSON>t ładowaniu akumulatora domowego", "legendBottomSubline": "a<PERSON> osiągnie {soc}.", "legendMiddleName": "Ustaw priorytet ładowaniu pojazdów", "legendMiddleSubline": "gdy poziom naładowania akumulatora domowego jest wyższy niż {soc}.", "legendTopAutostart": "Rozpocznij automatycznie", "legendTopName": "Ładowanie pojazdu z wykorzystaniem akumulatora", "legendTopSubline": "gdy poziom naładowania akumulatora domowego jest wyższy niż {soc}.", "modalTitle": "Bateria domowa", "usageTab": "<PERSON>ż<PERSON>cie baterii"}, "config": {"battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON><PERSON>"}, "charge": {"titleAdd": "Dodaj licznik ładowania", "titleEdit": "Edytuj licznik ładowania"}, "charger": {"chargers": "ładowarki EV", "generic": "Ogólne integracje", "ocppHelp": "Skopiuj ten adres do konfiguracji Twojej ładowarki", "switchsockets": "Przełączalne gniazdka", "template": "Producent", "titleAdd": {"charging": "<PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "<PERSON><PERSON><PERSON><PERSON>"}}, "circuits": {"description": "Zapewnia, że suma wszystkich punktów obciążenia podłączonych do obwodu nie przekracza skonfigurowanych limitów mocy i prądu. Obwody można zagnieżdżać, aby zbudować hierarchię.", "title": "Zarządzanie obciążeniem"}, "control": {"description": "Zazwyczaj wartości domyślne są w porządku. Zmień je tylko w<PERSON>y, g<PERSON> w<PERSON>, co robisz.", "labelInterval": "Interwał aktualizacji", "labelResidualPower": "<PERSON><PERSON> resztkowa", "title": "Kontrola zach<PERSON>nia"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON><PERSON>", "broker": "Broker", "bucket": "Bucket", "capacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chargeStatus": "Status", "chargeStatusA": "nie poł<PERSON>ony", "chargeStatusB": "połączony", "chargeStatusC": "ładowanie", "chargeStatusE": "brak zasilania", "chargeStatusF": "błąd", "chargedEnergy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configured": "Skonfigurowany", "currency": "<PERSON><PERSON><PERSON>", "current": "Prąd", "currentRange": "Aktualne", "enabled": "Włączony", "energy": "Energia", "feedinPrice": "Cena sprzedaży energii do sieci", "hemsType": "System", "no": "nie", "odometer": "<PERSON><PERSON><PERSON><PERSON>", "phaseCurrents": "Prąd L1, L2, L3", "phasePowers": "Moc L1, L2, L3", "phaseVoltages": "Napięcie L1, L2, L3", "power": "Moc", "powerRange": "<PERSON><PERSON><PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON><PERSON>", "soc": "<PERSON>", "temp": "Temperatura", "url": "URL", "vehicleLimitSoc": "<PERSON><PERSON>", "yes": "tak"}, "deviceValueChargeStatus": {"A": "A (nie połączony)", "B": "B (połączony)", "C": "C (ładowanie)"}, "eebus": {"title": "EEBus"}, "form": {"example": "Przykład", "optional": "opcjonalny"}, "general": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Usuń", "docsLink": "Zobacz dokumentacje", "experimental": "Eksperymentalny", "hideAdvancedSettings": "Sc<PERSON>aj us<PERSON> z<PERSON>e", "off": "wyłączony", "on": "włączony", "password": "<PERSON><PERSON><PERSON>", "readFromFile": "Przeczytaj z pliku", "remove": "Usuń", "save": "<PERSON><PERSON><PERSON><PERSON>", "showAdvancedSettings": "Po<PERSON>ż ustawienia zaawansowane", "telemetry": "Telemetria", "title": "<PERSON><PERSON><PERSON>", "validateSave": "Zatwierdź i zapisz"}, "grid": {"titleAdd": "Dodaj licznik sieci", "titleEdit": "Edytuj licznik sieci"}, "influx": {"labelBucket": "Bucket", "labelDatabase": "<PERSON><PERSON> danych", "labelPassword": "<PERSON><PERSON><PERSON>", "labelToken": "API Token", "labelUrl": "URL", "title": "InfluxDB"}, "loadpoint": {"addCharger": {"charging": "<PERSON><PERSON><PERSON>"}}, "main": {"addLoadpoint": "<PERSON><PERSON><PERSON>", "addPvBattery": "Dodaj licznik energii słonecznej lub baterii", "addVehicle": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Konfiguracja", "unconfigured": "nie skon<PERSON><PERSON><PERSON><PERSON>", "vehicles": "<PERSON><PERSON>", "yaml": "Skonfigurowano w evcc.yaml Nie można edytować w interfejsie użytkownika."}, "messaging": {"description": "Otrzymuj wiadomości o sesjach ładowania."}, "meter": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "template": "Producent", "titleChoice": "Co chcesz dodać?", "validateSave": "Sprawdź i zapisz"}, "modbusproxy": {"title": "Modbus Proxy"}, "mqtt": {"labelClientId": "Client ID", "labelPassword": "<PERSON><PERSON><PERSON>", "title": "MQTT"}, "network": {"labelPort": "Port"}, "options": {"endianness": {"big": "big-endian", "little": "little-endian"}}, "pv": {"titleAdd": "Dodaj licznik energii słonecznej", "titleEdit": "Edytuj licznik energii słonecznej"}, "validation": {"failed": "nie udało się", "label": "Status", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "success": "u<PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "validate": "zwer<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Us<PERSON>ń.", "generic": "Inne integracje", "online": "Pojazdy z API online", "save": "<PERSON><PERSON><PERSON><PERSON>", "scooter": "<PERSON><PERSON><PERSON>", "template": "Producent", "titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "validateSave": "Sprawdź i zapisz"}}, "footer": {"community": {"greenEnergy": "Energia słoneczna", "greenEnergySub1": "naładowany z evcc", "greenEnergySub2": "od Października 2022", "greenShare": "Energii słonecznej", "greenShareSub1": "zasilanie jest dostarczone przez", "greenShareSub2": "słońce i domowe akumulatory energii", "power": "Moc ładowania", "powerSub1": "{activeClients} z {totalClients} uczestników", "powerSub2": "ładuje się...", "tabTitle": "<PERSON> s<PERSON>"}, "savings": {"co2Saved": "{value} zaoszczędziło", "co2Title": "CO₂ Emisje", "configurePriceCo2": "<PERSON><PERSON><PERSON>, jak skonfigu<PERSON> dane dotyczące cen i emisji CO₂.", "footerLong": "{percent} energi<PERSON> s<PERSON>j", "footerShort": "{percent} s<PERSON><PERSON><PERSON><PERSON>", "modalTitle": "Przegląd energii ładowania", "moneySaved": "{value} zaoszczędzone", "percentGrid": "{grid} kWh sieć", "percentSelf": "{self} <PERSON>h sł<PERSON>", "percentTitle": "Energia Słoneczna", "period": {"30d": "ostatnie 30 dni", "365d": "ostatnie 365 dni", "total": "cały czas"}, "periodLabel": "Okres:", "priceTitle": "<PERSON><PERSON>", "referenceGrid": "<PERSON>e<PERSON>", "referenceLabel": "Dane referencyjne:", "tabTitle": "<PERSON><PERSON> dane"}, "sponsor": {"becomeSponsor": "Zostań sponsorem", "confetti": "Gotowy na konfetti?", "confettiPromise": "Będą naklejki i cyfrowe konfetti", "sticker": "… lub naklejki evcc?", "supportUs": "Nasza misja: <PERSON><PERSON><PERSON>, aby ładowanie słoneczne stało się standardem. <PERSON><PERSON><PERSON><PERSON> evcc, p<PERSON><PERSON><PERSON><PERSON> tyle, ile jest to dla <PERSON><PERSON>bie warte.", "thanks": "Dzięki za wsparcie, {sponsor}! Pomaga nam to w dalszym rozwoju.", "titleNoSponsor": "Wesprzyj nas", "titleSponsor": "Jesteś sponsorem"}, "telemetry": {"optIn": "Chcę udostępnić swoje dane.", "optInMoreDetails": "Wię<PERSON>j szczegółów {0}.", "optInMoreDetailsLink": "tutaj", "optInSponsorship": "Wymagany sponsoring."}, "version": {"availableLong": "nowa wersja dos<PERSON>ę<PERSON>", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "<PERSON><PERSON><PERSON>", "modalInstalledVersion": "Zainstalowana we<PERSON>ja", "modalNoReleaseNotes": "Brak dostępnych notatek. Więcej informacji o nowej wersji:", "modalTitle": "Nowa wersja dostę<PERSON>na", "modalUpdate": "<PERSON><PERSON><PERSON><PERSON>", "modalUpdateNow": "<PERSON><PERSON><PERSON><PERSON>", "modalUpdateStarted": "Nowa wersja evcc uruchomi się...", "modalUpdateStatusStart": "Instalacja rozpoczęta:"}}, "header": {"about": "O evcc", "blog": "Blog", "docs": "Dokumentacja", "github": "GitHub", "login": "<PERSON> <PERSON><PERSON>", "nativeSettings": "Zmień serwer", "needHelp": "Potr<PERSON><PERSON><PERSON><PERSON>?", "sessions": "<PERSON><PERSON><PERSON>"}, "help": {"discussionsButton": "Dyskusje na GitHub'ie", "documentationButton": "Dokumentacja", "issueButton": "Zgł<PERSON>ś błąd", "issueDescription": "Znalazłeś dziwne lub niewłaściwe zachowanie?", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pomocy?", "primaryActions": "<PERSON><PERSON> nie działa tak, jak powinno? To dobre mi<PERSON>, a<PERSON> <PERSON><PERSON><PERSON><PERSON> pomoc.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON>k, uruchom ponownie!", "description": "Normalne ponowne uruchamianie nie powinno być konieczne. Rozważ zgłoszenie błędu, je<PERSON><PERSON> chcesz regularnie restartować evcc.", "disclaimer": "Uwaga: evcc zakończy działanie i będzie polegać na systemie operacyjnym w celu ponownego uruchomienia evcc.", "modalTitle": "<PERSON>zy na pewno chcesz uruchomić ponownie?"}, "restartButton": "<PERSON><PERSON><PERSON><PERSON> ponownie", "restartDescription": "Próbowałeś go wyłączyć i włączyć ponownie?", "secondaryActions": "Nadal nie możesz rozwiązać swojego problemu? Oto kilka bardziej wymagających opcji."}, "main": {"chargingPlan": {"active": "Aktywny", "arrivalTab": "Przyjazd", "day": "Dzień", "departureTab": "Wyjazd", "goal": "Cel ładowania", "modalTitle": "Plan ładowania", "none": "nic", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "Czas", "title": "Plan", "titleMinSoc": "Minimalne ładowanie", "titleTargetCharge": "Wyjazd", "unsavedChanges": "Są niezapisane zmiany. Zastosować teraz?", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weekdays": "Dni"}, "energyflow": {"battery": "Bateria", "batteryCharge": "Ładowan<PERSON> bat<PERSON>i", "batteryDischarge": "Rozładowanie baterii", "batteryHold": "Bateria (locked)", "batteryTooltip": "{energy} z {total} ({soc})", "gridImport": "<PERSON> sieci", "homePower": "Zużycie", "loadpoints": "Ładowarka | Ładowarka | {count} Ładowarki", "noEnergy": "Brak danych licznika", "pvExport": "Eksport na siec", "pvProduction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selfConsumption": "Konsumpcja własna"}, "heatingStatus": {"charging": "Ogrzewanie…", "waitForVehicle": "Gotowe. Czekam na grzejnik…"}, "loadpoint": {"avgPrice": "⌀ Cena", "charged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2": "⌀ CO₂", "duration": "Czas trwania", "fallbackName": "Ładowarka", "power": "Moc", "price": "Koszt", "remaining": "Pozostało", "remoteDisabledHard": "{source}: wył<PERSON>czone", "remoteDisabledSoft": "{source}: adaptacyjne ładowanie słonecznie wyłączone", "solar": "Słoneczna"}, "loadpointSettings": {"currents": "Prąd Ładowania", "default": "domyślny", "disclaimerHint": "Uwaga:", "limitSoc": {"description": "Limit ładowania używany, gdy pojazd jest podłączony.", "label": "Domyślny limit"}, "maxCurrent": {"label": "Maks. prąd"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Po<PERSON><PERSON>d jest „s<PERSON><PERSON><PERSON>” ładowany do {0} w trybie „Słońce”. Następnie kontynuuje tylko z nadwyżką fotowoltaiczną. Przydatne, aby zapewnić minimalny zasięg nawet w ciemniejsze dni.", "label": "Minimalne naładowanie %"}, "onlyForSocBasedCharging": "Opcje te są dostępne tylko dla pojazdów ze znanym poziomem naładowania.", "phasesConfigured": {"label": "Fazy", "no1p3pSupport": "Jak jest podłączona ładowarka?", "phases_0": "automatyczne przełączanie", "phases_1": "1 faza", "phases_1_hint": "({min} do {max})", "phases_3": "3 fazy", "phases_3_hint": "({min} do {max})"}, "smartCostCheap": "<PERSON><PERSON> ł<PERSON> si<PERSON>", "smartCostClean": "<PERSON>zyste ładowanie si<PERSON>i", "title": "Ustawienia {0}", "vehicle": "Pojazd"}, "mode": {"minpv": "Min+<PERSON>ł<PERSON>ńce", "now": "<PERSON><PERSON><PERSON><PERSON>", "off": "Stop", "pv": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "provider": {"login": "z<PERSON>uj się", "logout": "wyl<PERSON><PERSON><PERSON>"}, "targetCharge": {"activate": "Aktywować", "co2Limit": "Limit CO₂ z {co2}", "costLimitIgnore": "Skonfigurowany {limit} bę<PERSON>zie ignorowany w tym okresie.", "currentPlan": "Plan aktywny", "descriptionEnergy": "Do kiedy należy zała<PERSON>wać {targetEnergy} do pojazdu?", "descriptionSoc": "Kiedy pojazd powinien zostać naladowany do {targetSoc}?", "inactiveLabel": "<PERSON>zas do<PERSON>owy", "notReachableInTime": "Cel zostanie osiągnięty {overrun} później.", "onlyInPvMode": "Plan ładowania działa tylko w trybie solarnym.", "planDuration": "<PERSON>zas ładowania", "planPeriodLabel": "<PERSON><PERSON>", "planPeriodValue": "{start} do {end}", "planUnknown": "jeszcze nie wiadomo", "preview": "Podgląd planu", "priceLimit": "limit ceny {cena}", "remove": "Usuń", "setTargetTime": "brak", "targetIsAboveLimit": "Skonfigurowany limit ładowania wynoszący {limit} b<PERSON><PERSON><PERSON> w tym okresie ignorowany.", "targetIsAboveVehicleLimit": "Limit pojazdu jest poniżej celu ładowania.", "targetIsInThePast": "Wybierz czas w przyszłości.", "targetIsTooFarInTheFuture": "Dostosujemy plan, gdy tylko dowiemy się więcej o przyszłości.", "title": "<PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "jutro", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON>, jak to s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "vehicleCapacityRequired": "Do oszacowania czasu ładowania wymagana jest pojem<PERSON>ść akumulatora pojazdu."}, "targetChargePlan": {"chargeDuration": "<PERSON>zas ładowania", "co2Label": "CO₂ emisja ⌀", "priceLabel": "<PERSON><PERSON> energii", "timeRange": "{day} {range} h", "unknownPrice": "nadal ni<PERSON>y"}, "targetEnergy": {"label": "Ograniczenie", "noLimit": "brak"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON>", "changeVehicle": "Zmień pojazd", "detectionActive": "Wykrywanie pojazdu...", "fallbackName": "Pojazd", "moreActions": "Wię<PERSON>j a<PERSON>", "none": "Brak p<PERSON>zdu", "notReachable": "Samochód nie był osiągalny. Spróbuj ponownie uruchomić evcc.", "targetSoc": "Ograniczenie", "temp": "Temp.", "tempLimit": "Ograniczenie temp.", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "vehicleSoc": "Bateria"}, "vehicleStatus": {"charging": "Ładuje się...", "cheapEnergyCharging": "<PERSON><PERSON><PERSON><PERSON><PERSON> tania energia.", "cleanEnergyCharging": "Czysta energia dostępna.", "climating": "Wykryto wstępne przygotowanie", "connected": "Połączony.", "disconnected": "Rozłączony.", "minCharge": "Minimalne ładowanie do {soc}.", "pvDisable": "Za mało nadwyżki. Wkrótce przerwa.", "pvEnable": "Dostępne nadwyżki. Zaczynamy wkrótce.", "scale1p": "Wkrótce przejdziemy na ładowanie jednofazowe.", "scale3p": "Wkrótce przejdziemy na ładowanie 3-fazowe.", "targetChargeActive": "Plan ładowania aktywny. Szacunkowy czas zakończenia za {duration}.", "targetChargePlanned": "Zaplanowane ładowanie zacznie się {duration}.", "targetChargeWaitForVehicle": "Zaplanowane ładowanie gotowy. Czekajem na pojazd...", "vehicleLimitReached": "Osiągnięto limit pojazdu.", "waitForVehicle": "Gotowe. Czekam na pojazd..."}, "vehicles": "<PERSON><PERSON><PERSON>"}, "notifications": {"dismissAll": "<PERSON><PERSON><PERSON><PERSON> wszystkie", "modalTitle": "Powiadomienia"}, "offline": {"message": "Brak połączenia z serwerem."}, "session": {"cancel": "<PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "finished": "Zakończony", "meter": "Licznik", "meterstart": "Licznik początek", "meterstop": "Licznik koniec", "odometer": "Przebieg", "price": "<PERSON><PERSON>", "started": "Zaczęło się", "title": "<PERSON><PERSON>ja <PERSON>"}, "sessions": {"avgPower": "⌀ Moc", "avgPrice": "⌀ Cena", "chargeDuration": "Czas trwania", "chartTitle": {"solar": "Udział energii słonecznej w ciągu roku"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energia (kWh)", "created": "Stworzony", "finished": "Zakończony", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Ładowarka", "meterstart": "Licznik początek (kWh)", "meterstop": "Licznik koniec (kWh)", "odometer": "Przebieg (km)", "vehicle": "Pojazd"}, "csvPeriod": "<PERSON><PERSON>rz plik {period} CSV", "csvTotal": "Pobierz plik CSV", "date": "Roz<PERSON>czę<PERSON>", "energy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter": {"allLoadpoints": "wszystkie punkty ładowania", "allVehicles": "wszystkie pojazdy", "filter": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Ładowarka", "noData": "Brak sesji ładowania w tym <PERSON>.", "overview": "Przegląd", "price": "Koszt", "reallyDelete": "<PERSON>zy na pewno chcesz usunąć tę sesję?", "solar": "Słoneczna", "title": "<PERSON><PERSON><PERSON>", "total": "Totalny", "vehicle": "Pojazd"}, "settings": {"hiddenFeatures": {"label": "Eksperymentalny", "value": "Pokaż eksperymentalne funkcje interfejsu"}, "language": {"auto": "Automatyczny", "label": "Język"}, "sponsorToken": {"expires": "Twój token sponsora wygasa {inXDays}. {getNewToken} i zaktualizuj tutaj.", "getNew": "Weź świeżego", "hint": "Uwaga: zautomatyzujemy to w przyszłości."}, "telemetry": {"label": "Telemetria"}, "theme": {"auto": "systemu", "dark": "ciemny", "label": "Wygląd", "light": "jasny"}, "title": "Interfejs użytkownika", "unit": {"km": "km", "label": "Jednostki", "mi": "mile"}}, "smartCost": {"activeHours": "{active} z {total}", "activeHoursLabel": "Aktywne godziny", "applyToAll": "A<PERSON>lik<PERSON>ć wszędzie?", "batteryDescription": "Ładuje akumulator domu energią z sieci.", "cheapTitle": "<PERSON>ie ładowanie z sieci", "cleanTitle": "Czyste ładowanie z sieci", "co2Label": "<PERSON><PERSON><PERSON>", "co2Limit": "Limit CO₂", "loadpointDescription": "Umożliwia tymczasowe szybkie ładowanie w trybie solarnym.", "modalTitle": "Ładowanie z inteligentnej sieci", "none": "nie ma", "priceLabel": "<PERSON><PERSON> energii", "priceLimit": "Limit ceny", "saved": "Zapisano."}, "startupError": {"configFile": "Używany plik konfiguracyjny:", "configuration": "Konfig", "description": "Sprawdź swój plik konfiguracyjny. Je<PERSON><PERSON> komunikat o błędzie nie pomoże, zapoznaj się z naszym {0}.", "discussions": "Dyskusje GitHub", "fixAndRestart": "Proszę rozwiązać problem i zrestartować serwer.", "hint": "Uwaga: <PERSON><PERSON><PERSON><PERSON> te<PERSON>, że masz wadliwe urządzenie (inwerter, licznik, ...). Sprawdź połączenia sieciowe.", "lineError": "Błąd w {0}.", "lineErrorLink": "w<PERSON>z {0}", "restartButton": "<PERSON><PERSON><PERSON><PERSON>", "title": "Błąd uruchamiania"}}