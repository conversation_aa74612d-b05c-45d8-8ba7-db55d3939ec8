{"batterySettings": {"batteryLevel": "<PERSON> baterije", "bufferStart": {"above": "ko je nad {soc}.", "full": "ko je na {soc}.", "never": "samo z dovolj presežka."}, "capacity": "{energy} od {total}", "control": "Nad<PERSON>", "discharge": "Preprečite praznjenje v hitrem načinu in načrtovano polnjenje.", "disclaimerHint": "Opomba:", "disclaimerText": "Te nastavitve vplivajo samo na solarni način. Obnašanje pri polnjenju je ustrezno prilagojeno.", "gridChargeTab": "Polnjenje iz omrežja", "legendBottomName": "Prednostno polnjenje domače baterije", "legendBottomSubline": "dokler ne <PERSON> {soc}.", "legendMiddleName": "Prioritetno polnjenje vozil", "legendMiddleSubline": "ko je domača baterija nad {soc}.", "legendTopAutostart": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "legendTopName": "Polnjen<PERSON> voz<PERSON>, ki ga podpira baterija", "legendTopSubline": "ko je domača baterija nad {soc}.", "modalTitle": "Domača baterija", "usageTab": "<PERSON>rab<PERSON> bat<PERSON>"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON>, ki prilagaja svojo porabo glede na razpoložljivi presežek (kot so pametni grelniki vode). evcc prič<PERSON>je, da bo ta naprava po potrebi zmanjšala porabo energije.", "titleAdd": "Dodajte samoregulirajočega odjemalca", "titleEdit": "Uredi samoregulirajočega odjemalca"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>"}, "charge": {"titleAdd": "Dodaj števec polnjenja", "titleEdit": "Urejanje merilnika polnjenja"}, "charger": {"chargers": "polnilnice za EV", "generic": "Splošne integracije", "heatingdevices": "Ogrevalne <PERSON>rave", "ocppHelp": "Kopirajte ta naslov v konfiguracijo vaše polnilnice.", "ocppLabel": "URL strežnika OCPP", "switchsockets": "Preklopne vtičnice", "template": "Proizvajalec", "titleAdd": {"charging": "Dodaj polnilnik"}, "titleEdit": {"charging": "<PERSON><PERSON>i p<PERSON>"}}, "circuits": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da vsota vseh obremenitvenih točk, povezanih z vezjem, ne preseže konfiguriranih omejitev moči in toka. Vezja je mogoče ugnezditi za izgradnjo hierarhije.", "title": "Upravljanje obremenitve"}, "control": {"description": "Običajno so privzete vrednosti v redu. Spremenite jih le, če veste, kaj počnete.", "descriptionInterval": "Cikel posodabljanja krmilne zanke v sekundah. Določa, kako pogosto evcc bere podatke števca, prilagodi moč polnjenja in posodobi uporabniški vmesnik. Kratki intervali (< 30 s) lahko povzročijo nihanje in neželeno vedenje.", "descriptionResidualPower": "Prestavi točko delovanja krmilne zanke. <PERSON>e imate do<PERSON><PERSON><PERSON> bat<PERSON>, je <PERSON><PERSON><PERSON>, da nastavite vrednost 100 W. Na ta način bo baterija imela rahlo prednost pred uporabo omrežja.", "labelInterval": "Interval posodabljanja", "labelResidualPower": "Preostala moč", "title": "Nadzor vedenja"}, "deviceValue": {"amount": "Znesek", "broker": "Broker", "bucket": "<PERSON><PERSON><PERSON>", "capacity": "Kapaciteta", "chargeStatus": "Status", "chargeStatusA": "brez povezave", "chargeStatusB": "povezan", "chargeStatusC": "polnjenje", "chargeStatusE": "brez mo<PERSON>i", "chargeStatusF": "napaka", "chargedEnergy": "Napolnjeno", "co2": "CO₂ iz omrežja", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "Nadzorljiv", "currency": "Valuta", "current": "Tok", "currentRange": "Tok", "enabled": "Omogočeno", "energy": "Energija", "feedinPrice": "Cena prejete energije iz omrežja", "gridPrice": "Cena omrežja", "heaterTempLimit": "<PERSON><PERSON><PERSON><PERSON>", "hemsType": "Sistem", "identifier": "RFID-Identifier", "no": "ne", "odometer": "Število kilometrov", "org": "Organizacija", "phaseCurrents": "Tok L1, L2, L3", "phasePowers": "Moč L1, L2, L3", "phaseVoltages": "Napetost L1, L2, L3", "phases1p3p": "Fazno stikalo", "power": "<PERSON><PERSON>", "powerRange": "<PERSON><PERSON>", "range": "Doseg", "singlePhase": "Enofazni", "soc": "Polnjenje", "solarForecast": "Napo<PERSON> sončne energije", "temp": "Temperatura", "topic": "Topic", "url": "URL", "vehicleLimitSoc": "<PERSON><PERSON><PERSON><PERSON> vozila", "yes": "da"}, "deviceValueChargeStatus": {"A": "A (brez povezave)", "B": "B (povezan)", "C": "C (polnjenje)"}, "devices": {"auxMeter": "Pametni porabnik", "batteryStorage": "Baterijski hranilnik", "solarSystem": "Solarni sistem"}, "editor": {"loading": "Nalaganje urejevalnika YAML …"}, "eebus": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki evcc omogoča komunikacijo z drugimi napravami EEBus.", "title": "EEBus"}, "ext": {"description": "Lahko se uporablja za upravljanje obremenitve ali statistične namene.", "titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>"}, "form": {"danger": "<PERSON><PERSON><PERSON><PERSON>", "deprecated": "deprecated", "example": "Primer", "optional": "neobvezno"}, "general": {"cancel": "Prekliči", "delete": "Izbriši", "docsLink": "Oglejte si dokumentacijo.", "experimental": "Eksperimentalno", "hideAdvancedSettings": "Skrij napredne nastavitve", "off": "izklopljeno", "on": "vklopljeno", "password": "<PERSON><PERSON><PERSON>", "readFromFile": "Preberi iz datoteke", "remove": "Odstrani", "save": "<PERSON><PERSON><PERSON>", "showAdvancedSettings": "Prikaži napredne nastavitve", "telemetry": "Telemetrija", "title": "Ime", "validateSave": "Preveri in shrani"}, "grid": {"title": "Omrežni meter", "titleAdd": "Dodaj števec električnega omrežja", "titleEdit": "Uredi števec električnega omrežja"}, "hems": {"description": "Povežite evcc z drugim sistemom za upravljanje z energijo doma.", "title": "HEMS"}, "influx": {"description": "Zapisuje podatke o polnjenju in druge meritve v InfluxDB. Za vizualizacijo podatkov uporabite Grafano ali druga orodja.", "descriptionToken": "Preverite dokumentacijo InfluxDB, če želite izvedeti, kako ga ustvariti. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Dovoli samopodpisane certifikate", "labelDatabase": "<PERSON><PERSON>", "labelInsecure": "Preverjanje certifikata", "labelOrg": "Organization", "labelPassword": "<PERSON><PERSON><PERSON>", "labelToken": "API Žeton", "labelUrl": "URL", "labelUser": "Uporabniško ime", "title": "InfluxDB", "v1Support": "Potrebujete podporo za InfluxDB 1.x?", "v2Support": "Nazaj na InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Dodaj polnilnik"}, "addMeter": "Dodaj namenski merilnik za polnilnico", "cancel": "Prekliči", "chargerLabel": {"charging": "Polnilnica"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Uporabilo se bo tokovno območje od 6 do 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Uporabilo se bo tokovno območje od 6 do 32 A.", "chargerPowerCustom": "drugo", "chargerPowerCustomHelp": "Določite drug obseg toka.", "chargerTypeLabel": "Vrsta polnilnice", "chargingTitle": "Polnjenje", "circuitLabel": "<PERSON><PERSON>", "circuitUnassigned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultModeHelp": {"charging": "<PERSON><PERSON>in polnjenja pri priklopu vozila."}, "defaultModeHelpKeep": "Ohrani zadnji izbrani način polnjenja.", "defaultModeLabel": "Privzeti način", "delete": "Izbriši", "electricalSubtitle": "Če ste v dvomih, vprašajte svojega električarja.", "electricalTitle": "Elektrika", "energyMeterHelp": "Dodat<PERSON>, če polnilec nima vgrajenega.", "energyMeterLabel": "Energijski števec", "estimateLabel": "Interpoliraj raven zaračunavanja med posodobitvami API-ja", "maxCurrentHelp": "Mora biti večji od minimalnega toka.", "maxCurrentLabel": "Maksimalni tok", "minCurrentHelp": "Pod 6 A pojdi samo, <PERSON><PERSON> ve<PERSON>, kaj del<PERSON>.", "minCurrentLabel": "Minimalni tok", "noVehicles": "Nobeno vozilo ni konfigurirano.", "phases1p": "1-faz<PERSON>", "phases3p": "3-faz<PERSON>", "phasesAutomatic": "Samodejne faze", "phasesAutomaticHelp": "Vaš polnilnik podpira samodejno preklapljanje med 1- in 3-faznim polnjenjem. Na glavnem zaslonu lahko prilagodite obnašanje faze med polnjenjem.", "phasesHelp": "Število faz, povezanih s polnilnikom.", "phasesLabel": "Faze", "pollIntervalHelp": "Čas med posodobitvami API-ja vozila. Kratki intervali lahko izpraznijo akumulator vozila.", "pollIntervalLabel": "Interval posodabljanja", "pollModeAlways": "vedno", "pollModeAlwaysHelp": "Vedno zahtevajte posodobitve stanja v rednih intervalih.", "pollModeCharging": "polnjenje", "pollModeChargingHelp": "Zahtevaj posodobitve statusa vozila samo med polnjenjem.", "pollModeConnected": "povezan", "pollModeConnectedHelp": "Ko je vzpostavljena povezava redno posodobi stanje vozila.", "pollModeLabel": "<PERSON><PERSON><PERSON><PERSON>", "priorityHelp": "Prioritetne polnilnice imajo prednostni dostop do sončnega presežka.", "priorityLabel": "Prioriteta", "save": "<PERSON><PERSON><PERSON>", "showAllSettings": "Prikaži vse nastavitve", "solarBehaviorCustomHelp": "Določite lastne pragove za omogočanje in onemogočanje ter zakasnitve.", "solarBehaviorDefaultHelp": "Polnite samo s solarnim presežkom. Začnite po {enableDelay} presežka. Ustavite, ko ni dovolj presežka za {disableDelay}.", "solarBehaviorLabel": "Sončno vedenje", "solarModeCustom": "po meri", "solarModeMaximum": "maks<PERSON>lno sončno polnjenje", "thresholdDisableDelayLabel": "Onemogoči zakasnitev", "thresholdDisableHelpInvalid": "Prosimo, uporabite pozitivno vrednost.", "thresholdDisableHelpPositive": "Ustavite polnjenje, ko se porabi več kot {power} iz omrežja za {delay}.", "thresholdDisableHelpZero": "<PERSON><PERSON><PERSON> poln<PERSON>n<PERSON>, ko najmanjše moči polnjenja ni mogoče doseči za {delay}.", "thresholdDisableLabel": "Onemogoči napajanje iz omrežja", "thresholdEnableDelayLabel": "Omogoči zakasnitev", "thresholdEnableHelpInvalid": "Prosimo, uporabite negativno vrednost.", "thresholdEnableHelpNegative": "Začnite polniti, ko bo {surplus} presežek na voljo za {delay}.", "thresholdEnableHelpZero": "<PERSON><PERSON><PERSON><PERSON>, ko je na voljo minimalni presežek polnilne moči za {delay}.", "thresholdEnableLabel": "Omogoči polnjenje iz omrežja", "titleAdd": "<PERSON><PERSON><PERSON> pol<PERSON>o mesto", "titleEdit": "Uredi polnilno mesto", "titleExample": "Garaža, nadstrešek za avto itd.", "titleLabel": "Ime", "vehicleAutoDetection": "samodejno zaznavanje", "vehicleHelpAutoDetection": "Samodejno izbere najbolj verjetno vozilo. Možen je ročni popravek.", "vehicleHelpDefault": "<PERSON><PERSON><PERSON> do<PERSON>, da se to vozilo polni tukaj. Samodejno zaznavanje je onemogočeno. Možen je ročni popravek.", "vehicleLabel": "Privzeto vozilo", "vehiclesTitle": "Voz<PERSON>"}, "main": {"addGrid": "Dodaj števec električnega omrežja", "addLoadpoint": "<PERSON><PERSON><PERSON> pol<PERSON>o mesto", "addPvBattery": "<PERSON><PERSON><PERSON> celice ali baterijo", "addTariffs": "<PERSON><PERSON><PERSON> tarife", "addVehicle": "<PERSON><PERSON><PERSON> voz<PERSON>", "configured": "konfigurirano", "edit": "u<PERSON>i", "name": "Ime", "title": "Konfiguracija", "unconfigured": "ni konfigurirano", "vehicles": "Moja vozila", "yaml": "Naprav iz evcc.yaml ni mogoče urejati."}, "messaging": {"description": "Prejemanje sporočil o vaših sejah polnjenja.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "meter": {"cancel": "Prekliči", "delete": "Izbriši", "save": "<PERSON><PERSON><PERSON>", "template": "Proizvajalec", "titleChoice": "<PERSON><PERSON><PERSON> do<PERSON>?", "validateSave": "Preveri in shrani"}, "modbusproxy": {"description": "Dovoli več odjemalcem dostop do ene naprave Modbus.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Avtentikacija", "description": "Povežite se s posrednikom MQTT za izmenjavo podatkov z drugimi sistemi v vašem omrežju.", "descriptionClientId": "<PERSON>vtor sporočil. Če se pusti prazno, se uporabi `evcc-[rand]`.", "descriptionTopic": "<PERSON>ust<PERSON> praz<PERSON>, če želite onemogočiti objavljanje.", "labelBroker": "Posrednik", "labelCaCert": "Certif<PERSON><PERSON> (CA)", "labelCheckInsecure": "Dovoli samopodpisane certifikate", "labelClientCert": "<PERSON><PERSON><PERSON><PERSON><PERSON> stranke", "labelClientId": "ID Stranke", "labelClientKey": "K<PERSON><PERSON>č stranke", "labelInsecure": "Preverjanje certifikata", "labelPassword": "<PERSON><PERSON><PERSON>", "labelTopic": "Topic", "labelUser": "Uporabniško ime", "publishing": "Objavljanje", "title": "MQTT"}, "network": {"descriptionHost": "Uporabite pripono .local, da omogočite mDNS. Pomembno za odkrivanje mobilne aplikacije in nekaterih polnilnikov OCPP.", "descriptionPort": "Vrata za spletni vmesnik in API. Če spremenite, boste morali posodobiti URL v brskalniku.", "descriptionSchema": "Vpliva samo na to, kako so URL-ji ustvarjeni. Če izberete HTTPS, šifriranje ne bo omogočeno.", "labelHost": "<PERSON><PERSON>", "labelPort": "Port", "labelSchema": "<PERSON><PERSON>", "title": "Mreža"}, "options": {"boolean": {"no": "ne", "yes": "da"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (nešif<PERSON>rano)", "https": "HTTPS (šifrirano)"}, "status": {"A": "A (brez povezave)", "B": "B (povezan)", "C": "C (polnjenje)"}}, "pv": {"titleAdd": "Dodaj meril<PERSON> sončne energije", "titleEdit": "Uredi merilnik sončne energije"}, "section": {"general": "Splošno", "grid": "Omrežje", "integrations": "Integracije", "loadpoints": "Polnilna mesta", "meter": "Sončna energija in baterije", "system": "Sistem", "vehicles": "Voz<PERSON>"}, "sponsor": {"addToken": "Vnesite ž<PERSON>", "changeToken": "Spremeni žeton", "description": "Model sponzoriranja nam pomaga vzdrževati projekt in trajnostno graditi nove in vznemirljive funkcije. Kot sponzor dobite dostop do vseh izvedb polnilnikov.", "descriptionToken": "Žeton dobite na naslovu {url}. Ponujamo tudi poskusni žeton za testiranje.", "error": "Sponzorski žeton ni veljaven.", "labelToken": "Sponzorski žeton", "title": "Sponzorstvo", "tokenRequired": "Pred ustvarjanjem te naprave morate konfigurirati žeton sponzorja.", "tokenRequiredLearnMore": "Več o tem.", "trialToken": "Poskusni žeton"}, "system": {"logs": "<PERSON><PERSON>", "restart": "Ponovni zagon", "restartRequiredDescription": "<PERSON><PERSON><PERSON>, ponovno zaženite, da vidite uč<PERSON>k.", "restartRequiredMessage": "Konfiguracija je bila spremenjena.", "restartingDescription": "Prosim počakajte...", "restartingMessage": "Ponovni zagon evcc."}, "tariffs": {"description": "Določite svoje tarife za energijo, da izračunate stroške svojih polnilnih sej.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Prikazano na glavnem zaslonu in na zavihku brskalnika.", "label": "Ime", "title": "<PERSON><PERSON>i ime"}, "validation": {"failed": "Napaka", "label": "Status", "running": "preverjanje...", "success": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "neznano", "validate": "preveri"}, "vehicle": {"cancel": "Prekliči", "delete": "Izbriši", "generic": "Druge integracije", "online": "Vozila s spletnim API-jem", "save": "<PERSON><PERSON><PERSON>", "scooter": "<PERSON><PERSON><PERSON>", "template": "Proizvajalec", "titleAdd": "<PERSON><PERSON><PERSON> voz<PERSON>", "titleEdit": "<PERSON><PERSON><PERSON> vozilo", "validateSave": "Preveri in shrani"}}, "footer": {"community": {"greenEnergy": "Sončna energija", "greenEnergySub1": "napolnjeno z evcc", "greenEnergySub2": "od Oktobra 2022", "greenShare": "Sončni <PERSON>ž", "greenShareSub1": "napajanja zagotavlja", "greenShareSub2": "sončna in baterijska energija", "power": "Moč polnjenja", "powerSub1": "{activeClients} od {totalClients} u<PERSON><PERSON><PERSON><PERSON>v", "powerSub2": "trenutno polni…", "tabTitle": "Skupnost v živo"}, "savings": {"co2Saved": "{value} p<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2Title": "Emisije CO₂", "configurePriceCo2": "Naučite se konfigurirati podatke o ceni in CO₂.", "footerLong": "{percent} son<PERSON>ne energije", "footerShort": "{percent} son<PERSON>ne energije", "modalTitle": "Pregled energije polnjenja", "moneySaved": "{value} p<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentGrid": "{grid} kWh omrežja", "percentSelf": "{self} kWh sončne energije", "percentTitle": "Sončna energija", "period": {"30d": "zadnjih 30 dni", "365d": "zadnjih 365 dni", "thisYear": "letos", "total": "ves <PERSON>as"}, "periodLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "priceTitle": "<PERSON><PERSON> energi<PERSON>", "referenceGrid": "omrežje", "referenceLabel": "Referenčni podatki:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "<PERSON><PERSON><PERSON> sponzor", "becomeSponsorExtended": "Podprite nas direktno in pridobite nalepke.", "confetti": "Ste pripravljeni na konfete?", "confettiPromise": "Prejmete nalepke in digitalne konfete", "sticker": "… ali nalepke evcc?", "supportUs": "<PERSON><PERSON><PERSON> p<PERSON> je, da sončna energija postane norma. Pomagajte evcc tako, da pla<PERSON><PERSON>, kolikor je vredno za vas.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Vaš prispevek pomaga pri nadaljnjem razvoju evcc.", "titleNoSponsor": "Podprite nas", "titleSponsor": "Ste podpornik", "titleTrial": "Preizkusni način", "titleVictron": "Sponzorirano s strani Victron Energy", "trial": "Ste v preskusnem načinu in lahko uporabljate vse funkcije. Razmislite o podpori projekta.", "victron": "Uporabljate evcc na strojni opremi Victron Energy in imate dostop do vseh funkcij."}, "telemetry": {"optIn": "<PERSON><PERSON>m prispevati svoje podatke.", "optInMoreDetails": "<PERSON><PERSON><PERSON> informacij {0}.", "optInMoreDetailsLink": "tukaj", "optInSponsorship": "Potrebno je sponzoriranje."}, "version": {"availableLong": "na voljo je nova različica", "modalCancel": "Prekliči", "modalDownload": "Prenos", "modalInstalledVersion": "Trenutno nameščena različica", "modalNoReleaseNotes": "Opombe ob izdaji niso na voljo. Več informacij o novi različici:", "modalTitle": "Na voljo je nova različica", "modalUpdate": "<PERSON><PERSON>", "modalUpdateNow": "<PERSON><PERSON>", "modalUpdateStarted": "Zagon nove različice evcc…", "modalUpdateStatusStart": "Namestitev se je začela:"}}, "forecast": {"co2": {"average": "Povprečje", "lowestHour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "Doseg"}, "modalTitle": "<PERSON><PERSON><PERSON>", "price": {"average": "Povprečje", "lowestHour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "Doseg"}, "solar": {"dayAfterTomorrow": "Pojutrišnjem", "partly": "<PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>"}, "solarAdjust": "Prilagodite sončno napoved na podlagi dejanskih podatkov o proizvodnji.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Sončna energija"}}, "header": {"about": "Informacije", "blog": "Blog", "docs": "Dokumentacija", "github": "GitHub", "login": "Prijave v vozila", "logout": "<PERSON><PERSON><PERSON><PERSON>", "nativeSettings": "Zamenjaj strežnik", "needHelp": "Potrebuješ pomoč?", "sessions": "Seje p<PERSON>"}, "help": {"discussionsButton": "GitHub razprave", "documentationButton": "Dokumentacija", "issueButton": "<PERSON><PERSON><PERSON><PERSON>o", "issueDescription": "Ste našli čudno ali napačno vedenje?", "logsButton": "Prikaži loge", "logsDescription": "Preveri loge za napake.", "modalTitle": "Potrebuješ pomoč?", "primaryActions": "Nekaj ne deluje tako, kot bi moralo? Na teh mestih lahko poiščete pomoč.", "restart": {"cancel": "Prekliči", "confirm": "Da, ponovno zaženi!", "description": "V normalnih okoliščinah ponovni zagon ne bi smel biti potreben. Prosimo, razmislite o prijavi napake, če morate redno ponovno zaganjati evcc.", "disclaimer": "Opomba: evcc bo prenehal delovati in se bo za ponovni zagon storitve zanašal na operacijski sistem.", "modalTitle": "<PERSON>, da <PERSON><PERSON><PERSON> znova zagnati?"}, "restartButton": "Ponovni zagon", "restartDescription": "Ste poskusili izklopiti in znova vklopiti?", "secondaryActions": "Še vedno ne morete rešiti svoje težave? Tukaj je nekaj zahtevnejših možnosti."}, "log": {"areaLabel": "Filtriraj glede na območje", "areas": "Vsa območja", "download": "Prenesi vse loge", "levelLabel": "Filtriraj po ravni logov", "nAreas": "{count} ob<PERSON><PERSON><PERSON><PERSON>", "noResults": "Ni ujemajočih vnosov v logih.", "search": "<PERSON><PERSON><PERSON>", "selectAll": "izberi vse", "showAll": "Pokaži vse vnose", "title": "<PERSON><PERSON>", "update": "Samodejno posodabljanje"}, "loginModal": {"cancel": "Prekliči", "error": "Prijava ni uspela: ", "iframeHint": "Odpri evcc v novem zavihku.", "iframeIssue": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON> je <PERSON>, venda<PERSON> se <PERSON>di, da je vaš brskalnik izpustil piškotek za preverjanje pristnosti. To se lahko zgodi, če zaženete evcc v iframe prek HTTP.", "invalid": "Geslo ni veljavno.", "login": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON> g<PERSON>?", "title": "Avtentikacija"}, "main": {"chargingPlan": {"active": "Aktivno", "addRepeatingPlan": "Dodaj ponavljajoči načrt", "arrivalTab": "Prihod", "day": "<PERSON>", "departureTab": "<PERSON><PERSON><PERSON>", "goal": "Cilj p<PERSON>n<PERSON>nja", "modalTitle": "Načrt polnjenja", "none": "brez", "planNumber": "Načrt {number}", "remove": "Odstrani", "repeating": "ponav<PERSON><PERSON><PERSON><PERSON><PERSON>", "repeatingPlans": "Ponavljajoči načrti", "selectAll": "Izberi vse", "time": "Čas", "title": "Načrt", "titleMinSoc": "<PERSON><PERSON>", "titleTargetCharge": "<PERSON><PERSON><PERSON>", "unsavedChanges": "Obstajajo neshranjene spremembe. Želite uporabiti zdaj?", "update": "Uporabi", "weekdays": "Dnevi"}, "energyflow": {"battery": "Baterija", "batteryCharge": "Polnjenje baterije", "batteryDischarge": "Praznjenje <PERSON>", "batteryGridChargeActive": "polnjenje iz omrežja aktivno", "batteryGridChargeLimit": "polnjenje iz omrežja ko", "batteryHold": "Baterija (zaklenjena)", "batteryTooltip": "{energy} od {total} ({soc})", "forecastTooltip": "napoved: preostala sončna proizvodnja danes", "gridImport": "Uvoz iz omrežja", "homePower": "Poraba objekta", "loadpoints": "Polnilnica | Polnilnici | {count} polnilnih mest", "noEnergy": "Ni podatkov o merilniku", "pvExport": "Izvoz v omrežje", "pvProduction": "Proizvodnja", "selfConsumption": "Samoporaba"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON>", "vehicleLimit": "<PERSON><PERSON><PERSON><PERSON>", "waitForVehicle": "Pripravljen. Čakam na grelec…"}, "loadpoint": {"avgPrice": "⌀ Cena", "charged": "Napolnjeno", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON><PERSON><PERSON>", "fallbackName": "Polnilno mesto", "power": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>", "remoteDisabledHard": "{source}: izkl<PERSON>čeno", "remoteDisabledSoft": "{source}: izklopljeno prilagodljivo solarno polnjenje", "solar": "Sončna energija"}, "loadpointSettings": {"batteryBoost": {"description": "Hitro polnjenje iz domače baterije.", "label": "Pospeševanje baterije", "mode": "Na voljo samo v solarnem in min+solarnem načinu.", "once": "Pospeševanje z baterijo aktivno za to sejo polnjenja."}, "batteryUsage": "Domača baterija", "currents": "Polnilni tok", "default": "privzeto", "disclaimerHint": "Opomba:", "limitSoc": {"description": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, ki se <PERSON><PERSON><PERSON><PERSON>, ko je to vozilo pove<PERSON>.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "maxCurrent": {"label": "Maks. tok"}, "minCurrent": {"label": "<PERSON>. tok"}, "minSoc": {"description": "Za nujne primere. <PERSON><PERSON><PERSON> »hitro« napolni na {0} od vse razpoložljive solarne energije, nato pa nadaljuje samo s solarnim presežkom.", "label": "<PERSON>. p<PERSON> %"}, "onlyForSocBasedCharging": "Te možnosti so na voljo samo za vozila z znano stopnjo napolnjenosti.", "phasesConfigured": {"label": "Faze", "no1p3pSupport": "<PERSON><PERSON> je priključena vaša polnilnica?", "phases_0": "samo<PERSON><PERSON><PERSON>", "phases_1": "1 faza", "phases_1_hint": "({min} do {max})", "phases_3": "3 faze", "phases_3_hint": "({min} do {max})"}, "smartCostCheap": "Poceni polnjenje iz omrežja", "smartCostClean": "Čisto polnjenje iz omrežja", "title": "Nastavitve {0}", "vehicle": "<PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+Sonce", "now": "<PERSON><PERSON>", "off": "<PERSON><PERSON><PERSON><PERSON>", "pv": "<PERSON><PERSON>", "smart": "Pametno"}, "provider": {"login": "pri<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON>"}, "startConfiguration": "Začnimo s konfiguracijo", "targetCharge": {"activate": "Aktiviraj", "co2Limit": "Omejitev CO₂ {co2}", "costLimitIgnore": "V tem obdobju se nastavljena {limit} ne bo upoš<PERSON>val<PERSON>.", "currentPlan": "Aktivni načrt", "descriptionEnergy": "Do kdaj želite da se vozilo napolni za {targetEnergy}?", "descriptionSoc": "<PERSON><PERSON><PERSON>, da je vozilo <PERSON> na {targetSoc}?", "inactiveLabel": "Ciljni čas", "nextPlan": "Naslednji načrt", "notReachableInTime": "<PERSON><PERSON><PERSON> bo <PERSON> {overrun} pozneje.", "onlyInPvMode": "Načrt polnjenja deluje samo v solarnem načinu.", "planDuration": "Čas polnjenja", "planPeriodLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planPeriodValue": "{start} do {end}", "planUnknown": "še ni znano", "preview": "Predogled načrta", "priceLimit": "<PERSON><PERSON><PERSON><PERSON> {price}", "remove": "Odstrani", "setTargetTime": "brez", "targetIsAboveLimit": "Konfigurirana omejitev polnjenja {limit} se ne bo upoštevala v tem obdobju.", "targetIsAboveVehicleLimit": "Omejitev vozila je pod ciljem polnjenja.", "targetIsInThePast": "Izberi čas v prihodnosti, Marty.", "targetIsTooFarInTheFuture": "Načrt bomo prilagodili takoj, ko bomo izvedeli več o novi funkcionalnosti.", "title": "Ciljni čas", "today": "danes", "tomorrow": "jutri", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "Naučite se konfigurirati.", "vehicleCapacityRequired": "Za oceno časa polnjenja je potrebna kapaciteta baterije vozila."}, "targetChargePlan": {"chargeDuration": "Čas polnjenja", "co2Label": "Emisije CO₂ ⌀", "priceLabel": "<PERSON><PERSON> energi<PERSON>", "timeRange": "{day} {range} h", "unknownPrice": "še vedno neznano"}, "targetEnergy": {"label": "<PERSON><PERSON><PERSON><PERSON>", "noLimit": "brez"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON> voz<PERSON>", "changeVehicle": "Zamenjaj vozilo", "detectionActive": "Zaznavanje vozila...", "fallbackName": "<PERSON><PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON>", "none": "<PERSON> vozila", "notReachable": "Vozilo ni bilo dosegljivo. Poskusite znova zagnati evcc.", "targetSoc": "<PERSON><PERSON><PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Temp. limit", "unknown": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "vehicleSoc": "Napolnjeno"}, "vehicleStatus": {"awaitingAuthorization": "Čakanje na avtorizacijo.", "batteryBoost": "Pospeševanje z baterijo aktivno", "charging": "Polnjen<PERSON>…", "cheapEnergyCharging": "Poceni energija je na voljo.", "cheapEnergyNextStart": "Poceni energija čez {duration}.", "cheapEnergySet": "Nastavljena je omejitev cene.", "cleanEnergyCharging": "Čista energija je na voljo.", "cleanEnergyNextStart": "Čista energija na voljo čez {duration}.", "cleanEnergySet": "Omejitev CO₂ je nastavljena.", "climating": "Zaznano predkondicioniranje.", "connected": "<PERSON><PERSON><PERSON>.", "disconnectRequired": "Seja prekinjena. Ponovno se povežite.", "disconnected": "Odklopljen.", "finished": "Končano.", "minCharge": "Minimalno polnjenje na {soc}.", "pvDisable": "Premalo presežka. Prekinitev polnjenja se bo izvedla kmalu.", "pvEnable": "Na voljo so presežki. Začetek polnjenja se bo izvedel kmalu.", "scale1p": "Zmanjšanje na 1-fazno napajanje kmalu...", "scale3p": "Povečanje na 3-fazno napajanje kmalu...", "targetChargeActive": "<PERSON><PERSON>jno polnjenje je aktivno, predviden konec {duration}.", "targetChargePlanned": "Ciljno polnjenje se začne ob {time}.", "targetChargeWaitForVehicle": "Ciljno polnjenje je pripravljeno. Čakam na vozilo ...", "vehicleLimit": "<PERSON><PERSON><PERSON><PERSON> vozila", "vehicleLimitReached": "<PERSON><PERSON><PERSON><PERSON> voz<PERSON>.", "waitForVehicle": "Pripravljen. Čakam na vozilo...", "welcome": "Kratko začetno polnjenje za potrditev povezave."}, "vehicles": "Parkiranje", "welcome": "Pozdravljeni na krovu!"}, "notifications": {"dismissAll": "<PERSON><PERSON> vse", "logs": "Prikaži celotne loge", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "offline": {"configurationError": "Napaka med zagonom. Preverite konfiguracijo in znova zaženite.", "message": "Ni povezave s strežnikom.", "restart": "Ponovni zagon", "restartNeeded": "Potrebno za uveljavitev sprememb.", "restarting": "Strežnik bo kmalu nazaj."}, "passwordModal": {"description": "Nastavite geslo za zaščito konfiguracijskih nastavitev. Uporaba glavnega vmesnika je še vedno možna brez prijave.", "empty": "G<PERSON>lo ne sme biti prazno", "error": "Napaka: ", "labelCurrent": "Trenutno geslo", "labelNew": "Novo geslo", "labelRepeat": "<PERSON>novi geslo", "newPassword": "Ustvar<PERSON> g<PERSON>lo", "noMatch": "G<PERSON><PERSON> se ne ujema", "titleNew": "Nastavi skrbniško geslo", "titleUpdate": "Posodobi skrbniško geslo", "updatePassword": "Posodobi geslo"}, "session": {"cancel": "Prekliči", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Izbriši", "finished": "<PERSON><PERSON><PERSON><PERSON>", "meter": "<PERSON><PERSON><PERSON>", "meterstart": "Začetek merilnika", "meterstop": "Zaključek merilnika", "odometer": "Število prevoženih kilometrov", "price": "<PERSON><PERSON>", "started": "Začetek", "title": "Seja polnjenja"}, "sessions": {"avgPower": "⌀ <PERSON><PERSON>", "avgPrice": "⌀ Cena", "chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Cena {byGroup}", "byGroupLoadpoint": "po polnilnem mestu", "byGroupVehicle": "po vozilu", "energy": "Napolnjena energija", "energyGrouped": "Sončna energija v primerjavi z omrežno energijo", "energyGroupedByGroup": "Energija {byGroup}", "energySubSolar": "{value} son<PERSON>na energija", "energySubTotal": "{value} skupaj", "groupedCo2ByGroup": "CO₂-Količina {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> {byGroup}", "historyCo2": "CO₂-Emisije", "historyCo2Sub": "{value} skupaj", "historyPrice": "Stroški polnjenja", "historyPriceSub": "{value} skupaj", "solar": "<PERSON><PERSON>ž <PERSON>ez leto", "solarByGroup": "<PERSON><PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energija (kWh)", "chargeduration": "<PERSON><PERSON><PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "Ustvarjeno", "finished": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Polnilno mesto", "meterstart": "Začetek merjenja (kWh)", "meterstop": "Konec merjenja (kWh)", "odometer": "Prevoženi kilometri (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Cena/kWh", "solarpercentage": "Sončna energija (%)", "vehicle": "<PERSON><PERSON><PERSON>"}, "csvPeriod": "Prenesi {period} CSV", "csvTotal": "Prenesi celoten CSV", "date": "Začetek", "energy": "Napolnjeno", "filter": {"allLoadpoints": "vsa polnilna mesta", "allVehicles": "vsa vozila", "filter": "Filter"}, "group": {"co2": "Emisije", "grid": "Omrežje", "price": "<PERSON><PERSON>", "self": "Sončna energija"}, "groupBy": {"loadpoint": "Polnilno mesto", "none": "S<PERSON><PERSON><PERSON>", "vehicle": "<PERSON><PERSON><PERSON>"}, "loadpoint": "Polnilno mesto", "noData": "Ta mesec ni bilo sej polnjenja.", "overview": "Pregled", "period": {"month": "Mesec", "total": "S<PERSON><PERSON><PERSON>", "year": "Leto"}, "price": "<PERSON><PERSON>", "reallyDelete": "Ali res želite izbrisati to sejo?", "showIndividualEntries": "Prikaži posamezne seje", "solar": "Sončna energija", "title": "Seje p<PERSON>", "total": "S<PERSON><PERSON><PERSON>", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Sončna energija"}, "vehicle": "<PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Vstopi v celozaslonski način", "exit": "Izhod iz celozaslonskega načina", "label": "Celozaslonski način"}, "hiddenFeatures": {"label": "Eksperimentalno", "value": "Prikaži eksperimentalne funkcije uporabniškega vmesnika."}, "language": {"auto": "Samodejno", "label": "<PERSON><PERSON><PERSON>"}, "sponsorToken": {"expires": "Sponzorskemu žetonu poteče veljavnost čez {inXDays}. {getNewToken} in ga posodobite tukaj.", "getNew": "Zgrabi svežega", "hint": "Opomba: To bomo v prihodnosti avtomatizirali."}, "telemetry": {"label": "Telemetrija"}, "theme": {"auto": "sistem", "dark": "temno", "label": "<PERSON><PERSON><PERSON>", "light": "s<PERSON><PERSON>"}, "title": "Uporabniški vmesnik", "unit": {"km": "km", "label": "<PERSON><PERSON>", "mi": "milje"}}, "smartCost": {"activeHours": "{active} od {total}", "activeHoursLabel": "Aktivne ure", "applyToAll": "Uporabi povsod?", "batteryDescription": "Napolni domačo baterijo z energijo iz omrežja.", "cheapTitle": "Poceni polnjenje iz omrežja", "cleanTitle": "Čisto polnjenje iz omrežja", "co2Label": "Emisije CO₂", "co2Limit": "<PERSON><PERSON><PERSON><PERSON>", "loadpointDescription": "Omogoči začasno hitro polnjenje v solarnem načinu.", "modalTitle": "Smart Grid <PERSON>", "none": "brez", "priceLabel": "<PERSON><PERSON> energi<PERSON>", "priceLimit": "<PERSON><PERSON><PERSON><PERSON> cene", "resetAction": "Odstrani omejitev", "resetWarning": "Dinamična cena omrežja ali vir CO₂ ni konfiguriran. <PERSON><PERSON> vedno pa obstaja omejitev {limit}. <PERSON> želi<PERSON> poč<PERSON>iti konfiguracijo?", "saved": "Shranjeno."}, "startupError": {"configFile": "Uporabljena konfiguracijska datoteka:", "configuration": "Konfiguracija", "description": "Preverite konfiguracijsko datoteko. Če sporočilo o napaki ne pomaga, preverite {0}.", "discussions": "GitHub razprave", "fixAndRestart": "Prosim, odpravite težavo in znova zaženite strežnik.", "hint": "Opomba: <PERSON><PERSON><PERSON>, da imate tudi ok<PERSON>ravo (inverter, števec, ...). Preverite omrežne povezave.", "lineError": "Napaka v {0}.", "lineErrorLink": "v<PERSON><PERSON> {0}", "restartButton": "Ponovni zagon", "title": "Napaka pri zagonu"}}