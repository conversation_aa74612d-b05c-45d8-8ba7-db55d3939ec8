{"batterySettings": {"batteryLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufferStart": {"above": "{soc} bereikt heeft.", "full": "wanneer op {soc}.", "never": "enkel bij voldo<PERSON>e overschot."}, "capacity": "{energy} van {total}", "control": "Batterijsturing", "discharge": "Voorkom het ontladen van de thuisaccu bij gebruik van de snelle modus of bij gepland laden.", "disclaimerHint": "Opgelet:", "disclaimerText": "Deze instellingen beïnvloeden enkel de zonnemodus. Oplaadgedrag wordt overeenkomstig aangepast.", "gridChargeTab": "Netladen", "legendBottomName": "Thuisaccu heeft prioriteit", "legendBottomSubline": "tot {soc} bereikt is.", "legendMiddleName": "Voertuig laden heeft prioriteit", "legendMiddleSubline": "als de thuisaccu {soc} bereikt heeft.", "legendTopAutostart": "<PERSON>den vanuit de thuisbatterij eens de batterij", "legendTopName": "<PERSON><PERSON><PERSON> met energie uit de thuisaccu", "legendTopSubline": "stoppen wanneer de thuisaccu {soc} bereikt.", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usageTab": "Batterijgebruik"}, "config": {"aux": {"description": "Apparaat dat zijn verbruik aanpast op basis van het beschikbare overschot (zoals slimme boilers). EVCC verwacht dat dit apparaat het stroomverbruik vermindert als dat nodig is.", "titleAdd": "Zelfregulerende verbruiker toevoegen", "titleEdit": "Zelfregulerende verbruiker bewerken"}, "battery": {"titleAdd": "Voeg accu toe", "titleEdit": "Accu aanpassen"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON> toe", "titleEdit": "Bewerk Lader Meter"}, "charger": {"chargers": "EV laders", "generic": "Algemene integraties", "heatingdevices": "Warmtepompen", "ocppHelp": "<PERSON><PERSON><PERSON> dit adres in de configuratie van jouw lader.", "ocppLabel": "OCPP-Server URL", "switchsockets": "schakelbare stopcontacten", "template": "<PERSON>abrikant", "titleAdd": {"charging": "<PERSON><PERSON><PERSON> toe", "heating": "Voeg verwarmer toe"}, "titleEdit": {"charging": "Editeer <PERSON>", "heating": "Pas ver<PERSON>er aan"}, "type": {"custom": {"charging": "Gebruikersgedefinieerde lader", "heating": "Gebruikersgedefinieerde verwarmer"}, "heatpump": "Gebruikersgedefinieerde warmtepomp", "sgready": "Gebruikersgedefinieerde warmtepomp (sg-ready, alle)", "sgready-boost": "Gebruikersgedefinieerde warmtepomp (sg-ready, boost)", "switchsocket": "Gebruikersgedefinieerd scha<PERSON><PERSON> stopcontact"}}, "circuits": {"description": "<PERSON><PERSON><PERSON> ervoor dat de som van alle laadpunten die zijn aangesloten op een circuit de ingestelde vermogens- en stroomlimieten niet overschrijdt. Circuits kunnen genest worden om een hiërarchie op te bouwen.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "control": {"description": "Normaalgesproken zijn de standaardwaardes in orde. Pas deze alleen aan als je weet wat je doet.", "descriptionInterval": "Updatecyclus van de regeling in seconden. Bepaalt hoe vaak evcc metergegevens uitlee<PERSON>, het laadvermogen aanpast en de gebruikersinterface bijwerkt. Korte intervallen (< 30s) kunnen oscillaties en ongewenst gedrag veroorzaken.", "descriptionResidualPower": "Verplaatst het werkpunt van de regelkring. Als je een thuisbatterij hebt, wordt aanbevolen om een waarde van 100 W in te stellen. Op deze manier krijgt de batterij een lichte prioriteit boven het gebruik van het net.", "labelInterval": "Update interval", "labelResidualPower": "Vermogensoverschot", "title": "Regelgedrag"}, "deviceValue": {"amount": "Aantal", "broker": "Agent", "bucket": "Bucket", "capacity": "Capaciteit", "chargeStatus": "Status", "chargeStatusA": "niet verbonden", "chargeStatusB": "verbonden", "chargeStatusC": "aan het opladen", "chargeStatusE": "geen vermogen", "chargeStatusF": "error", "chargedEnergy": "Geladen", "co2": "Net-CO₂", "configured": "Geconfigureerd", "controllable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency": "Valuta", "current": "Stroomsterkte", "currentRange": "Stroomsterkte", "enabled": "Ingeschakeld", "energy": "Energie", "feedinPrice": "Terugleververgoeding", "gridPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heaterTempLimit": "Verwarming limiet", "hemsType": "Systeem", "identifier": "RFID-Nummer", "no": "nee", "odometer": "Kilometerstand", "org": "Organisatie", "phaseCurrents": "Stroomsterkte L1, L2, L3", "phasePowers": "Vermogen L1, L2, L3", "phaseVoltages": "Spanning L1, L2, L3", "phases1p3p": "Fase-omschakeling", "power": "Vermogen", "powerRange": "Vermogen", "range": "Actieradius", "singlePhase": "Monofase", "soc": "Laden", "solarForecast": "Voorspelling van zonne-energie", "temp": "Temperatuur", "topic": "Onderwerp", "url": "URL", "vehicleLimitSoc": "Voertuig limiet", "yes": "ja"}, "deviceValueChargeStatus": {"A": "A (niet a<PERSON>)", "B": "B (aangesloten)", "C": "C (opladen)"}, "devices": {"auxMeter": "<PERSON><PERSON> verb<PERSON>", "batteryStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solarSystem": "PV systeem"}, "editor": {"loading": "YAML editor laden…"}, "eebus": {"description": "Configuratie voor communicatie met andere EEBus apparaten.", "title": "EEBus"}, "ext": {"description": "Kan worden gebruikt voor belastingbeheer of statistische doeleinden.", "titleAdd": "<PERSON>terne Meter Toevoegen", "titleEdit": "Externe meter bewerken"}, "form": {"danger": "<PERSON><PERSON><PERSON>", "deprecated": "verouderd", "example": "Voorbeeld", "optional": "optioneel"}, "general": {"cancel": "<PERSON><PERSON><PERSON>", "customHelp": "<PERSON><PERSON><PERSON><PERSON> een geb<PERSON>r-gedefin<PERSON>erd apparaat met evcc’s plugin systeem.", "customOption": "Gebruiker-gedefinieerd apparaat", "delete": "Wissen", "docsLink": "<PERSON><PERSON> documentatie.", "experimental": "Experimenteel", "hideAdvancedSettings": "Verberg geavanceerde instellingen", "invalidFileSelected": "<PERSON><PERSON><PERSON><PERSON> bestand geselecteerd", "noFileSelected": "<PERSON><PERSON> bestand geselecteerd.", "off": "uit", "on": "aan", "password": "Wachtwoord", "readFromFile": "<PERSON>it bestand lezen", "remove": "Verwijderen", "save": "Opsla<PERSON>", "selectFile": "<PERSON><PERSON>", "showAdvancedSettings": "Toon geavance<PERSON>e instellingen", "telemetry": "Telemetrie", "templateLoading": "Laden...", "title": "Titel", "validateSave": "Valideren & opslaan"}, "grid": {"title": "Netmeter", "titleAdd": "Voeg netmeter toe", "titleEdit": "Bewerk netmeter"}, "hems": {"description": "Verbind evcc met een ander thuis-energie managementsysteem.", "title": "HEMS"}, "icon": {"change": "wij<PERSON>"}, "influx": {"description": "Schrijft laad-data en andere cijfers naar InfluxDB. Gebruik Grafana of andere tools om de data te visualiseren.", "descriptionToken": "Bekijk de InfluxDB documentatie om te zien hoe je er een maakt. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Sta zelfondertekende certificaten toe", "labelDatabase": "Database", "labelInsecure": "Certificaat validatie", "labelOrg": "Organisatie", "labelPassword": "Wachtwoord", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Gebruikersnaam", "title": "InfluxDB", "v1Support": "<PERSON><PERSON><PERSON> nodig voor InfluxDB 1.x?", "v2Support": "Terug naar InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Laadpunt toevoegen", "heating": "<PERSON>er<PERSON><PERSON>"}, "addMeter": "<PERSON><PERSON><PERSON><PERSON> van een toegewijde energiemeter", "cancel": "<PERSON><PERSON><PERSON>", "chargerError": {"charging": "Het configureren van een oplader is vereist.", "heating": "<PERSON><PERSON> verwarmer instellen is vereist."}, "chargerLabel": {"charging": "<PERSON><PERSON><PERSON>", "heating": "Verwarmingstoestel"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "<PERSON><PERSON> een stroom van 6 tot 16 A gebruiken.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "<PERSON><PERSON> een stroom van 6 tot 32 A gebruiken.", "chargerPowerCustom": "andere", "chargerPowerCustomHelp": "Definieer een aangepast stroombereik.", "chargerTypeLabel": "Type laadpunt", "chargingTitle": "Gedrag", "circuitHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON> van Load management om te voorkomen dat de vermogens- en stroomlimieten niet worden overschreden.", "circuitLabel": "Circuit", "circuitUnassigned": "niet toe<PERSON>", "defaultModeHelp": {"charging": "Laadmodus bij het aansluiten van een voertuig.", "heating": "Wordt ingesteld bij opstarten van het systeem."}, "defaultModeHelpKeep": "<PERSON><PERSON><PERSON> de <PERSON>t-geselecteerde modus.", "defaultModeLabel": "Standaard mode", "delete": "Wissen", "electricalSubtitle": "<PERSON><PERSON><PERSON><PERSON>, raad<PERSON><PERSON> een elektricien.", "electricalTitle": "Elektrisch", "energyMeterHelp": "Extra meter voor laadstations zonder geïntegreerde meter.", "energyMeterLabel": "Energiemeter", "estimateLabel": "“Interpoleer batterijpercentage tussen API updates”", "maxCurrentHelp": "<PERSON><PERSON> groter zijn dan de minimum stroom.", "maxCurrentLabel": "“Maximale stroom”", "minCurrentHelp": "Kies 6 A enkel wanneer je weet wat je aan het doen bent.", "minCurrentLabel": "“Minimale stroom”", "noVehicles": "<PERSON><PERSON> voe<PERSON><PERSON>gen ingesteld.", "option": {"charging": "Laadpunt toevoegen", "heating": "Verwarmingstoestel toevoegen"}, "phases1p": "“1-fase”", "phases3p": "“3-fase”", "phasesAutomatic": "“Automatische fasen”", "phasesAutomaticHelp": "Uw laadstation ondersteunt automatisch wisselen tussen laden op 1 of 3-fasen. U kunt dit aanpassen van op het startscherm tijdens de laadsessie.", "phasesHelp": "Aantal aangesloten fasen.", "phasesLabel": "“Fasen”", "pollIntervalDanger": "Het voertuig vaak bevragen kan de accu sneller ontladen. Sommige autofabrikanten kunnen mogelijk actief het laden voorkomen in dit geval. Niet aangeraden! Gebruik dit alleen wanneer je je bewust bent van de risico's.", "pollIntervalHelp": "Tijd tussen API-updates van voertuigen. Korte intervallen kunnen de accu van het voertuig leegmaken.", "pollIntervalLabel": "Update-interval", "pollModeAlways": "altijd", "pollModeAlwaysHelp": "Vraag altijd om regelmatige statusupdates.", "pollModeCharging": "laden", "pollModeChargingHelp": "Vraag alleen om updates van de voertuigstatus tijdens het opladen.", "pollModeConnected": "verbonden", "pollModeConnectedHelp": "Werk de voertuigstatus met regelmatige tussenpozen bij wanneer deze is aangesloten.", "pollModeLabel": "Update gedrag", "priorityHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met een hogere prioriteit krijgen voorrang bij zonne-energie overschotten.", "priorityLabel": "Prioriteit", "save": "Opsla<PERSON>", "showAllSettings": "Alle instellingen tonen", "solarBehaviorCustomHelp": "Definieer uw eigen in- en uitschakeldrempels en vertragingen.", "solarBehaviorDefaultHelp": "<PERSON><PERSON> alleen op met een overschot aan zonne-energie. Begin na {enableDelay} van overschot. Stop als er niet genoeg overschot is voor {disableDelay}.", "solarBehaviorLabel": "<PERSON><PERSON><PERSON>ne-energie", "solarModeCustom": "Aangepast", "solarModeMaximum": "Maximale zonne-energie", "thresholdDisableDelayLabel": "Vertraging uitschakelen", "thresholdDisableHelpInvalid": "Gebruik alstublieft een positieve waarde.", "thresholdDisableHelpPositive": "Stop met opladen wanneer er meer dan {stroom} van het net wordt gebruikt voor {vertraging}.", "thresholdDisableHelpZero": "Stop wanneer het minimale laadvermogen niet kan worden voldaan voor {delay}.", "thresholdDisableLabel": "<PERSON><PERSON>el netspanning uit", "thresholdEnableDelayLabel": "Vertraging inschakelen", "thresholdEnableHelpInvalid": "Gebruik aub een negatieve waarde.", "thresholdEnableHelpNegative": "<PERSON><PERSON> met opladen wanneer het overschot van {surplus} be<PERSON><PERSON><PERSON><PERSON> is voor {delay}.", "thresholdEnableHelpZero": "Begin wanneer het minimale overschot aan laadvermogen beschikbaar is voor {delay}.", "thresholdEnableLabel": "Netstroom inschakelen", "titleAdd": {"charging": "Laadpunt Toevoegen", "heating": "Verwarmingstoestel Toevoegen"}, "titleEdit": "Laadpunt bewerken", "titleExample": "Garage, carport, enz.", "titleLabel": "Titel", "vehicleAutoDetection": "automatische detectie", "vehicleHelpAutoDetection": "Selecteert automatisch het meest aannemelijke voertuig. Handmatige aanpassen is mogelijk.", "vehicleHelpDefault": "Ga er altijd vanuit dat dit voertuig hier aan het opladen is. Automatische detectie uitgeschakeld. Handmatige aanpassen is mogelijk.", "vehicleLabel": "Standaar<PERSON> voe<PERSON><PERSON>g", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "Extra meter toevoegen", "addGrid": "Netmeter toevoegen", "addLoadpoint": "Voeg laadpunt toe", "addPvBattery": "<PERSON><PERSON><PERSON> of batter<PERSON>j toe", "addTariffs": "<PERSON><PERSON><PERSON> toe<PERSON>n", "addVehicle": "Voeg voertuig toe", "configured": "gecon<PERSON><PERSON>reerd", "edit": "bewerken", "loadpointRequired": "Er moet tenminste één laadpunt worden geconfigureerd.", "name": "<PERSON><PERSON>", "title": "Configuratie", "unconfigured": "<PERSON><PERSON>", "vehicles": "<PERSON><PERSON>", "yaml": "Apparaat van evcc.yaml kan niet worden bewerkt."}, "messaging": {"description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> over je oplaadsessies.", "title": "Notificaties"}, "meter": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generic": "Generieke integraties", "option": {"aux": "Zelfregulerend<PERSON> consument toevoegen", "battery": "Bat<PERSON><PERSON>j<PERSON> toe<PERSON>n", "ext": "Externe meter toevoegen", "pv": "Zonne-energie meter toevoegen"}, "save": "Opsla<PERSON>", "specific": "Specifieke integraties", "template": "<PERSON>abrikant", "titleChoice": "Wat wil je toevoegen?", "validateSave": "Valideren en opslaan"}, "modbus": {"baudrate": "Baud rate", "comset": "ComSet", "connection": "Modbusverbinding", "connectionHintSerial": "Het apparaat is direct verbonden met evcc via een RS485 interface.", "connectionHintTcpip": "Het apparaat is aanspreekbaar vanuit evcc via LAN/WiFi.", "connectionValueSerial": "Serieel / USB", "connectionValueTcpip": "Netwerk", "device": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceHint": "Voorbeeld: /dev/ttyUSB0", "host": "IP-adres of hostnaam", "hostHint": "Voorbeeld: *********", "id": "Modbus ID", "port": "Poort", "protocol": "Modbusprotocol", "protocolHintRtu": "Verbinding via een RS485 naar Ethernet-adapter zonder protocolvertaling.", "protocolHintTcp": "Apparaat heeft ingebouwde LAN/WiFi-ondersteuning of is verbonden via een RS485 naar Ethernet-adapter met protocolvertaling.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Sta meerdere clients de toegang tot één Modbus apparaat toe.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Authenticatie", "description": "<PERSON><PERSON><PERSON><PERSON> met een MQTT broker om data met andere netwerksystemen uit te wisselen.", "descriptionClientId": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> leeg wordt `evcc-[rand]` geb<PERSON><PERSON>t.", "descriptionTopic": "Laat leeg om publicatie te deactiveren.", "labelBroker": "Agent", "labelCaCert": "Servercertificaat (CA)", "labelCheckInsecure": "St<PERSON> self-signed certificaten toe", "labelClientCert": "Clientcertificaat", "labelClientId": "Client ID", "labelClientKey": "Gebruikers-sleutel", "labelInsecure": "Certificaat validatie", "labelPassword": "Wachtwoord", "labelTopic": "Onderwerp", "labelUser": "Gebruikersnaam", "publishing": "Publiceren", "title": "MQTT"}, "network": {"descriptionHost": "Gebruik de .local toevoeging om mDNS te activeren. Voor toegang vanuit de mobiele app en sommige OCPP laders van belang.", "descriptionPort": "Poort voor de web interface en API. Update de browser url wanneer je dit aanpast.", "descriptionSchema": "Beïnvloedt enkel hoe URLs gegenereerd worden. HTTPS selecteren activeert geen encryptie.", "labelHost": "Hostname", "labelPort": "Poort", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Netwerk"}, "options": {"boolean": {"no": "nee", "yes": "ja"}, "endianness": {"big": "groot-endian", "little": "klein-endian"}, "schema": {"http": "HTTP (niet-versleuteld)", "https": "HTTPS (versleuteld)"}, "status": {"A": "A (niet verbonden)", "B": "B (verbonden)", "C": "C (opladen)"}}, "pv": {"titleAdd": "Voeg meter zonne<PERSON>elen toe", "titleEdit": "Bewerk meter zonnepanelen"}, "section": {"additionalMeter": "Extra meters", "general": "<PERSON><PERSON><PERSON><PERSON>", "grid": "Netaansluiting", "integrations": "Integraties", "loadpoints": "Laadpunten", "meter": "Zonnepanelen & accu", "system": "Systeem", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "Token invoeren", "changeToken": "Token bewerken", "description": "Het sponsormodel helpt ons het project te onderhouden en nieuwe toffe functies te ontwikkelen. Als sponsor krijg je toegang tot de implementaties voor alle laadpunten.", "descriptionToken": "Haal de token op vanaf {url}. We bieden ook een trial-token aan om te testen.", "error": "De sponsortoken is ongeldig.", "labelToken": "Sponsortoken", "title": "Sponsoring", "tokenRequired": "U moet een sponsortoken configureren voordat u dit apparaat kunt aan<PERSON>ken.", "tokenRequiredLearnMore": "Meer informatie.", "trialToken": "Tijdelijke token"}, "system": {"logs": "Logboeken", "restart": "Herstart", "restartRequiredDescription": "Herstart om het effect te zien.", "restartRequiredMessage": "Configurat<PERSON> g<PERSON>.", "restartingDescription": "Even geduld aub…", "restartingMessage": "Evcc wordt herstart."}, "tariffs": {"description": "<PERSON><PERSON><PERSON> jouw energietarief in om de kosten van het laden te berekenen.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Wordt weergegeven op het hoofdscherm en op het browsertabblad.", "label": "Titel", "title": "Wijzig titel"}, "validation": {"failed": "mislukt", "label": "Status", "running": "bezig met valideren…", "success": "succesvol", "unknown": "onbekend", "validate": "valideer"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "chargingSettings": "Laadinstellingen", "defaultMode": "Standaardmodus", "defaultModeHelp": "Laadmodus bij het aansluiten van een voertuig.", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generic": "Andere integraties", "identifiers": "RFID identificatiegegevens", "identifiersHelp": "Lijst van RFIDs om het voertuig te identificeren. Een RFID per regel. De huidige RFID kan gevonden worden op de overzichtspagina, bij het respectievelijke laadstation.", "maximumCurrent": "Maximale stroomsterkte", "maximumCurrentHelp": "<PERSON><PERSON> hoger zijn dan de minimale stroomsterkte.", "maximumPhases": "Maximaal aantal fases", "maximumPhasesHelp": "Met hoeveel fasen kan dit voertuig laden? Wordt gebruikt om de minimale duur van zon<PERSON>overscho<PERSON> en plan te berekenen.", "minimumCurrent": "Minimale stroomsterkte", "minimumCurrentHelp": "Stel alleen lager in dan 6A waneer je weet wat je doet.", "online": "Voertuig met online API", "primary": "Generieke integraties", "priority": "Prioriteit", "priorityHelp": "Hogere prioriteit betekent dat dit voertui<PERSON> met voor<PERSON> toegang heeft tot zonneoverschot.", "save": "Opsla<PERSON>", "scooter": "<PERSON>ooter", "template": "<PERSON>abrikant", "titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleEdit": "Voertuig bewerken", "validateSave": "Valideren & opslaan"}}, "footer": {"community": {"greenEnergy": "Zon", "greenEnergySub1": "<PERSON><PERSON><PERSON> met evcc", "greenEnergySub2": "sinds oktober 2022", "greenShare": "<PERSON><PERSON><PERSON> zon", "greenShareSub1": "van de stroom werd geleverd door", "greenShareSub2": "zon- en batterij-opslag", "power": "Laadvermogen", "powerSub1": "{activeClients} van {totalClients} deelnemers", "powerSub2": "laden…", "tabTitle": "Live community"}, "savings": {"co2Saved": "{value} be<PERSON>aard", "co2Title": "CO₂-uitstoot", "configurePriceCo2": "Meer informatie rond het configureren van prijs en CO₂ gegevens.", "footerLong": "{percent} zonne-energie", "footerShort": "{percent} zon", "modalTitle": "Oplaadenergie-overzicht", "moneySaved": "{value} be<PERSON>aard", "percentGrid": "{grid} kWh net", "percentSelf": "{self} kWh zon", "percentTitle": "Zonne-energie", "period": {"30d": "laatste 30 dagen", "365d": "laatste 365 dagen", "thisYear": "dit jaar", "total": "aller tijden"}, "periodLabel": "Periode:", "priceTitle": "Energieprijs", "referenceGrid": "net", "referenceLabel": "Referentiedata:", "tabTitle": "Mijn data"}, "sponsor": {"becomeSponsor": "Sponsor worden", "becomeSponsorExtended": "Steun ons direct om stickers te krijgen.", "confetti": "<PERSON><PERSON><PERSON> voor confetti?", "confettiPromise": "Je krijgt stickers en digitale confetti", "sticker": "... of evcc stickers?", "supportUs": "<PERSON><PERSON> missie is om van opladen via zonne-energie de norm te maken. Help evcc door te betalen wat het u waard is.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Je bijdrage helpt evcc verder te ontwikkelen.", "titleNoSponsor": "Sponsor ons", "titleSponsor": "Je bent een sponsor", "titleTrial": "Testmodus", "titleVictron": "Gesponsord door Victron Energy", "trial": "U bevindt zich in de proefmodus en kunt alle functies gebruiken. Overweeg alstublieft om het project te steunen.", "victron": "Je gebruikt evcc op Victron Energy-hardware en hebt toegang tot alle functies."}, "telemetry": {"optIn": "<PERSON>k wil mijn gegeven<PERSON> delen.", "optInMoreDetails": "Meer details {0}.", "optInMoreDetailsLink": "hier", "optInSponsorship": "Sponsoring nodig."}, "version": {"availableLong": "nieuwe versie besch<PERSON>", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "Download", "modalInstalledVersion": "Geïnstalleerde versie", "modalNoReleaseNotes": "Geen release notes beschikbaar. Meer info over de nieuwe versie:", "modalTitle": "Nieuwe versie besch<PERSON>ar", "modalUpdate": "Installeren", "modalUpdateNow": "<PERSON>u installeren", "modalUpdateStarted": "<PERSON><PERSON> van de nieuwe versie van evcc…", "modalUpdateStatusStart": "Installatie gestart:"}}, "forecast": {"co2": {"average": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lowestHour": "Schoonste uur", "range": "Be<PERSON>ik"}, "modalTitle": "Voorspelling", "price": {"average": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lowestHour": "Goedkoopste uur", "range": "Be<PERSON>ik"}, "solar": {"dayAfterTomorrow": "Overmorgen", "partly": "gedeeltelijk", "remaining": "resterend", "today": "Vandaag", "tomorrow": "<PERSON><PERSON>"}, "solarAdjust": "<PERSON><PERSON> <PERSON>ne<PERSON>pelling aan op basis van echte productiegegevens{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON><PERSON><PERSON>", "solar": "Zonne-energie"}}, "header": {"about": "Over", "blog": "Blog", "docs": "Documentatie", "github": "GitHub", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logout": "Uitloggen", "nativeSettings": "Andere server", "needHelp": "<PERSON>lp nodig?", "sessions": "Laadsessies"}, "help": {"discussionsButton": "GitHub discussies", "documentationButton": "Documentatie", "issueButton": "<PERSON>en bug melden", "issueDescription": "Vreemd of foutief gedrag gevonden?", "logsButton": "Bekijk logboeken", "logsDescription": "<PERSON><PERSON> in de logboeken voor foutmeldingen.", "modalTitle": "<PERSON>lp nodig?", "primaryActions": "Werkt iets niet naar behoren? Er zijn goede plaatsen om hulp te krijgen.", "restart": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Ja, herstarten!", "description": "Herstarten niet nodig onder normale omstandigheden. Overweeg een bug in te dienen als je evcc geregeld moet herstarten.", "disclaimer": "Nota: evcc zal afsluiten en rekenen op het besturingssysteem om de service te herstarten.", "modalTitle": "Ben je zeker dat je wil herstarten?"}, "restartButton": "Herstart", "restartDescription": "<PERSON><PERSON><PERSON><PERSON> het uit en weer aan te zetten?", "secondaryActions": "Nog niet gelukt om je probleem op te lossen? Hier zijn enkele hardhandige opties."}, "log": {"areaLabel": "Filter op domain", "areas": "Alle domainen", "download": "Volledig logboek downloaden", "levelLabel": "Filter op log level", "nAreas": "{count} geb<PERSON>en", "noResults": "<PERSON><PERSON> over<PERSON> log regels.", "search": "<PERSON><PERSON>", "selectAll": "alles selecteren", "showAll": "Toon alles", "title": "Logboeken", "update": "Automatisch updaten"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON>", "demoMode": "<PERSON>gin niet on<PERSON><PERSON><PERSON> in demo mode.", "error": "<PERSON><PERSON> mislukt: ", "iframeHint": "Open evcc in een nieuw tabblad.", "iframeIssue": "Uw wachtwoord is correct, maar het lijkt erop dat uw browser de authenticatiecookie heeft verwijderd. Dit kan gebeuren als u evcc via HTTP in een iframe uitvoert.", "invalid": "Wachtwoord onjuist.", "login": "Inloggen", "password": "Wachtwoord", "reset": "Wachtwoord resetten?", "title": "Authenticatie"}, "main": {"chargingPlan": {"active": "Actief", "addRepeatingPlan": "Herhalend schema toevoegen", "arrivalTab": "Aankomst", "day": "<PERSON><PERSON>", "departureTab": "<PERSON><PERSON><PERSON>", "goal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modalTitle": "Laad Plan", "none": "geen", "planNumber": "Schema {number}", "preconditionDescription": "Laad(duur) voor vertrek voor accu voorbereiding.", "preconditionLong": "Laat Opladen", "preconditionOptionAll": "alles", "preconditionOptionNo": "nee", "preconditionShort": "Laat", "remove": "Verwijderen", "repeating": "herhalend", "repeatingPlans": "Herhalende schema's", "selectAll": "Alles select<PERSON>en", "time": "Tijd", "title": "Plan", "titleMinSoc": "Min lading", "titleTargetCharge": "<PERSON><PERSON><PERSON>", "unsavedChanges": "Wijzigingen nog niet opgeslagen. Nu toepassen?", "update": "Toepassen", "weekdays": "Dagen"}, "energyflow": {"battery": "<PERSON><PERSON><PERSON><PERSON>", "batteryCharge": "<PERSON><PERSON><PERSON><PERSON>", "batteryDischarge": "<PERSON><PERSON><PERSON><PERSON>", "batteryGridChargeActive": "net laden actief", "batteryGridChargeLimit": "net laden wanneer", "batteryHold": "<PERSON><PERSON><PERSON><PERSON> (aanhouden)", "batteryTooltip": "{energy} van {total} ({soc})", "forecastTooltip": "Voorspelling: resterende zonneproductie voor vandaag", "gridImport": "Netafname", "homePower": "Consumptie", "loadpoints": "Lader| Lader | {count} laders", "noEnergy": "Geen meter data", "pv": "PV-systeem", "pvExport": "Netinjectie", "pvProduction": "<PERSON><PERSON>", "selfConsumption": "Zelfverbruik"}, "heatingStatus": {"charging": "<PERSON><PERSON> met ver<PERSON>en…", "connected": "Stand-by.", "vehicleLimit": "Verwarming limiet", "waitForVehicle": "<PERSON><PERSON><PERSON>. Wachten op verwarming…"}, "loadpoint": {"avgPrice": "⌀ Prijs", "charged": "Geladen", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON>", "fallbackName": "Laadpunt", "finished": "Eindtijd", "power": "Vermogen", "price": "<PERSON><PERSON>", "remaining": "Resterend", "remoteDisabledHard": "{source}: uitgeschakeld", "remoteDisabledSoft": "{source}: adaptief PV-laden uitgeschakeld", "solar": "Zon"}, "loadpointSettings": {"batteryBoost": {"description": "Snelladen vanuit thuisbatterij.", "label": "<PERSON><PERSON><PERSON><PERSON>", "mode": "<PERSON><PERSON> in PV en Min+PV modus.", "once": "Boost g<PERSON>erd voor deze laadsessie."}, "batteryUsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currents": "Laadstroom", "default": "standaard", "disclaimerHint": "Let op:", "limitSoc": {"description": "Standaard laadlimiet wanneer dit voertuig aangesloten wordt.", "label": "Standaard limiet"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Het voertuig wordt „snel” opgeladen tot {0} in deze modus. Het laden gaat daarna verder met zonneoverschot. Nuttig om een bepaald rijbereik te garanderen, ook gedurende periodes met we<PERSON>g of geen zonnestroom.", "label": "Min. batterij %"}, "onlyForSocBasedCharging": "Deze instellingen zijn alleen beschikbaar bij voertuigen waar het batterijpercentage bekend is.", "phasesConfigured": {"label": "Fasen", "no1p3pSupport": "Hoe is je lader aang<PERSON>en?", "phases_0": "automatisch wisselen van aantal fases", "phases_1": "1 fase", "phases_1_hint": "({min} tot {max})", "phases_3": "3 fase", "phases_3_hint": "({min} tot {max})"}, "smartCostCheap": "Goedkoop laden met netstroom", "smartCostClean": "<PERSON><PERSON> met scho<PERSON> energie van het net", "title": "Instellingen {0}", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+PV", "now": "Snel", "off": "Uit", "pv": "PV", "smart": "<PERSON>"}, "provider": {"login": "log in", "logout": "afmelden"}, "startConfiguration": "Laten we beginnen met de configuratie", "targetCharge": {"activate": "<PERSON><PERSON>", "co2Limit": "CO₂ limiet van {co2}", "costLimitIgnore": "De geconfigureerde {limit} zal genegeerd worden tijdens deze periode.", "currentPlan": "<PERSON><PERSON><PERSON><PERSON>", "descriptionEnergy": "Tegen wanneer moet {targetEnergy} in het voertuig geladen zijn?", "descriptionSoc": "<PERSON><PERSON> moet het voertuig geladen zijn naar {targetSoc}?", "goalReached": "<PERSON><PERSON> al bereikt", "inactiveLabel": "Ingestelde tijd", "nextPlan": "Volgende schema", "notReachableInTime": "<PERSON><PERSON><PERSON><PERSON> zal {overrun} later bere<PERSON><PERSON> zijn.", "onlyInPvMode": "Laadplan werkt alleen in PV modus.", "planDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planPeriodLabel": "Periode", "planPeriodValue": "{start} tot {end}", "planUnknown": "nog niet bekend", "preview": "Planning", "priceLimit": "prijslimiet van {price}", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setTargetTime": "geen", "targetIsAboveLimit": "De ingestelde laadlimiet {limit} wordt genegeerd gedurende deze periode.", "targetIsAboveVehicleLimit": "Voertuiglimiet is lager dan het laaddoel.", "targetIsInThePast": "<PERSON><PERSON> een tijd in de toeko<PERSON>t, Marty.", "targetIsTooFarInTheFuture": "We passen het plan aan zodra we meer weten over de toekomst.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>", "tomorrow": "morgen", "update": "Updaten", "vehicleCapacityDocs": "<PERSON>er informatie over het configureren hiervan.", "vehicleCapacityRequired": "De batterijcapaciteit van het voertuig is een vereiste parameter om de oplaadtijd te kunnen bepalen."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "co2Label": "CO₂ emissie ⌀", "priceLabel": "Energieprijs", "timeRange": "{day} {range} h", "unknownPrice": "nog onbekend"}, "targetEnergy": {"label": "<PERSON><PERSON>", "noLimit": "geen"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "changeVehicle": "Wijzig voertuig", "detectionActive": "Detecteren voertuig bezig…", "fallbackName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreActions": "Meer acties", "none": "<PERSON><PERSON> v<PERSON>", "notReachable": "Wagengegevens niet be<PERSON>. Herstart EVCC.", "targetSoc": "<PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Temperatuur limiet", "unknown": "<PERSON><PERSON> v<PERSON>", "vehicleSoc": "Lading"}, "vehicleStatus": {"awaitingAuthorization": "Wachten op autorisatie.", "batteryBoost": "Batterijboost actief.", "charging": "Opladen…", "cheapEnergyCharging": "Goedkope energie beschikbaar.", "cheapEnergyNextStart": "Goedkope energie in {duration}.", "cheapEnergySet": "Prijslimit ingesteld.", "cleanEnergyCharging": "Schone energie beschikbaar.", "cleanEnergyNextStart": "Schone energie in {duration}.", "cleanEnergySet": "CO₂ limiet ingesteld.", "climating": "Voor-conditionering gedetecteerd.", "connected": "Verbonden.", "disconnectRequired": "<PERSON><PERSON> beëindigd. Maak opnieuw verbinding.", "disconnected": "<PERSON><PERSON> verbonden.", "feedinPriorityNextStart": "Hoge energieprijs start over {duration}.", "feedinPriorityPausing": "Zonne opladen gepauzeerd om grid import te maximaliseren.", "finished": "Beëindigd.", "minCharge": "Minimaal opladen naar {soc}.", "pvDisable": "Onvoldoende PV overschot. Laden wordt gepauzeerd.", "pvEnable": "Voldoende PV overschot beschikbaar. Laden wordt gestart.", "scale1p": "<PERSON><PERSON> gered<PERSON>erd naar laden op 1 fase.", "scale3p": "<PERSON>t verhoogd naar laden op 3 fasen.", "targetChargeActive": "Laadplan actief. Geschatte beëindiging over {duration}.", "targetChargePlanned": "Laadplan begint over {duration}.", "targetChargeWaitForVehicle": "Laadplan gereed. Wachten op voertuig…", "vehicleLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleLimitReached": "Voertuiglimiet bereikt.", "waitForVehicle": "<PERSON>la<PERSON>. Wachten op voertuig…", "welcome": "Verkort initiële lading om de verbinding te bevestigen."}, "vehicles": "Parking", "welcome": "Welkom aan boord!"}, "notifications": {"dismissAll": "Alles verwijderen", "logs": "Volledige logboeken bekijken", "modalTitle": "Notificaties"}, "offline": {"configurationError": "Fout tijdens opstarten. Controleer de configuratie en start opnieuw.", "message": "<PERSON><PERSON> met een server.", "restart": "Herstart", "restartNeeded": "<PERSON><PERSON><PERSON>d om aan<PERSON>en door te voeren.", "restarting": "Server is bin<PERSON><PERSON><PERSON> weer be<PERSON><PERSON>."}, "passwordModal": {"description": "<PERSON>el een wachtwoord in voor het afschermen van de instellingen. Het hoofdscherm blijft beschikbaar zonder wachtwoord.", "empty": "Wachtwoord mag niet leeg zijn", "error": "Fout: ", "labelCurrent": "<PERSON><PERSON><PERSON> wa<PERSON>", "labelNew": "<PERSON><PERSON><PERSON> wa<PERSON>", "labelRepeat": "Wachtwoord herhalen", "newPassword": "Wachtwoord aanmaken", "noMatch": "Wachtwoorden komen niet overeen", "titleNew": "<PERSON>el beheerderswachtwoord in", "titleUpdate": "Beheerderswachtwoord bijwerken", "updatePassword": "Wachtwoord bijwerken"}, "session": {"cancel": "<PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "Periode", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finished": "Afgerond", "meter": "<PERSON>er", "meterstart": "Meter start", "meterstop": "Meter stop", "odometer": "Kilometerstand", "price": "<PERSON><PERSON><PERSON><PERSON>", "started": "Gestart", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sessions": {"avgPower": "⌀ Vermogen", "avgPrice": "⌀ Prijs", "chargeDuration": "<PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Prijs {byGroup}", "byGroupLoadpoint": "per <PERSON>", "byGroupVehicle": "per <PERSON>", "energy": "Geladen Energie", "energyGrouped": "<PERSON><PERSON> vs. Net Energie", "energyGroupedByGroup": "Energie {byGroup}", "energySubSolar": "{value} zon", "energySubTotal": "{value} totaal", "groupedCo2ByGroup": "CO₂-Hoeveelheid {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON> {byGroup}", "historyCo2": "CO₂-Uitstoot", "historyCo2Sub": "{value} totaal", "historyPrice": "Oplaadkosten", "historyPriceSub": "{value} totaal", "solar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> per jaar", "solarByGroup": "<PERSON>onne-<PERSON><PERSON><PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energie (kWh)", "chargeduration": "<PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "Aangemaakt", "finished": "Beëindigd", "identifier": "ID", "loadpoint": "Oplaadpunt", "meterstart": "Meter start (kWh)", "meterstop": "Meter stop (kwh)", "odometer": "Kilometerstand (km)", "price": "<PERSON><PERSON><PERSON><PERSON>", "priceperkwh": "Prijs/kWh", "solarpercentage": "Zonne-energie (%)", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "csvPeriod": "Download CSV van {period}", "csvTotal": "Download totale CSV", "date": "Start", "energy": "Geladen", "filter": {"allLoadpoints": "alle oplaadpunten", "allVehicles": "alle voertuigen", "filter": "Filter"}, "group": {"co2": "Emissies", "grid": "Net", "price": "<PERSON><PERSON><PERSON><PERSON>", "self": "Zon"}, "groupBy": {"loadpoint": "Laadpunt", "none": "Totaal", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Oplaadpunt", "noData": "<PERSON><PERSON> deze maand.", "overview": "Overzicht", "period": {"month": "<PERSON><PERSON>", "total": "Totaal", "year": "Jaar"}, "price": "<PERSON><PERSON>", "reallyDelete": "Wilt u deze sessie echt verwijderen?", "showIndividualEntries": "Toon individuele sessies", "solar": "Zon", "title": "Oplaadsessies", "total": "Totaal", "type": {"co2": "CO₂", "price": "<PERSON><PERSON><PERSON><PERSON>", "solar": "Zon"}, "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Open volledig scherm", "exit": "Volledig scherm afsluiten", "label": "Volledig scherm"}, "hiddenFeatures": {"label": "Experimenteel", "value": "Toon experimentele UI functies."}, "language": {"auto": "Automatisch", "label": "Taal"}, "sponsorToken": {"expires": "Je sponsortoken vervalt {inXDays}. {getNewToken} en update het hier.", "getNew": "<PERSON>raag een nieuwe aan", "hint": "Nota: We zullen dit in de toekomst automatiseren."}, "telemetry": {"label": "Telemetrie"}, "theme": {"auto": "systeem", "dark": "donker", "label": "<PERSON>a", "light": "licht"}, "time": {"12h": "12u", "24h": "24u", "label": "Tijdnotatie"}, "title": "Gebruikersinterface", "unit": {"km": "km", "label": "Eenheden", "mi": "<PERSON><PERSON><PERSON>"}}, "smartCost": {"activeHours": "{active} van {total}", "activeHoursLabel": "<PERSON><PERSON><PERSON> uren", "applyToAll": "Overal toepassen?", "batteryDescription": "<PERSON><PERSON><PERSON> de huisbatterij op met stroom uit het net.", "cheapTitle": "Goedkoop netstroom laden", "cleanTitle": "<PERSON><PERSON> met schone energie uit het net", "co2Label": "CO₂ uitstoot", "co2Limit": "CO₂ limiet", "loadpointDescription": "Zet tijdelijk snel-laden in solar mode aan.", "modalTitle": "Smart Grid laden", "none": "geen", "priceLabel": "Energieprijs", "priceLimit": "Prijslimiet", "resetAction": "Limiet verwijderen", "resetWarning": "Er is geen dynamische netprijs of CO₂-bron geconfigureerd. Er is echter nog steeds een limiet van {limit}. Je configuratie opruimen?", "saved": "Opgeslagen."}, "smartFeedInPriority": {"activeHoursLabel": "Gepau<PERSON>rde uren", "description": "<PERSON><PERSON><PERSON> het opladen tijdens hoge prijzen om prioriteit te geven aan terugleveren.", "priceLabel": "Teruglevertarief", "priceLimit": "Terugleverprijs limiet", "title": "Terugleveren prioritiseren"}, "startupError": {"configFile": "Gebruikt configuratiebestand:", "configuration": "Configuratie", "description": "Controleer uw configuratiebestand. Als de foutmelding niet helpt, bekijk dan de {0}.", "discussions": "GitHub Discussies", "fixAndRestart": "Los het probleem op en herstart de server.", "hint": "Let op: het kan ook zijn dat u een defect toestel heeft (omvormer, meter, …). Controleer uw netwerkverbindingen.", "lineError": "Fout in {0}.", "lineErrorLink": "lijn {0}", "restartButton": "Herstart", "title": "Start Error"}}