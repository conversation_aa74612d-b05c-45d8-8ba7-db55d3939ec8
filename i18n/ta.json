{"batterySettings": {"batteryLevel": "பேட்டரி நிலை", "bufferStart": {"above": "மேலே {soc}.", "full": "போது {soc at இல் இருக்கும்போது.", "never": "போதுமான உபரி மட்டுமே."}, "capacity": "{energy} இன் {total} \"", "control": "பேட்டரி கட்டுப்பாடு", "discharge": "வேகமான பயன்முறையில் வெளியேற்றத்தைத் தடுக்கவும் மற்றும் திட்டமிடப்பட்ட சார்சிங்.", "disclaimerHint": "குறிப்பு:", "disclaimerText": "இந்த அமைப்புகள் சூரிய பயன்முறையை மட்டுமே பாதிக்கின்றன. சார்சிங் நடத்தை அதற்கேற்ப சரிசெய்யப்படுகிறது.", "gridChargeTab": "கட்டம் சார்சிங்", "legendBottomName": "வீட்டு பேட்டரி சார்சிங்கிற்கு முன்னுரிமை அளிக்கவும்", "legendBottomSubline": "அது அடையும் வரை {soc}.", "legendMiddleName": "வாகன கட்டணம் வசூலிப்பதற்கு முன்னுரிமை அளிக்கவும்", "legendMiddleSubline": "வீட்டு பேட்டரி மேலே இருக்கும்போது {soc}.", "legendTopAutostart": "தானாகவே தொடங்குங்கள்", "legendTopName": "பேட்டரி உதவி வாகன சார்சிங்", "legendTopSubline": "வீட்டு பேட்டரி மேலே இருக்கும்போது {soc}.", "modalTitle": "வீட்டு பேட்டரி", "usageTab": "பேட்டரி பயன்பாடு"}, "config": {"aux": {"description": "கிடைக்கக்கூடிய உபரி (ச்மார்ட் வாட்டர் ஈட்டர்கள் போன்றவை) அடிப்படையில் அதன் நுகர்வு சரிசெய்யும் சாதனம். தேவைப்பட்டால் இந்த சாதனம் அதன் மின் நுகர்வு குறைக்கிறது என்று ஈ.வி.சி.சி எதிர்பார்க்கிறது.", "titleAdd": "சுய-ஒழுங்குபடுத்தும் நுகர்வோர் சேர்க்கவும்", "titleEdit": "சுய-ஒழுங்குபடுத்தும் நுகர்வோர் திருத்தவும்"}, "battery": {"titleAdd": "பேட்டரியைச் சேர்க்கவும்", "titleEdit": "பேட்டரியைத் திருத்து"}, "charge": {"titleAdd": "கட்டண மீட்டரைச் சேர்க்கவும்", "titleEdit": "கட்டண மீட்டரைத் திருத்து"}, "charger": {"chargers": "ஈ.வி. சார்சர்ச்", "generic": "பொதுவான ஒருங்கிணைப்புகள்", "heatingdevices": "வெப்ப சாதனங்கள்", "ocppHelp": "இந்த முகவரியை உங்கள் சார்சர்ச் உள்ளமைவில் நகலெடுக்கவும்.", "ocppLabel": "OCPP-SERVER முகவரி", "switchsockets": "மாறக்கூடிய சாக்கெட்டுகள்", "template": "உற்பத்தியாளர்", "titleAdd": {"charging": "சார்சரைச் சேர்க்கவும்", "heating": "ஈட்டரைச் சேர்க்கவும்"}, "titleEdit": {"charging": "சார்சரைத் திருத்து", "heating": "ஈட்டரைத் திருத்து"}, "type": {"custom": {"charging": "பயனர் வரையறுக்கப்பட்ட சார்சர்", "heating": "பயனர் வரையறுக்கப்பட்ட ஈட்டர்"}, "heatpump": "பயனர் வரையறுக்கப்பட்ட வெப்ப பம்ப்", "sgready": "பயனர் வரையறுக்கப்பட்ட வெப்ப பம்ப் (எச்.சி-தயார், அனைத்தும்)", "sgready-boost": "பயனர் வரையறுக்கப்பட்ட வெப்ப பம்ப் (எச்.சி-தயார், பூச்ட்)", "switchsocket": "பயனர் வரையறுக்கப்பட்ட சுவிட்ச் சாக்கெட்"}}, "circuits": {"description": "ஒரு சுற்றுடன் இணைக்கப்பட்ட அனைத்து ஏற்ற புள்ளிகளின் கூட்டுத்தொகையும் கட்டமைக்கப்பட்ட ஆற்றல் மற்றும் தற்போதைய வரம்புகளை மீறாது என்பதை உறுதி செய்கிறது. ஒரு படிநிலையை உருவாக்க சுற்றுகள் கூடு கட்டப்படலாம்.", "title": "சுமை மேலாண்மை"}, "control": {"description": "பொதுவாக இயல்புநிலை மதிப்புகள் நன்றாக இருக்கும். நீங்கள் என்ன செய்கிறீர்கள் என்று உங்களுக்குத் தெரிந்தால் மட்டுமே அவற்றை மாற்றவும்.", "descriptionInterval": "நொடிகளில் கட்டுப்பாட்டு லூப் புதுப்பிப்பு சுழற்சியைக் கட்டுப்படுத்துங்கள். ஈ.வி.சி.சி மீட்டர் தரவைப் படிக்கிறது, சார்சிங் சக்தியை சரிசெய்கிறது மற்றும் இடைமுகம் ஐப் புதுப்பிக்கிறது. குறுகிய இடைவெளிகள் (<30 கள்) ஊசலாட்டங்களையும் தேவையற்ற நடத்தையையும் ஏற்படுத்தும்.", "descriptionResidualPower": "கட்டுப்பாட்டு வளையத்தின் செயல்பாட்டு புள்ளியை மாற்றுகிறது. உங்களிடம் வீட்டு பேட்டரி இருந்தால் 100 W மதிப்பை அமைக்க பரிந்துரைக்கப்படுகிறது. இந்த வழியில் கட்டம் பயன்பாட்டை விட பேட்டரி சற்று முன்னுரிமை பெறும்.", "labelInterval": "புதுப்பிப்பு இடைவெளி", "labelResidualPower": "மீதமுள்ள ஆற்றல்", "title": "கட்டுப்பாட்டு நடத்தை"}, "deviceValue": {"amount": "தொகை", "broker": "தரகர்", "bucket": "வாளி", "capacity": "திறன்", "chargeStatus": "நிலை", "chargeStatusA": "இணைக்கப்படவில்லை", "chargeStatusB": "இணைக்கப்பட்டுள்ளது", "chargeStatusC": "சார்சிங்", "chargeStatusE": "இல்லை ஆற்றல்", "chargeStatusF": "பிழை", "chargedEnergy": "கட்டணம் வசூலிக்கப்பட்டது", "co2": "கிரிட் கோ ₂", "configured": "கட்டமைக்கப்பட்ட", "controllable": "கட்டுப்படுத்தக்கூடியது", "currency": "நாணயம்", "current": "நடப்பு", "currentRange": "நடப்பு", "enabled": "இயக்கப்பட்டது", "energy": "சக்தி", "feedinPrice": "ஃபீட்-இன் விலை", "gridPrice": "கட்டம் விலை", "heaterTempLimit": "ஈட்டர் வரம்பு", "hemsType": "கணினி", "identifier": "RFID-IDENTIFIER", "no": "இல்லை", "odometer": "ஓடோமீட்டர்", "org": "அமைப்பு", "phaseCurrents": "தற்போதைய எல் 1, எல் 2, எல் 3", "phasePowers": "பவர் எல் 1, இல்லை, எல்", "phaseVoltages": "மின்னழுத்தம் எல் 1, எல் 2, எல் 3", "phases1p3p": "கட்ட சுவிட்ச்", "power": "ஆற்றல்", "powerRange": "ஆற்றல்", "range": "வரம்பு", "singlePhase": "ஒற்றைத் தறுவாய்", "soc": "சொக்", "solarForecast": "சூரிய முன்னறிவிப்பு", "temp": "வெப்பநிலை", "topic": "தலைப்பு", "url": "முகவரி", "vehicleLimitSoc": "வாகன வரம்பு", "yes": "ஆம்"}, "deviceValueChargeStatus": {"A": "A (இணைக்கப்படவில்லை)", "B": "பி (இணைக்கப்பட்டுள்ளது)", "C": "சி (சார்சிங்)"}, "devices": {"auxMeter": "அறிவுள்ள நுகர்வோர்", "batteryStorage": "பேட்டரி சேமிப்பு", "solarSystem": "சூரிய குடும்பம்"}, "editor": {"loading": "யாம் எடிட்டரை ஏற்றுகிறது…"}, "eebus": {"description": "ஈ.வி.சி.சி மற்ற ஈபச் சாதனங்களுடன் தொடர்பு கொள்ள உதவும் உள்ளமைவு.", "title": "ஈபச்"}, "ext": {"description": "சுமை மேலாண்மை அல்லது புள்ளிவிவர நோக்கங்களுக்காக பயன்படுத்தலாம்.", "titleAdd": "வெளிப்புற மீட்டரைச் சேர்க்கவும்", "titleEdit": "வெளிப்புற மீட்டரைத் திருத்தவும்"}, "form": {"danger": "இடர்", "deprecated": "மதிப்பிடப்பட்டது", "example": "எடுத்துக்காட்டு", "optional": "விரும்பினால்"}, "general": {"cancel": "ரத்துசெய்", "customHelp": "EVCC இன் சொருகி அமைப்பைப் பயன்படுத்தி பயனர் வரையறுக்கப்பட்ட சாதனத்தை உருவாக்கவும்.", "customOption": "பயனர் வரையறுக்கப்பட்ட சாதனம்", "delete": "நீக்கு", "docsLink": "ஆவணங்களைக் காண்க.", "experimental": "சோதனை", "hideAdvancedSettings": "மேம்பட்ட அமைப்புகளை மறைக்க", "invalidFileSelected": "தவறான கோப்பு தேர்ந்தெடுக்கப்பட்டது", "noFileSelected": "எந்த கோப்பும் தேர்ந்தெடுக்கப்படவில்லை.", "off": "ஆஃப்", "on": "ஆன்", "password": "கடவுச்சொல்", "readFromFile": "கோப்பிலிருந்து படியுங்கள்", "remove": "அகற்று", "save": "சேமி", "selectFile": "உலாவு", "showAdvancedSettings": "மேம்பட்ட அமைப்புகளைக் காட்டு", "telemetry": "டெலிமெட்ரி", "templateLoading": "ஏற்றுகிறது ...", "title": "தலைப்பு", "validateSave": "சரிபார்க்கவும் சேமிக்கவும்"}, "grid": {"title": "கட்டம் மீட்டர்", "titleAdd": "கட்டம் மீட்டரைச் சேர்க்கவும்", "titleEdit": "கட்டம் மீட்டரைத் திருத்து"}, "hems": {"description": "ஈ.வி.சி.சியை மற்றொரு வீட்டு எரிசக்தி மேலாண்மை அமைப்புடன் இணைக்கவும்.", "title": "எம்ச்"}, "icon": {"change": "மாற்றம்"}, "influx": {"description": "சார்சிங் தரவு மற்றும் பிற அளவீடுகளை இன்ஃப்ளக்ச்.டி.பி.க்கு எழுதுகிறது. தரவைக் காட்சிப்படுத்த கிராஃபானா அல்லது பிற கருவிகளைப் பயன்படுத்தவும்.", "descriptionToken": "ஒன்றை எவ்வாறு உருவாக்குவது என்பதை அறிய இன்ஃப்ளக்ச்.டி.பி ஆவணங்களைச் சரிபார். https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "வாளி", "labelCheckInsecure": "தன்வய கையொப்பமிடப்பட்ட சான்றிதழ்களை அனுமதிக்கவும்", "labelDatabase": "தரவுத்தளம்", "labelInsecure": "சான்றிதழ் சரிபார்ப்பு", "labelOrg": "அமைப்பு", "labelPassword": "கடவுச்சொல்", "labelToken": "பநிஇ கிள்ளாக்கு", "labelUrl": "முகவரி", "labelUser": "பயனர்பெயர்", "title": "Influxdb", "v1Support": "இன்ஃப்ளக்ச்.டி.பி 1.x க்கு உதவி தேவையா?", "v2Support": "UnfluxDB 2.x க்கு திரும்பவும்"}, "loadpoint": {"addCharger": {"charging": "சார்சரைச் சேர்க்கவும்", "heating": "ஈட்டரைச் சேர்க்கவும்"}, "addMeter": "அர்ப்பணிப்பு ஆற்றல் மீட்டரைச் சேர்க்கவும்", "cancel": "ரத்துசெய்", "chargerError": {"charging": "சார்சரை உள்ளமைப்பது தேவை.", "heating": "ஒரு ஈட்டரை உள்ளமைப்பது தேவை."}, "chargerLabel": {"charging": "மின்னூட்டி", "heating": "ஈட்டர்"}, "chargerPower11kw": "11 கிலோவாட்", "chargerPower11kwHelp": "தற்போதைய 6 முதல் 16 வரை தற்போதைய வரம்பைப் பயன்படுத்தும்.", "chargerPower22kw": "22 கிலோவாட்", "chargerPower22kwHelp": "தற்போதைய 6 முதல் 32 ஏ வரை பயன்படுத்தும்.", "chargerPowerCustom": "மற்றொன்று", "chargerPowerCustomHelp": "தனிப்பயன் தற்போதைய வரம்பை வரையறுக்கவும்.", "chargerTypeLabel": "சார்சர் வகை", "chargingTitle": "நடத்தை", "circuitHelp": "ஆற்றல் மற்றும் தற்போதைய வரம்புகள் மீறப்படுவதில்லை என்பதை உறுதிப்படுத்த சுமை மேலாண்மை பணி.", "circuitLabel": "சுற்று", "circuitUnassigned": "ஒதுக்கப்படாதது", "defaultModeHelp": {"charging": "வாகனத்தை இணைக்கும்போது சார்சிங் பயன்முறை.", "heating": "கணினி தொடங்கும் போது அமைக்கப்படுகிறது."}, "defaultModeHelpKeep": "கடைசியாக தேர்ந்தெடுக்கப்பட்ட பயன்முறையை வைத்திருக்கிறது.", "defaultModeLabel": "இயல்புநிலை பயன்முறை", "delete": "நீக்கு", "electricalSubtitle": "ஐயம் இருக்கும்போது, உங்கள் எலக்ட்ரீசியனிடம் கேளுங்கள்.", "electricalTitle": "மின்னியல்", "energyMeterHelp": "கூடுதல் மீட்டர் சார்சருக்கு ஒருங்கிணைந்த ஒன்று இல்லை என்றால்.", "energyMeterLabel": "ஆற்றல் மீட்டர்", "estimateLabel": "பநிஇ புதுப்பிப்புகளுக்கு இடையில் கட்டண அளவை இடைக்கணிக்கவும்", "maxCurrentHelp": "குறைந்தபட்ச மின்னோட்டத்தை விட அதிகமாக இருக்க வேண்டும்.", "maxCurrentLabel": "அதிகபட்ச மின்னோட்டம்", "minCurrentHelp": "நீங்கள் என்ன செய்கிறீர்கள் என்று உங்களுக்குத் தெரிந்தால் மட்டுமே 6 A க்குக் கீழே செல்லுங்கள்.", "minCurrentLabel": "குறைந்தபட்ச மின்னோட்டம்", "noVehicles": "எந்த வாகனங்களும் கட்டமைக்கப்படவில்லை.", "option": {"charging": "சார்சிங் புள்ளியைச் சேர்க்கவும்", "heating": "வெப்பமூட்டும் சாதனத்தைச் சேர்க்கவும்"}, "phases1p": "1-கட்டம்", "phases3p": "3-கட்டம்", "phasesAutomatic": "தானியங்கி கட்டங்கள்", "phasesAutomaticHelp": "உங்கள் சார்சர் 1- மற்றும் 3-கட்ட சார்சிங்கிற்கு இடையில் தானியங்கி மாறுவதை ஆதரிக்கிறது. முதன்மையான திரையில் நீங்கள் சார்ச் செய்யும் போது கட்ட நடத்தை சரிசெய்யலாம்.", "phasesHelp": "இணைக்கப்பட்ட கட்டங்களின் எண்ணிக்கை.", "phasesLabel": "கட்டங்கள்", "pollIntervalDanger": "வாகனத்தை தவறாமல் வினவுவது வாகன பேட்டரியை வெளியேற்றக்கூடும். சில வாகன உற்பத்தியாளர்கள் இந்த வழக்கில் கட்டணம் வசூலிப்பதை தீவிரமாக தடுக்கலாம். பரிந்துரைக்கப்படவில்லை! அபாயங்கள் உங்களுக்குத் தெரிந்தால் மட்டுமே இதைப் பயன்படுத்தவும்.", "pollIntervalHelp": "வாகன பநிஇ புதுப்பிப்புகளுக்கு இடையிலான நேரம். குறுகிய இடைவெளிகள் வாகன பேட்டரியை வெளியேற்றக்கூடும்.", "pollIntervalLabel": "இடைவெளி புதுப்பிக்கவும்", "pollModeAlways": "எப்போதும்", "pollModeAlwaysHelp": "சரியான இடைவெளியில் நிலை புதுப்பிப்புகளை எப்போதும் கோருங்கள்.", "pollModeCharging": "சார்சிங்", "pollModeChargingHelp": "கட்டணம் வசூலிக்கும்போது மட்டுமே வாகன நிலை புதுப்பிப்புகளைக் கோருங்கள்.", "pollModeConnected": "இணைக்கப்பட்டுள்ளது", "pollModeConnectedHelp": "இணைக்கும்போது வாகன நிலையை சீரான இடைவெளியில் புதுப்பிக்கவும்.", "pollModeLabel": "புதுப்பிப்பு நடத்தை", "priorityHelp": "அதிக முன்னுரிமை சூரிய உபளிக்கு விருப்பமான அணுகலைப் பெறுங்கள்.", "priorityLabel": "முன்னுரிமை", "save": "சேமி", "showAllSettings": "எல்லா அமைப்புகளையும் காட்டு", "solarBehaviorCustomHelp": "உங்கள் சொந்தத்தை வரையறுக்கவும், வாசல்கள் மற்றும் தாமதங்களை முடக்கவும்.", "solarBehaviorDefaultHelp": "போதுமான உபரி {enableDelay} க்குப் பிறகு தொடங்கவும். {disableDelay} க்கு போதுமான உபரி இல்லாதபோது நிறுத்துங்கள்.", "solarBehaviorLabel": "சூரிய", "solarModeCustom": "தனிப்பயன்", "solarModeMaximum": "அதிகபட்ச சூரிய", "thresholdDisableDelayLabel": "தாமதத்தை முடக்கு", "thresholdDisableHelpInvalid": "நேர்மறையான மதிப்பைப் பயன்படுத்தவும்.", "thresholdDisableHelpPositive": "நிறுத்த, {power} ஐ விட அதிகமாக {delay} க்கு பயன்படுத்தப்படும்போது.", "thresholdDisableHelpZero": "குறைந்தபட்ச தேவையான சக்தியை {delay} க்கு நிறைவு செய்ய முடியாது.", "thresholdDisableLabel": "கட்டம் சக்தியை முடக்கு", "thresholdEnableDelayLabel": "தாமதத்தை இயக்கவும்", "thresholdEnableHelpInvalid": "எதிர்மறை மதிப்பைப் பயன்படுத்தவும்.", "thresholdEnableHelpNegative": "{surplus} உபரி {தாமதத்திற்கு கிடைக்கும்போது தொடங்குங்கள்.", "thresholdEnableHelpZero": "{delay} க்கு குறைந்தபட்ச தேவையான சக்தியை நிறைவு செய்யும்போது தொடங்கவும்.", "thresholdEnableLabel": "கட்டம் சக்தியை இயக்கவும்", "titleAdd": {"charging": "சார்சிங் புள்ளியைச் சேர்க்கவும்", "heating": "வெப்பமூட்டும் சாதனத்தைச் சேர்க்கவும்", "unknown": "சார்சர் அல்லது ஈட்டரைச் சேர்க்கவும்"}, "titleEdit": {"charging": "சார்சிங் புள்ளியைத் திருத்து", "heating": "வெப்ப சாதனத்தைத் திருத்து", "unknown": "சார்சர் அல்லது ஈட்டரைத் திருத்து"}, "titleExample": {"charging": "கேரேச், கார்போர்ட், முதலியன.", "heating": "வெப்ப பம்ப், ஈட்டர் போன்றவை."}, "titleLabel": "தலைப்பு", "vehicleAutoDetection": "ஆட்டோ கண்டறிதல்", "vehicleHelpAutoDetection": "மிகவும் நம்பத்தகுந்த வாகனத்தை தானாகவே தேர்ந்தெடுக்கிறது. கையேடு மேலெழுதல் சாத்தியமாகும்.", "vehicleHelpDefault": "இந்த வண்டி இங்கே கட்டணம் வசூலிப்பதாக எப்போதும் கருதுங்கள். தானாக கண்டறிதல் முடக்கப்பட்டுள்ளது. கையேடு மேலெழுதல் சாத்தியமாகும்.", "vehicleLabel": "இயல்புநிலை வண்டி", "vehiclesTitle": "வாகனங்கள்"}, "main": {"addAdditional": "கூடுதல் மீட்டரைச் சேர்க்கவும்", "addGrid": "கட்டம் மீட்டரைச் சேர்க்கவும்", "addLoadpoint": "சார்சர் அல்லது ஈட்டரைச் சேர்க்கவும்", "addPvBattery": "சூரிய அல்லது பேட்டரியைச் சேர்க்கவும்", "addTariffs": "கட்டணங்களைச் சேர்க்கவும்", "addVehicle": "வண்டி சேர்க்கவும்", "configured": "கட்டமைக்கப்பட்ட", "edit": "திருத்து", "loadpointRequired": "குறைந்தது ஒரு சார்சிங் புள்ளியை உள்ளமைக்க வேண்டும்.", "name": "பெயர்", "title": "உள்ளமைவு", "unconfigured": "கட்டமைக்கப்படவில்லை", "vehicles": "என் வாகனங்கள்", "yaml": "Evcc.yaml இல் கட்டமைக்கப்பட்டுள்ளது. இடைமுகம் இல் திருத்த முடியாது."}, "messaging": {"description": "உங்கள் சார்சிங் அமர்வுகள் பற்றிய செய்திகளைப் பெறுங்கள்.", "title": "அறிவிப்புகள்"}, "meter": {"cancel": "ரத்துசெய்", "delete": "நீக்கு", "generic": "பொதுவான ஒருங்கிணைப்புகள்", "option": {"aux": "சுய-ஒழுங்குபடுத்தும் நுகர்வோர் சேர்க்கவும்", "battery": "பேட்டரி மீட்டரைச் சேர்க்கவும்", "ext": "வெளிப்புற மீட்டரைச் சேர்க்கவும்", "pv": "சூரிய மீட்டரைச் சேர்க்கவும்"}, "save": "சேமி", "specific": "குறிப்பிட்ட ஒருங்கிணைப்புகள்", "template": "உற்பத்தியாளர்", "titleChoice": "நீங்கள் என்ன சேர்க்க விரும்புகிறீர்கள்?", "validateSave": "சரிபார்க்கவும் சேமிக்கவும்"}, "modbus": {"baudrate": "பாட் வீதம்", "comset": "COMSET", "connection": "மோட்பச் இணைப்பு", "connectionHintSerial": "சாதனம் நேரடியாக RS485 இடைமுகம் வழியாக EVCC உடன் இணைக்கப்பட்டுள்ளது.", "connectionHintTcpip": "சாதனம் EVCC இலிருந்து LAN/WIFI வழியாக உரையாற்றக்கூடியது.", "connectionValueSerial": "சீரியல் / யூ.எச்.பி", "connectionValueTcpip": "பிணையம்", "device": "சாதன பெயர்", "deviceHint": "எடுத்துக்காட்டு: /dev /ttyusb0", "host": "ஐபி முகவரி அல்லது ஓச்ட்பெயர்", "hostHint": "எடுத்துக்காட்டு: *********", "id": "மோட்பச் அடையாளம்", "port": "துறைமுகம்", "protocol": "மோட்பச் நெறிமுறை", "protocolHintRtu": "நெறிமுறை மொழிபெயர்ப்பு இல்லாமல் ஈதர்நெட் அடாப்டருக்கு RS485 மூலம் இணைப்பு.", "protocolHintTcp": "சாதனம் சொந்த LAN/WIFI ஆதரவைக் கொண்டுள்ளது அல்லது நெறிமுறை மொழிபெயர்ப்புடன் RS485 மூலம் ஈத்தர்நெட் அடாப்டர் மூலம் இணைக்கப்பட்டுள்ளது.", "protocolValueRtu": "Rtu", "protocolValueTcp": "டி.சி.பி."}, "modbusproxy": {"description": "பல வாடிக்கையாளர்களை ஒற்றை மோட்பச் சாதனத்தை அணுக அனுமதிக்கவும்.", "title": "மோட்பச் பதிலாள்"}, "mqtt": {"authentication": "ஏற்பு", "description": "உங்கள் நெட்வொர்க்கில் உள்ள பிற அமைப்புகளுடன் தரவைப் பரிமாறிக் கொள்ள MQTT தரகருடன் இணைக்கவும்.", "descriptionClientId": "செய்திகளின் ஆசிரியர். காலியாக இருந்தால்` evcc- [rand] `பயன்படுத்தப்பட்டால்.", "descriptionTopic": "வெளியீட்டை முடக்க காலியாக விடவும்.", "labelBroker": "தரகர்", "labelCaCert": "சேவையக சான்றிதழ் (CA)", "labelCheckInsecure": "தன்வய கையொப்பமிடப்பட்ட சான்றிதழ்களை அனுமதிக்கவும்", "labelClientCert": "கிளையன்ட் சான்றிதழ்", "labelClientId": "வாங்கி ஐடி", "labelClientKey": "கிளையன்ட் விசை", "labelInsecure": "சான்றிதழ் சரிபார்ப்பு", "labelPassword": "கடவுச்சொல்", "labelTopic": "தலைப்பு", "labelUser": "பயனர்பெயர்", "publishing": "வெளியீடு", "title": "MQTT"}, "network": {"descriptionHost": "எம்.டி.என் -களை இயக்க .லோகல் பின்னொட்டைப் பயன்படுத்துங்கள். மொபைல் பயன்பாடு மற்றும் சில OCPP சார்சர்களைக் கண்டுபிடிப்பதற்கு பொருத்தமானது.", "descriptionPort": "வலை இடைமுகம் மற்றும் பநிஇ க்கான துறைமுகம். இதை மாற்றினால் உங்கள் உலாவி முகவரி ஐ புதுப்பிக்க வேண்டும்.", "descriptionSchema": "முகவரி கள் எவ்வாறு உருவாக்கப்படுகின்றன என்பதை மட்டுமே பாதிக்கிறது. HTTP களைத் தேர்ந்தெடுப்பது குறியாக்கத்தை இயக்காது.", "labelHost": "ஓச்ட்பெயர்", "labelPort": "துறைமுகம்", "labelSchema": "ச்கீமா", "title": "பிணையம்"}, "options": {"boolean": {"no": "இல்லை", "yes": "ஆம்"}, "endianness": {"big": "பிக்-எண்டியன்", "little": "லிட்டில்-எண்டியன்"}, "operationMode": {"heating": "வெப்பமாக்கல்", "standby": "காத்திருப்பு"}, "schema": {"http": "HTTP (மறைகுறியாக்கப்படாதது)", "https": "Https (குறியாக்கப்பட்டது)"}, "status": {"A": "A (இணைக்கப்படவில்லை)", "B": "பி (இணைக்கப்பட்டுள்ளது)", "C": "சி (சார்சிங்)"}}, "pv": {"titleAdd": "சூரிய மீட்டரைச் சேர்க்கவும்", "titleEdit": "சூரிய மீட்டரைத் திருத்தவும்"}, "section": {"additionalMeter": "கூடுதல் மீட்டர்", "general": "பொது", "grid": "கட்டம்", "integrations": "ஒருங்கிணைப்புகள்", "loadpoints": "கட்டணம் மற்றும் வெப்பமாக்கல்", "meter": "சூரிய & பேட்டரி", "system": "கணினி", "vehicles": "வாகனங்கள்"}, "sponsor": {"addToken": "ஒப்புரவாளர் கிள்ளாக்கை உள்ளிடவும்", "changeToken": "ஒப்புரவாளர் கிள்ளாக்கை மாற்றவும்", "description": "ஒப்புரவாளர் மாதிரி திட்டத்தை பராமரிக்கவும் புதிய மற்றும் அற்புதமான அம்சங்களை நிலையானதாக உருவாக்கவும் உதவுகிறது. ஒரு ச்பான்சராக நீங்கள் அனைத்து சார்சர் செயலாக்கங்களுக்கும் அணுகலைப் பெறுவீர்கள்.", "descriptionToken": "நீங்கள் {url இருந்து இலிருந்து கிள்ளாக்கைப் பெறுவீர்கள். சோதனைக்கு ஒரு சோதனை டோக்கனையும் நாங்கள் வழங்குகிறோம்.", "error": "ஒப்புரவாளர் கிள்ளாக்கு செல்லுபடியாகாது.", "labelToken": "ஒப்புரவாளர் கிள்ளாக்கு", "title": "ச்பான்சர்சிப்", "tokenRequired": "இந்த சாதனத்தை உருவாக்குவதற்கு முன்பு நீங்கள் ஒரு ஒப்புரவாளர் கிள்ளாக்கை உள்ளமைக்க வேண்டும்.", "tokenRequiredLearnMore": "மேலும் அறிக.", "trialToken": "சோதனை கிள்ளாக்கு"}, "system": {"backupRestore": {"backup": {"action": "காப்புப்பிரதியைப் பதிவிறக்குக ...", "confirmationButton": "காப்புப்பிரதியைப் பதிவிறக்கவும்", "confirmationText": "தரவுத்தள கோப்பைப் பதிவிறக்க உங்கள் கடவுச்சொல்லை உள்ளிடவும்.", "description": "உங்கள் தரவை ஒரு கோப்பில் காப்புப் பிரதி எடுக்கவும். கணினி தோல்வி ஏற்பட்டால் உங்கள் தரவை மீட்டெடுக்க இந்த கோப்பு பயன்படுத்தப்படலாம்.", "title": "காப்புப்பிரதி"}, "cancel": "ரத்துசெய்", "description": "உங்கள் தரவை காப்புப் பிரதி, மீட்டமை மற்றும் மீட்டமைக்கவும். உங்கள் தரவை வேறொரு கணினிக்கு நகர்த்த விரும்பினால் பயனுள்ளதாக இருக்கும்.", "note": "குறிப்பு: மேலே உள்ள அனைத்து செயல்களும் உங்கள் தரவுத்தள தரவை மட்டுமே பாதிக்கின்றன. EVCC.YAML உள்ளமைவு கோப்பு மாறாமல் உள்ளது.", "reset": {"action": "மீட்டமை ...", "confirmationButton": "மீட்டமை மற்றும் மறுதொடக்கம்", "confirmationText": "இது நீங்கள் தேர்ந்தெடுத்த தரவை நிரந்தரமாக நீக்கும். நீங்கள் முதலில் ஒரு காப்புப்பிரதியை பதிவிறக்கம் செய்துள்ளீர்கள் என்பதை உறுதிப்படுத்தவும்.", "description": "உள்ளமைவில் சிக்கல்கள் மற்றும் தொடங்க விரும்புகிறீர்களா? எல்லா தரவையும் நீக்கி புதியதாகத் தொடங்கவும்.", "sessions": "சார்சிங் அமர்வுகள்", "sessionsDescription": "உங்கள் சார்சிங் அமர்வு வரலாற்றை நீக்குகிறது.", "settings": "உள்ளமைவு மற்றும் அமைப்புகள்", "settingsDescription": "உள்ளமைக்கப்பட்ட அனைத்து சாதனங்கள், சேவைகள், திட்டங்கள், தற்காலிக சேமிப்புகள் போன்றவற்றை நீக்குகிறது.", "title": "மீட்டமை"}, "restore": {"action": "மீட்டமைக்க ...", "confirmationButton": "மீட்டெடுத்து மறுதொடக்கம் செய்யுங்கள்", "confirmationText": "இது உங்கள் முழுமையான தரவுத்தளத்தை மேலெழுதும். நீங்கள் முதலில் ஒரு காப்புப்பிரதியை பதிவிறக்கம் செய்துள்ளீர்கள் என்பதை உறுதிப்படுத்தவும்.", "description": "காப்புப்பிரதி கோப்பிலிருந்து உங்கள் தரவை மீட்டெடுக்கவும். இது உங்கள் தற்போதைய எல்லா தரவையும் மேலெழுதும்.", "labelFile": "காப்புப்பிரதி கோப்பு", "title": "மீட்டெடு"}, "title": "காப்புப்பிரதி மற்றும் மீட்டமை"}, "logs": "பதிவுகள்", "restart": "மறுதொடக்கம்", "restartRequiredDescription": "விளைவைக் காண தயவுசெய்து மறுதொடக்கம் செய்யுங்கள்.", "restartRequiredMessage": "உள்ளமைவு மாற்றப்பட்டது.", "restartingDescription": "தயவுசெய்து காத்திருங்கள்…", "restartingMessage": "EVCC ஐ மறுதொடக்கம் செய்தல்."}, "tariffs": {"description": "உங்கள் சார்சிங் அமர்வுகளின் செலவுகளைக் கணக்கிட உங்கள் ஆற்றல் கட்டணங்களை வரையறுக்கவும்.", "title": "கட்டணங்கள்"}, "title": {"description": "முதன்மையான திரை மற்றும் உலாவி தாவலில் காட்டப்படும்.", "label": "தலைப்பு", "title": "தலைப்பைத் திருத்து"}, "validation": {"failed": "தோல்வியுற்றது", "label": "நிலை", "running": "சரிபார்ப்பு…", "success": "வெற்றி", "unknown": "தெரியவில்லை", "validate": "சரிபார்ப்பு"}, "vehicle": {"cancel": "ரத்துசெய்", "chargingSettings": "அமைப்புகள் சார்ச்", "defaultMode": "இயல்புநிலை பயன்முறை", "defaultModeHelp": "வாகனத்தை இணைக்கும்போது சார்சிங் பயன்முறை.", "delete": "நீக்கு", "generic": "பிற ஒருங்கிணைப்புகள்", "identifiers": "RFID அடையாளங்காட்டிகள்", "identifiersHelp": "வாகனத்தை அடையாளம் காண RFID சரங்களின் பட்டியல். ஒரு வரிக்கு ஒரு நுழைவு. தற்போதைய அடையாளங்காட்டியை கண்ணோட்டம் பக்கத்தில் அந்தந்த சார்சிங் புள்ளியில் காணலாம்.", "maximumCurrent": "அதிகபட்ச மின்னோட்டம்", "maximumCurrentHelp": "குறைந்தபட்ச மின்னோட்டத்தை விட அதிகமாக இருக்க வேண்டும்.", "maximumPhases": "அதிகபட்ச கட்டங்கள்", "maximumPhasesHelp": "இந்த வண்டி எத்தனை கட்டங்களை வசூலிக்க முடியும்? தேவையான குறைந்தபட்ச சூரிய உபரி மற்றும் திட்ட காலத்தைக் கணக்கிடப் பயன்படுகிறது.", "minimumCurrent": "குறைந்தபட்ச மின்னோட்டம்", "minimumCurrentHelp": "நீங்கள் என்ன செய்கிறீர்கள் என்று உங்களுக்குத் தெரிந்தால் மட்டுமே 6A க்கு கீழே செல்லுங்கள்.", "online": "நிகழ்நிலை பநிஇ கொண்ட வாகனங்கள்", "primary": "பொதுவான ஒருங்கிணைப்புகள்", "priority": "முன்னுரிமை", "priorityHelp": "அதிக முன்னுரிமை என்பது இந்த வண்டி சூரிய உபளிக்கு விருப்பமான அணுகலைப் பெறுகிறது.", "save": "சேமி", "scooter": "ச்கூட்டர்", "template": "உற்பத்தியாளர்", "titleAdd": "வண்டி சேர்க்கவும்", "titleEdit": "வண்டி திருத்து", "validateSave": "சரிபார்க்கவும் சேமிக்கவும்"}}, "footer": {"community": {"greenEnergy": "சூரிய", "greenEnergySub1": "ஈ.வி.சி.சி மீது கட்டணம் வசூலிக்கப்படுகிறது", "greenEnergySub2": "அக்டோபர் 2022 முதல்", "greenShare": "சூரிய பங்கு", "greenShareSub1": "வழங்கிய ஆற்றல்", "greenShareSub2": "சூரிய மற்றும் பேட்டரி சேமிப்பு", "power": "கட்டணம் வசூலிக்கும் ஆற்றல்", "powerSub1": "{செயலில் உள்ள வாடிக்கையாளர்கள் {activeClients} பங்கேற்பாளர்கள்", "powerSub2": "சார்சிங்…", "tabTitle": "நேரடி சமூகம்"}, "savings": {"co2Saved": "{value} சேமிக்கப்பட்டது", "co2Title": "கோ -உமிழ்வு", "configurePriceCo2": "விலை மற்றும் CO₂ தரவை எவ்வாறு கட்டமைப்பது என்பதை அறிக.", "footerLong": "{percent} சூரிய ஆற்றல்", "footerShort": "{percent} சூரிய", "modalTitle": "சார்ச் ஆற்றல் கண்ணோட்டம்", "moneySaved": "{value} சேமிக்கப்பட்டது", "percentGrid": "{grid} kWh கட்டம்", "percentSelf": "{self} kWh சூரிய", "percentTitle": "சூரிய ஆற்றல்", "period": {"30d": "கடைசி 30 நாட்கள்", "365d": "கடைசி 365 நாட்கள்", "thisYear": "இந்த ஆண்டு", "total": "எல்லா நேரமும்"}, "periodLabel": "காலம்:", "priceTitle": "ஆற்றல் விலை", "referenceGrid": "கட்டம்", "referenceLabel": "குறிப்பு தரவு:", "tabTitle": "எனது தரவு"}, "sponsor": {"becomeSponsor": "ஒரு ச்பான்சராகுங்கள்", "becomeSponsorExtended": "ச்டிக்கர்களைப் பெற நேரடியாக எங்களை ஆதரிக்கவும்.", "confetti": "கான்ஃபெட்டிக்கு தயாரா?", "confettiPromise": "நீங்கள் ச்டிக்கர்கள் மற்றும் டிசிட்டல் கான்ஃபெட்டி பெறுகிறீர்கள்", "sticker": "… அல்லது ஈ.வி.சி.சி ச்டிக்கர்கள்?", "supportUs": "எங்கள் நோக்கம் சோலார் விதிமுறைகளை வசூலிப்பதே ஆகும். உங்களுக்கு தகுதியானதை செலுத்துவதன் மூலம் ஈ.வி.சி.சி.க்கு உதவுங்கள்.", "thanks": "நன்றி, {sponsor}! உங்கள் பங்களிப்பு EVCC ஐ மேலும் உருவாக்க உதவுகிறது.", "titleNoSponsor": "எங்களுக்கு ஆதரவளிக்கவும்", "titleSponsor": "நீங்கள் ஒரு ஆதரவாளர்", "titleTrial": "சோதனை முறை", "titleVictron": "விக்ட்ரான் எனர்சியால் வழங்கப்படுகிறது", "trial": "நீங்கள் சோதனை பயன்முறையில் இருக்கிறீர்கள், எல்லா அம்சங்களையும் பயன்படுத்தலாம். தயவுசெய்து திட்டத்தை ஆதரிப்பதைக் கவனியுங்கள்.", "victron": "நீங்கள் விக்ட்ரான் எனர்சி வன்பொருளில் ஈ.வி.சி.சியைப் பயன்படுத்துகிறீர்கள், மேலும் அனைத்து அம்சங்களுக்கும் அணுகல் உள்ளது."}, "telemetry": {"optIn": "எனது தரவை பங்களிக்க விரும்புகிறேன்.", "optInMoreDetails": "மேலும் விவரங்கள் {0}.", "optInMoreDetailsLink": "இங்கே", "optInSponsorship": "ஒப்புரவாளர் தேவை."}, "version": {"availableLong": "புதிய பதிப்பு கிடைக்கிறது", "modalCancel": "ரத்துசெய்", "modalDownload": "பதிவிறக்கம்", "modalInstalledVersion": "நிறுவப்பட்ட பதிப்பு", "modalNoReleaseNotes": "வெளியீட்டுக் குறிப்புகள் இல்லை. புதிய பதிப்பைப் பற்றிய கூடுதல் தகவல்:", "modalTitle": "புதிய பதிப்பு கிடைக்கிறது", "modalUpdate": "நிறுவு", "modalUpdateNow": "இப்போது நிறுவவும்", "modalUpdateStarted": "ஈ.வி.சி.சியின் புதிய பதிப்பைத் தொடங்குதல்…", "modalUpdateStatusStart": "நிறுவல் தொடங்கியது:"}}, "forecast": {"co2": {"average": "சராசரி", "lowestHour": "தூய்மையான மணி", "range": "வீச்சு"}, "modalTitle": "முன்னறிவிப்பு", "price": {"average": "சராசரி", "lowestHour": "மலிவான மணி", "range": "வீச்சு"}, "solar": {"dayAfterTomorrow": "நாளை அடுத்த நாள்", "partly": "ஓரளவு", "remaining": "மீதமுள்ள", "today": "இன்று", "tomorrow": "நாளை"}, "solarAdjust": "உண்மையான உற்பத்தித் தரவின் அடிப்படையில் சூரிய முன்னறிவிப்பை சரிசெய்யவும் {percent}.", "type": {"co2": "கோ", "price": "விலை", "solar": "சூரிய"}}, "header": {"about": "பற்றி", "blog": "வலைப்பதிவு", "docs": "ஆவணங்கள்", "github": "கிடப்", "login": "வாகன உள்நுழைவுகள்", "logout": "வெளியேறு", "nativeSettings": "சேவையகத்தை மாற்றவும்", "needHelp": "உதவி தேவையா?", "sessions": "சார்சிங் அமர்வுகள்"}, "help": {"discussionsButton": "அறிவிலிமையம் விவாதங்கள்", "documentationButton": "ஆவணங்கள்", "issueButton": "ஒரு பிழையைப் புகாரளிக்கவும்", "issueDescription": "ஒரு விசித்திரமான அல்லது தவறான நடத்தை கிடைத்ததா?", "logsButton": "பதிவுகளைக் காண்க", "logsDescription": "பிழைகளுக்கான பதிவுகளை சரிபார்க்கவும்.", "modalTitle": "உதவி தேவையா?", "primaryActions": "ஏதாவது செய்ய வேண்டிய வழியில் வேலை செய்யாது? இவை உதவி பெற நல்ல இடங்கள்.", "restart": {"cancel": "கைவிடு", "confirm": "ஆம், மறுதொடக்கம் செய்!", "description": "Under normal circumstances restarting should not be necessary. Please consider filing a பிழை if you need பெறுநர் மறுதொடக்கம் evcc on a regular basis.", "disclaimer": "Note: evcc will terminate and rely on the operating மண்டலம் பெறுநர் மறுதொடக்கம் the service.", "modalTitle": "Are you sure you want பெறுநர் restart?"}, "restartButton": "மறுதொடக்கம்", "restartDescription": "அதை மீண்டும் அணைக்க முயற்சித்தீர்களா?", "secondaryActions": "Still not able பெறுநர் solve your problem? Here அரே some more heavy-handed options."}, "log": {"areaLabel": "பரப்பளவில் வடிகட்டவும்", "areas": "அனைத்து பகுதிகளும்", "download": "முழுமையான பதிவிறக்கம் பதிவு", "levelLabel": "பதிவு நிலை மூலம் வடிகட்டவும்", "nAreas": "{count} பகுதிகள்", "noResults": "பொருந்தக்கூடிய பதிவு உள்ளீடுகள் இல்லை.", "search": "தேடு", "selectAll": "அனைத்தையும் தெரிவுசெய்", "showAll": "Show அனைத்தும் entries", "title": "பதிவுகள்", "update": "ஆட்டோ புதுப்பிப்பு"}, "loginModal": {"cancel": "கைவிடு", "demoMode": "டெமோ பயன்முறையில் உள்நுழைவு ஆதரிக்கப்படவில்லை.", "error": "உள்நுழைவு தோல்வியடைந்தது: ", "iframeHint": "Open evcc in a புதிய tab.", "iframeIssue": "Your password is correct, but your browser seems பெறுநர் have dropped the authentication cookie. This can happen if you ஓடு evcc in an iframe வழிமம் HTTP.", "invalid": "கடவுச்சொல் தவறானது.", "login": "புகுபதிவு", "password": "நிர்வாகி கடவுச்சொல்", "reset": "கடவுச்சொல்லை மீட்டமைக்கவா?", "title": "ஏற்பு"}, "main": {"chargingPlan": {"active": "செயலில்", "addRepeatingPlan": "மறுநிகழ்வு திட்டத்தைச் சேர்", "arrivalTab": "வருகை", "day": "நாள்", "departureTab": "புறப்படுதல்", "goal": "மின்சேர்வி இலக்கு", "modalTitle": "மின்சேர்வி திட்டம்", "none": "எதுவுமில்லை", "planNumber": "திட்டம் {number}", "preconditionDescription": "பேட்டரி முன்நிபந்தனைக்கு புறப்படுவதற்கு முன் {காலம் {காலம்.", "preconditionLong": "தாமதமாக சார்சிங்", "preconditionOptionAll": "எல்லாம்", "preconditionOptionNo": "இல்லை", "preconditionShort": "தாமதமாக", "remove": "அகற்று", "repeating": "மறுநிகழ்தல்", "repeatingPlans": "மறுநிகழ்வு திட்டங்கள்", "selectAll": "அனைத்தையும் தேர்ந்தெடு", "time": "நேரம்", "title": "திட்டம்", "titleMinSoc": "\"குறை. மின்னூட்டு\"", "titleTargetCharge": "புறப்படுதல்", "unsavedChanges": "There அரே unsaved changes. இடு now?", "update": "இடு", "weekdays": "நாட்கள்"}, "energyflow": {"battery": "மின்கலம்", "batteryCharge": "மின்கலம் மின்சேர்வி", "batteryDischarge": "மின்கலம் மின்நீக்கி", "batteryGridChargeActive": "கட்டம் சார்சிங் செயலில்", "batteryGridChargeLimit": "எப்போது கட்டம் சார்சிங்", "batteryHold": "மின்கலம் (பூட்டபட்டது)", "batteryTooltip": "{energy} ({total}) இன் {soc}", "forecastTooltip": "முன்னறிவிப்பு: இன்று சூரிய விளைவாக்கம்", "gridImport": "கட்டம் பயன்பாடு", "homePower": "நுகர்வு", "loadpoints": "Charger| மின்னூட்டி | {count} chargers", "noEnergy": "No அளவி data", "pv": "சூரிய குடும்பம்", "pvExport": "கட்ட ஏற்றுமதி", "pvProduction": "விளைவாக்கம்", "selfConsumption": "தன்-நுகர்வு"}, "heatingStatus": {"charging": "வெப்பமாக்கல்…", "connected": "காத்திருப்பு.", "vehicleLimit": "ஈட்டர் வரம்பு", "waitForVehicle": "Ready. Waiting க்கு heater…"}, "loadpoint": {"avgPrice": "⌀ விலை", "charged": "மின்சேர்க்கப்பட்டது", "co2": "Co co₂", "duration": "காலம்", "fallbackName": "மின்சேர்வி புள்ளி", "finished": "நேரம் முடிந்தது", "power": "ஆற்றல்", "price": "செலவு", "remaining": "மீதம்", "remoteDisabledHard": "{source}: அணைக்கப்பட்டது", "remoteDisabledSoft": "{source}: turned அணை adaptive solar-charging", "solar": "ஞாயிறு"}, "loadpointSettings": {"batteryBoost": {"description": "Fast மின்னூட்டு இருந்து வீடு battery.", "label": "மின்கலம் ஊட்டம்", "mode": "ஞாயிறு மற்றும் குறை+ஞாயிறு பயன்முறையில் மட்டுமே கிடைக்கும்.", "once": "Boost செயலில் க்கு this charging session."}, "batteryUsage": "முகப்பு பேட்டரி", "currents": "மின்சேர்வி மின்னோட்டம்", "default": "இயல்புநிலை", "disclaimerHint": "குறிப்பு:", "limitSoc": {"description": "இந்த வாகனம் இணைக்கப்படும்போது பயன்படுத்தப்படும் மின்சேர்வி வரம்பு.", "label": "இயல்புநிலை வரம்பு"}, "maxCurrent": {"label": "அதிக. மின்னோட்டம்"}, "minCurrent": {"label": "குறை. மின்னோட்டம்"}, "minSoc": {"description": "The vehicle gets „fast” charged பெறுநர் {0} in solar mode. Then continues with solar surplus. Useful பெறுநர் ensure a சிறுமம் வீச்சு இரட்டை க்கு darker days.", "label": "Min. மின்னூட்டு %"}, "onlyForSocBasedCharging": "These விருப்பங்கள் அரே only available க்கு vehicles with known charging level.", "phasesConfigured": {"label": "கட்டங்கள்", "no1p3pSupport": "How is your மின்னூட்டி connected?", "phases_0": "தானாக-மாறுதல்", "phases_1": "1 கட்டம்", "phases_1_hint": "({min} பெறுநர் {max})", "phases_3": "3 கட்டம்", "phases_3_hint": "({min} பெறுநர் {max})"}, "smartCostCheap": "Cheap வலைவாய் Charging", "smartCostClean": "Clean வலைவாய் Charging", "title": "அமைப்புகள் {0}", "vehicle": "வண்டி"}, "mode": {"minpv": "குறை+ஞாயிறு", "now": "வேகமாக", "off": "அணை", "pv": "ஞாயிறு", "smart": "அறிவாளி"}, "provider": {"login": "புகுபதிகை", "logout": "விடு பதிகை"}, "startConfiguration": "உள்ளமைவைத் தொடங்குவோம்", "targetCharge": {"activate": "செயல்படுத்து", "co2Limit": "CO₂ வரம்பு {co2}", "costLimitIgnore": "கட்டமைக்கப்பட்ட {limit} இந்தக் காலகட்டத்தில் புறக்கணிக்கப்படும்.", "currentPlan": "செயலில் திட்டம்", "descriptionEnergy": "எப்போது {targetEnergy} வாகனத்தில் ஏற்றப்பட வேண்டும்?", "descriptionSoc": "When should the vehicle be charged பெறுநர் {targetSoc}?", "goalReached": "இலக்கு ஏற்கனவே அடைந்தது", "inactiveLabel": "இலக்கு நேரம்", "nextPlan": "அடுத்த திட்டம்", "notReachableInTime": "இலக்கு எட்டப்படும் {overrun} பின்னர்.", "onlyInPvMode": "கட்டணம் வசூலிக்கும் திட்டம் சூரிய பயன்முறையில் மட்டுமே வேலை செய்கிறது.", "planDuration": "மின்சேர்வி நேரம்", "planPeriodLabel": "காலசுழற்சி", "planPeriodValue": "{start} பெறுநர் {end}", "planUnknown": "இன்னும் அறியப்படவில்லை", "preview": "முன்னோட்டம் திட்டம்", "priceLimit": "விலை வரம்பு {price}", "remove": "அகற்று", "setTargetTime": "எதுவுமில்லை", "targetIsAboveLimit": "{limit} இன் கட்டமைக்கப்பட்ட மின்சேர்வி வரம்பு இந்தக் காலகட்டத்தில் புறக்கணிக்கப்படும்.", "targetIsAboveVehicleLimit": "வாகன வரம்பு இலக்கு மின்சேர்வியை விடக் கீழே உள்ளது.", "targetIsInThePast": "எதிர்காலத்தில் ஒரு நேரத்தைத் தேர்ந்தெடுங்கள், மார்டி.", "targetIsTooFarInTheFuture": "We will adjust the plan அச் soon அச் we know more பற்றி the future.", "title": "இலக்கு நேரம்", "today": "இன்று", "tomorrow": "நாளை", "update": "புதுப்பிப்பு", "vehicleCapacityDocs": "Learn how பெறுநர் configure it.", "vehicleCapacityRequired": "The vehicle மின்கலம் capacity is required பெறுநர் estimate the charging time."}, "targetChargePlan": {"chargeDuration": "மின்சேர்வி நேரம்", "co2Label": "CO₂ உமிழ்வு ⌀", "priceLabel": "ஆற்றல் விலை", "timeRange": "{day} {range} எச்", "unknownPrice": "இன்னும் தெரியவில்லை"}, "targetEnergy": {"label": "வரம்பு", "noLimit": "எதுவுமில்லை"}, "vehicle": {"addVehicle": "வண்டி சேர்க்கவும்", "changeVehicle": "வாகனத்தை மாற்றவும்", "detectionActive": "வண்டி கண்டறிதல்…", "fallbackName": "வண்டி", "moreActions": "மேலும் செயல்கள்", "none": "வண்டி இல்லை", "notReachable": "வண்டி அடைய முடியாது. EVCC ஐ மறுதொடக்கம் செய்ய முயற்சிக்கவும்.", "targetSoc": "வரம்பு", "temp": "தற்காலிக.", "tempLimit": "தற்காலிக வரம்பு", "unknown": "விருந்தினர் வண்டி", "vehicleSoc": "கட்டணம்"}, "vehicleStatus": {"awaitingAuthorization": "அங்கீகாரத்திற்காக காத்திருக்கிறது.", "batteryBoost": "பேட்டரி பூச்ட் செயலில்.", "charging": "சார்சிங்…", "cheapEnergyCharging": "மலிவான ஆற்றல் கிடைக்கிறது.", "cheapEnergyNextStart": "{duration} இல் மலிவான ஆற்றல்.", "cheapEnergySet": "விலை வரம்பு தொகுப்பு.", "cleanEnergyCharging": "தூய்மையான ஆற்றல் கிடைக்கிறது.", "cleanEnergyNextStart": "{duration} இல் தூய்மையான ஆற்றல்.", "cleanEnergySet": "கோ லிமிட் செட்.", "climating": "முன் கண்டிசனிங் கண்டறியப்பட்டது.", "connected": "இணைக்கப்பட்டுள்ளது.", "disconnectRequired": "அமர்வு நிறுத்தப்பட்டது. தயவுசெய்து மீண்டும் இணைக்கவும்.", "disconnected": "துண்டிக்கப்பட்டது.", "feedinPriorityNextStart": "அதிக தீவன விகிதங்கள் {duration} இல் தொடங்குகின்றன.", "feedinPriorityPausing": "சூரிய சார்ச் ஊட்டத்தை அதிகரிக்க இடைநிறுத்தப்பட்டது.", "finished": "முடிந்தது.", "minCharge": "{Soc} க்கு குறைந்தஅளவு கட்டணம்.", "pvDisable": "போதுமான உபரி இல்லை. விரைவில் இடைநிறுத்துதல்.", "pvEnable": "உபரி கிடைக்கிறது. விரைவில் தொடங்குகிறது.", "scale1p": "விரைவில் 1-கட்ட கட்டணம் வசூலிப்பதைக் குறைத்தல்.", "scale3p": "விரைவில் 3-கட்ட கட்டணம் வசூலிக்கிறது.", "targetChargeActive": "சார்சிங் பிளான் செயலில். மதிப்பிடப்பட்ட பூச்சு {duration}.", "targetChargePlanned": "சார்சிங் திட்டம் {duration} இல் தொடங்குகிறது.", "targetChargeWaitForVehicle": "திட்டம் தயாராக உள்ளது. வாகனத்திற்காகக் காத்திருக்கிறது …", "vehicleLimit": "வாகன வரம்பு", "vehicleLimitReached": "வாகன வரம்பு எட்டப்பட்டது.", "waitForVehicle": "ஆயத்தம். வாகனத்திற்காக காத்திருக்கிறது…", "welcome": "இணைப்பை உறுதிப்படுத்த குறுகிய ஆரம்ப கட்டணம்."}, "vehicles": "பார்க்கிங்", "welcome": "வணக்கம்!"}, "notifications": {"dismissAll": "அனைத்தையும் நிராகரிக்கவும்", "logs": "முழு பதிவுகளையும் காண்க", "modalTitle": "அறிவிப்புகள்"}, "offline": {"configurationError": "தொடக்கத்தின் போது பிழை. உங்கள் உள்ளமைவை சரிபார்த்து மறுதொடக்கம் செய்யுங்கள்.", "message": "சேவையகத்துடன் இணைக்கப்படவில்லை.", "restart": "மறுதொடக்கம்", "restartNeeded": "மாற்றங்களைப் பயன்படுத்த வேண்டும்.", "restarting": "சேவையகம் ஒரு கணத்தில் திரும்பும்."}, "passwordModal": {"description": "உள்ளமைவு அமைப்புகளைப் பாதுகாக்க கடவுச்சொல்லை அமைக்கவும். உள்நுழைவு இல்லாமல் முதன்மையான திரையைப் பயன்படுத்துவது இன்னும் சாத்தியமாகும்.", "empty": "கடவுச்சொல் காலியாக இருக்கக்கூடாது", "error": "பிழை: ", "labelCurrent": "தற்போதைய கடவுச்சொல்", "labelNew": "புதிய கடவுச்சொல்", "labelRepeat": "கடவுச்சொல்லை மீண்டும்", "newPassword": "கடவுச்சொல்லை உருவாக்கு", "noMatch": "கடவுச்சொற்கள் பொருந்தவில்லை", "titleNew": "நிர்வாகி கடவுச்சொல்லை அமைக்கவும்", "titleUpdate": "நிர்வாகி கடவுச்சொல்லைப் புதுப்பிக்கவும்", "updatePassword": "கடவுச்சொல்லைப் புதுப்பிக்கவும்"}, "session": {"cancel": "ரத்துசெய்", "co2": "கோ ₂", "date": "காலசுழற்சி", "delete": "நீக்கு", "finished": "முடிந்தது", "meter": "மீட்டர்", "meterstart": "மீட்டர் தொடக்க", "meterstop": "மீட்டர் நிறுத்தம்", "odometer": "மைலேச்", "price": "விலை", "started": "தொடங்கியது", "title": "சார்சிங் அமர்வு"}, "sessions": {"avgPower": "⌀ ஆற்றல்", "avgPrice": "⌀ விலை", "chargeDuration": "காலம்", "chartTitle": {"avgCo2ByGroup": "⌀ co₂ {byGroup}", "avgPriceByGroup": "⌀ விலை {byGroup}", "byGroupLoadpoint": "சார்சிங் பாயிண்ட் மூலம்", "byGroupVehicle": "வண்டி மூலம்", "energy": "சார்ச் செய்யப்பட்ட ஆற்றல்", "energyGrouped": "சோலார் வெர்சச் கட்டம் ஆற்றல்", "energyGroupedByGroup": "ஆற்றல் {byGroup}", "energySubSolar": "{value} சூரிய", "energySubTotal": "{value} மொத்தம்", "groupedCo2ByGroup": "CO₂-AMOUNT {byGroup}", "groupedPriceByGroup": "மொத்த செலவு {byGroup}", "historyCo2": "கோ-உமிழ்வுகள்", "historyCo2Sub": "{value} மொத்தம்", "historyPrice": "வசூலிக்கும் செலவுகள்", "historyPriceSub": "{value} மொத்தம்", "solar": "ஆண்டு முழுவதும் சூரிய பங்கு", "solarByGroup": "சோலார் சேர் {byGroup}"}, "co2": "⌀ co₂", "csv": {"chargedenergy": "ஆற்றல் (கிலோவாட்)", "chargeduration": "காலம்", "co2perkwh": "Co₂/kWh", "created": "உருவாக்கப்பட்டது", "finished": "முடிந்தது", "identifier": "அடையாளங்காட்டி", "loadpoint": "சார்சிங் பாயிண்ட்", "meterstart": "மீட்டர் தொடக்க (கிலோவாட்)", "meterstop": "மீட்டர் நிறுத்தம் (கிலோவாட்)", "odometer": "மைலேச் (கி.மீ)", "price": "விலை", "priceperkwh": "விலை/கிலோவாட்", "solarpercentage": "சூரிய (%)", "vehicle": "வண்டி"}, "csvPeriod": "பதிவிறக்கம் {period} சிஎச்வி", "csvTotal": "மொத்த காபிம ஐ பதிவிறக்கவும்", "date": "தொடக்க", "energy": "கட்டணம் வசூலிக்கப்பட்டது", "filter": {"allLoadpoints": "அனைத்து சார்சிங் புள்ளிகளும்", "allVehicles": "அனைத்து வாகனங்களும்", "filter": "வடிகட்டி"}, "group": {"co2": "உமிழ்வு", "grid": "கட்டம்", "price": "விலை", "self": "சூரிய"}, "groupBy": {"loadpoint": "சார்சிங் பாயிண்ட்", "none": "மொத்தம்", "vehicle": "வண்டி"}, "loadpoint": "சார்சிங் பாயிண்ட்", "noData": "இந்த மாதத்தில் சார்சிங் அமர்வுகள் இல்லை.", "overview": "கண்ணோட்டம்", "period": {"month": "மாதம்", "total": "மொத்தம்", "year": "ஆண்டு"}, "price": "செலவு", "reallyDelete": "நீங்கள் உண்மையில் இந்த அமர்வை நீக்க விரும்புகிறீர்களா?", "showIndividualEntries": "தனிப்பட்ட அமர்வுகளைக் காட்டு", "solar": "சூரிய", "title": "சார்சிங் அமர்வுகள்", "total": "மொத்தம்", "type": {"co2": "கோ ₂", "price": "விலை", "solar": "சூரிய"}, "vehicle": "வண்டி"}, "settings": {"fullscreen": {"enter": "முழுத் திரையில் உள்ளிடவும்", "exit": "முழுத்திரை வெளியேறு", "label": "முழுத்திரை"}, "hiddenFeatures": {"label": "சோதனை", "value": "சோதனை இடைமுகம் அம்சங்களைக் காட்டு."}, "language": {"auto": "தானியங்கி", "label": "மொழி"}, "sponsorToken": {"expires": "நீங்கள் ஒப்புரவாளர் கிள்ளாக்கு காலாவதியாகிறது {inXDays}. {getNewToken} மற்றும் அதை இங்கே புதுப்பிக்கவும்.", "getNew": "ஒரு புதிய ஒன்றைப் பிடிக்கவும்", "hint": "குறிப்பு: எதிர்காலத்தில் இதை தானியக்கமாக்குவோம்."}, "telemetry": {"label": "டெலிமெட்ரி"}, "theme": {"auto": "கணினி", "dark": "இருண்ட", "label": "வடிவமைப்பு", "light": "ஒளி"}, "time": {"12h": "12 ம", "24h": "24 எச்", "label": "நேர வடிவம்"}, "title": "பயனர் இடைமுகம்", "unit": {"km": "கி.மீ", "label": "அலகுகள்", "mi": "மைல்கள்"}}, "smartCost": {"activeHours": "{active} இன் {total} \"", "activeHoursLabel": "செயலில் நேரம்", "applyToAll": "எல்லா இடங்களிலும் விண்ணப்பிக்கவா?", "batteryDescription": "கட்டத்தில் இருந்து ஆற்றலுடன் வீட்டு பேட்டரியை வசூலிக்கிறது.", "cheapTitle": "மலிவான கட்டம் சார்சிங்", "cleanTitle": "தூய்மையான கட்டம் சார்சிங்", "co2Label": "கோ உமிழ்வு", "co2Limit": "கோ வரம்பு", "loadpointDescription": "சோலார் பயன்முறையில் தற்காலிகமாக வேகமாக சார்ச் செய்ய உதவுகிறது.", "modalTitle": "அறிவுள்ள கிரிட் சார்சிங்", "none": "எதுவுமில்லை", "priceLabel": "ஆற்றல் விலை", "priceLimit": "விலை வரம்பு", "resetAction": "வரம்பை அகற்று", "resetWarning": "மாறும் கட்டம் விலை அல்லது கோ-மூல கட்டமைக்கப்படவில்லை. இருப்பினும், {வரம்பு of இன் வரம்பு இன்னும் உள்ளது. உங்கள் உள்ளமைவை தூய்மை செய்யவா?", "saved": "சேமிக்கப்பட்டது."}, "smartFeedInPriority": {"activeHoursLabel": "இடைநிறுத்தப்பட்ட மணிநேரம்", "description": "இலாபகரமான கட்டம் ஊட்டத்திற்கு முன்னுரிமை அளிக்க அதிக விலையில் கட்டணம் வசூலிப்பதை இடைநிறுத்துகிறது.", "priceLabel": "தீவன வீதம்", "priceLimit": "தீவன வரம்பு", "resetWarning": "மாறும் ஃபீட்-இன் கட்டணம் எதுவும் கட்டப்படவில்லை. இருப்பினும், {வரம்பு of இன் வரம்பு இன்னும் உள்ளது. உங்கள் உள்ளமைவை தூய்மை செய்யவா?", "title": "தீவன முன்னுரிமை"}, "startupError": {"configFile": "உள்ளமைவு கோப்பு பயன்படுத்தப்பட்டது:", "configuration": "கட்டமைப்பு", "description": "தயவுசெய்து உங்கள் உள்ளமைவு கோப்பை சரிபார்க்கவும். பிழை செய்தி உதவவில்லை என்றால், {0} ஐப் பாருங்கள்.", "discussions": "அறிவிலிமையம் விவாதங்கள்", "fixAndRestart": "தயவுசெய்து சிக்கலை சரிசெய்து சேவையகத்தை மறுதொடக்கம் செய்யுங்கள்.", "hint": "குறிப்பு: உங்களிடம் தவறான சாதனம் இருக்கலாம் (இன்வெர்ட்டர், மீட்டர்,…). உங்கள் பிணைய இணைப்புகளை சரிபார்க்கவும்.", "lineError": "{0} இல் பிழை.", "lineErrorLink": "வரி {0}", "restartButton": "மறுதொடக்கம்", "title": "தொடக்க பிழை"}}