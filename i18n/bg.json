{"batterySettings": {"batteryLevel": "Батерия %", "bufferStart": {"above": "когато над {soc}", "full": "при {soc}.", "never": "само при наличие на излишък"}, "capacity": "{energy} от {total}", "control": "Управление на батерията", "discharge": "Предотврати разреждането в бърз режим и планираното зареждане", "disclaimerHint": "Бележка:", "disclaimerText": "Тези настройки важат само за режим на работа използвайки енергия от фотоволтаици. Зареждането ще бъде настроено спрямо тях", "gridChargeTab": "Зареждане от мрежата", "legendBottomName": "Приоритизиране на зареждането на домашната батерия", "legendBottomSubline": "докато достигне {soc}.", "legendMiddleName": "Приоритизиране на зареждането на превозното средство", "legendMiddleSubline": "когато домашната батерия е над {soc}.", "legendTopAutostart": "автоматично стартиране", "legendTopName": "зареждане използвайки батерия", "legendTopSubline": "когато домашната батерия е над {soc}.", "modalTitle": "До<PERSON><PERSON><PERSON>на батерия", "usageTab": "Използване на батерията"}, "config": {"aux": {"description": "Устройство което определя потреблението си според достъпния излишък на енергия (напр. умен нагревател). evcc очаква това устройство да намали консумацията си на енергия ако това е необходимо.", "titleAdd": "Добави саморегулиращ се консуматор", "titleEdit": "Промени саморегулиращ се консуматор"}, "battery": {"titleAdd": "Добави батерия", "titleEdit": "Редактиране на батерията"}, "charge": {"titleAdd": "Добави електромер за зареждането", "titleEdit": "Промени електромер за зареждането"}, "charger": {"chargers": "Зарядни станции", "generic": "Общи интеграции", "heatingdevices": "Загревателни устройства", "ocppHelp": "Копирай този адрес в конфигурацията на твоите зарядни станции.", "ocppLabel": "Адрес на OCPP сървъра", "switchsockets": "Контакти с прекъсвач", "template": "Производител", "titleAdd": {"charging": "Добави зарядно"}, "titleEdit": {"charging": "Промени зарядно"}}, "circuits": {"description": "Гарантир<PERSON>, че сумата от всички точки на натоварване, свързани към една верига, не надвишава конфигурираните граници на мощност и ток. Веригите могат да бъдат вложени, за да се изгради йерархия.", "title": "Управление на натоварването"}, "control": {"description": "Обикновено стойностите по подразбиране са наред. Променяйте ги само ако знаете какво правите.", "descriptionInterval": "Цикъл на актуализация на контролния цикъл в секунди. Определя колко често evcc чете данни от измервателния уред, регулира мощността на зареждане и актуализира потребителския интерфейс. Кратките интервали (< 30s) могат да причинят осцилации и нежелано поведение.", "descriptionResidualPower": "Премества работната точка на контролния цикъл. Ако имате домашна батерия, се препоръчва да зададете стойност от 100 W. По този начин батерията ще има лек приоритет пред използването на мрежата.", "labelInterval": "Интервал на актуализация", "labelResidualPower": "Остатъчна мощност", "title": "Поведение на управление"}, "deviceValue": {"amount": "Количество", "broker": "Брокер", "bucket": "Кофа", "capacity": "Капацитет", "chargeStatus": "Статус", "chargeStatusA": "не е свързан", "chargeStatusB": "свързан", "chargeStatusC": "зареждане", "chargeStatusE": "няма мощност", "chargeStatusF": "грешка", "chargedEnergy": "Заредено", "co2": "Мрежа CO₂", "configured": "Конфигу<PERSON><PERSON><PERSON><PERSON>н", "controllable": "Контрол<PERSON><PERSON><PERSON><PERSON>м", "currency": "Валута", "current": "Електричеството", "currentRange": "Електричеството", "enabled": "Готов за зареждане", "energy": "Енергия", "feedinPrice": "Цена за изкупуване", "gridPrice": "Цена на мрежата", "heaterTempLimit": "Лимит на загревателя", "hemsType": "Система", "identifier": "RFID идентификатор", "no": "не", "odometer": "одометър", "org": "Организация", "phaseCurrents": "Текущ L1, L2, L3", "phasePowers": "Мощност L1, L2, L3", "phaseVoltages": "Напрежение L1, L2, L3", "phases1p3p": "Фазов превключвател", "power": "Мощност", "powerRange": "Мощност", "range": "Об<PERSON><PERSON><PERSON>т", "singlePhase": "Една фаза", "soc": "Състояние на заряда", "solarForecast": "Соларна прогноза", "temp": "Температура", "topic": "Тема", "url": "URL", "vehicleLimitSoc": "Максимален заряд на автомобила", "yes": "да"}, "deviceValueChargeStatus": {"A": "А (не е свързан)", "B": "Б (свързан)", "C": "В (зарежда се)"}, "devices": {"auxMeter": "Смарт потребител", "batteryStorage": "Батерия", "solarSystem": "Фотоволтаична система"}, "eebus": {"description": "Основна конфигурация за комуникация с други EEBus устройства.", "title": "EEBus"}, "ext": {"description": "Може да бъде използвано за управление на товара или за статистически анализ.", "titleAdd": "Добави Външен Електромер", "titleEdit": "Промени Външен Електромер"}, "form": {"danger": "Опасност", "deprecated": "изваден от употреба", "example": "Пример", "optional": "незадължителен"}, "general": {"cancel": "Отказ", "delete": "Изтрий", "docsLink": "Виж документацията.", "experimental": "Експериментален", "hideAdvancedSettings": "Скрий разширените настройки", "off": "изключено", "on": "включено", "password": "Парола", "readFromFile": "Прочети от файл", "remove": "Премахни", "save": "Запази", "showAdvancedSettings": "Покажи разширените настройки", "telemetry": "Телеметрия", "templateLoading": "Зарежда се...", "title": "Заглавие", "validateSave": "Провери и запази"}, "grid": {"title": "Мрежов измервателен уред", "titleAdd": "Добави мрежов измервателен уред", "titleEdit": "Редактирай мрежов измервателен уред"}, "hems": {"description": "Свържете evcc с друга система за управление на домашната енергия.", "title": "„Система за управление на енергията в дома“"}, "influx": {"description": "Записва данни за зареждане и други метрики в InfluxDB. Използвайте Grafana или други инструменти за визуализиране на данните.", "descriptionToken": "Проверете документацията на InfluxDB, за да научите как да създадете такъв. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Кофа", "labelCheckInsecure": "Разреши самоподписани сертификати", "labelDatabase": "База данни", "labelInsecure": "„Проверка на сертификат“", "labelOrg": "Организация", "labelPassword": "Парола", "labelToken": "API токен", "labelUrl": "URL", "labelUser": "Потребителско име", "title": "InfluxDB", "v1Support": "Нуждаете се от поддръжка за InfluxDB 1.x?", "v2Support": "Назад към InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Добави зарядно"}, "addMeter": "Добави електромер за зарядното", "cancel": "Откажи", "chargerError": {"charging": "Необходимо е да конфигурирате зарядното."}, "chargerLabel": {"charging": "Зарядно"}, "chargerPower11kw": "11 кВт", "chargerPower11kwHelp": "Ще бъде използван ток в диапазона от 6 до 16 А.", "chargerPower22kw": "22 кВт", "chargerPower22kwHelp": "Ще бъде използван ток в диапазона от 6 до 32 А.", "chargerPowerCustom": "други", "chargerPowerCustomHelp": "Определи специфичен токов диапазон.", "chargerTypeLabel": "Вид на зарадяното", "chargingTitle": "Зарежда", "circuitLabel": "Верига", "circuitUnassigned": "неразпределен", "defaultModeHelp": {"charging": "Режим на зареждане при свързване с автомобила."}, "defaultModeHelpKeep": "Запазва последно избрания режим на зареждане.", "defaultModeLabel": "Режим по подразбиране", "delete": "Изтрий", "electricalSubtitle": "При съмнения, допитайте се до електротехник.", "electricalTitle": "Електрически", "energyMeterHelp": "Допълни<PERSON><PERSON><PERSON>ен електромер, ако зарядното няма вграден.", "energyMeterLabel": "Електромер", "estimateLabel": "Интерполиране на нивата на зареждане между отделните актуализации", "maxCurrentHelp": "Трябва да е по-голям от минималния ток.", "maxCurrentLabel": "Максимален ток", "minCurrentHelp": "Използвай по-малко от 6 А, само ако знаеш какво правиш.", "minCurrentLabel": "Мини<PERSON>а<PERSON>ен ток", "noVehicles": "Няма конфигурирани автомобили.", "phases1p": "еднофазен", "phases3p": "трифазен", "phasesAutomatic": "Автоматични фази", "phasesAutomaticHelp": "Зарядното поддържа превключване между 1 и 3 фази. Поведението на фазите може да бъде настроено на главната страница.", "phasesHelp": "Номер на фазите свързани със зарядното.", "phasesLabel": "Фази", "pollIntervalDanger": "Редовната проверка на статус на автомобила може да изтощи акумулатора му. Някои производители дори активно блокират зареждането в такива случаи. Не се препоръчва! Използвайте тази настройка само ако знаете какво правите.", "pollIntervalHelp": "Време между актуализациите на статуса на автомобила. Малките интервали могат да изтощят батерията на автомобила.", "pollIntervalLabel": "Интервал на актуализациите", "pollModeAlways": "винаги", "pollModeAlwaysHelp": "Винаги прави запитвания за статуса в равни интервали.", "pollModeCharging": "зарежда", "pollModeChargingHelp": "Актуализ<PERSON><PERSON><PERSON>й статуса на автомобила само когато той се зарежда.", "pollModeConnected": "свързан", "pollModeConnectedHelp": "Актуализ<PERSON><PERSON><PERSON>й статуса на автомобила на равни интервали, когато той е свързан.", "pollModeLabel": "Поведение на актуализациите", "priorityHelp": "Зарядни станции с по-висок приоритет получават достъп до фотоволтаичния излишък.", "priorityLabel": "Приоритет", "save": "Запази", "showAllSettings": "Покажи всичко настройки", "solarBehaviorCustomHelp": "Дефинирай индивидуални нива и забавяния на активиране и деактивиране.", "solarBehaviorDefaultHelp": "Зареждай само с фотоволтаичен излишък. Стартирай след {enableDelay} излишък. Спри след като няма излишък за {disableDelay}.", "solarBehaviorLabel": "Фотоволтаично поведение", "solarModeCustom": "собствен", "solarModeMaximum": "само слънце", "thresholdDisableDelayLabel": "Забавяне на изключването", "thresholdDisableHelpInvalid": "Моля, използвай положителни стойности.", "thresholdDisableHelpPositive": "Спри зареждането, след като повече от {power} се използват от мрежата за {delay}.", "thresholdDisableHelpZero": "Спри когато минималната мощност на зареждане не може да бъде достигната за {delay}.", "thresholdDisableLabel": "Деактивирай мощност от мрежата", "thresholdEnableDelayLabel": "Активи<PERSON><PERSON>й забавяне", "thresholdEnableHelpInvalid": "Моля използвай негативни стойности.", "thresholdEnableHelpNegative": "Започни зареждането, след като има {surplus} излищък за {delay}.", "thresholdEnableHelpZero": "Започни, когато минималната мощност на зарежда от излишък е достъпна за {delay}.", "thresholdEnableLabel": "Активи<PERSON><PERSON>й мощност от мрежата", "titleAdd": "Добави зарядна станция", "titleEdit": "Промени зарядна станция", "titleExample": "<PERSON><PERSON><PERSON><PERSON><PERSON>, На<PERSON>е<PERSON>, итн.", "titleLabel": "Название", "vehicleAutoDetection": "автоматично намиране", "vehicleHelpAutoDetection": "Автоматично избиране на най-подходящия автомобил. Ръчната промяна е възможна.", "vehicleHelpDefault": "Приеми че този автомобил винаги зарежда тук. Автоматичното разпознаване е изключено. Ръчната промяна е възможна.", "vehicleLabel": "Автомобил по подразбиране", "vehiclesTitle": "Превозни средства"}, "main": {"addAdditional": "Добави допълнителен електромер", "addGrid": "Добави електромер на мрежата", "addLoadpoint": "Добави зарядна точка", "addPvBattery": "Добави соларен панел или батерия", "addTariffs": "Добави тарифи", "addVehicle": "Добави автомобил", "configured": "Конфигу<PERSON><PERSON><PERSON><PERSON>н", "edit": "редакти<PERSON><PERSON>й", "loadpointRequired": "Поне една зарядна станция трябва да бъде конфигурирана.", "name": "Име", "title": "Конфигурация", "unconfigured": "не е конфигуриран", "vehicles": "Моите Автомобили", "yaml": "Конфигурирано в evcc.yaml. Не може да се редактира в потребителския интерфейс."}, "messaging": {"description": "Получавайте съобщения за вашите сесии на зареждане.", "title": "Notifications"}, "meter": {"cancel": "Отказ", "delete": "Изтрий", "option": {"aux": "Добави интелигентен консуматор", "battery": "Добави домашна батерия", "ext": "Добави допълнителен електромер", "pv": "Добави фотоволтаичен електромер"}, "save": "Запази", "template": "Производител", "titleChoice": "Какво искате да добавите?", "validateSave": "Валиди<PERSON><PERSON><PERSON> и запази"}, "modbus": {"baudrate": "Честота на бодовете", "comset": "ComSet", "connection": "Връзка с Modbus", "connectionHintSerial": "Устройството е директно свързано към evcc през RS485 интерфейс.", "connectionHintTcpip": "Устройството достъпно за evcc през LAN/Wifi.", "connectionValueSerial": "Serial / USB", "connectionValueTcpip": "Мрежа", "device": "Име на устройството", "deviceHint": "Пример: /dev/ttyUSB0", "host": "IP адрес или име на хоста", "hostHint": "Пример: *********", "id": "Modbus ID", "port": "Порт", "protocol": "Протокол Modbus", "protocolHintRtu": "Връзка към мрежов адаптер през RS485 и без превод на протокола.", "protocolHintTcp": "Устройството поддържа LAN/Wifi или е свързано към мрежов адаптер през RS485 и с превод на протокола.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Позволете на няколко клиента да имат достъп до едно Modbus устройство.", "title": "„Модбус прокси“"}, "mqtt": {"authentication": "Автентикация", "description": "Свържете се с MQTT брокер, за да обменяте данни с други системи във вашата мрежа.", "descriptionClientId": "Автор на съобщенията. Ако е празно, се използва `evcc-[rand]`.", "descriptionTopic": "Оставете празно, за да деактивирате публикуването.", "labelBroker": "Брокер", "labelCaCert": "Сървърен сертификат (CA)", "labelCheckInsecure": "Позволете самоподписани сертификати", "labelClientCert": "Клиентски сертификат", "labelClientId": "Клиентски идентификатор", "labelClientKey": "Клиентски ключ", "labelInsecure": "Валидиране на сертификат", "labelPassword": "Парола", "labelTopic": "Тема", "labelUser": "Потребителско име", "publishing": "Публикуване", "title": "MQTT"}, "network": {"descriptionHost": "„Използвайте разширението .local, за да активирате mDNS. Това е от значение за разпознаване на мобилни приложения и някои зарядни станции OCPP.“", "descriptionPort": "Порт за уеб интерфейса и API. Ще трябва да актуализирате URL адреса на браузъра си, ако промените това.", "descriptionSchema": "Влияе само на начина, по който се генерират URL адресите. Изборът на HTTPS няма да активира криптиране.", "labelHost": "Хост име", "labelPort": "Порт", "labelSchema": "Схема", "title": "Мрежа"}, "options": {"boolean": {"no": "не", "yes": "да"}, "endianness": {"big": "голям ендиан", "little": "„малко ендианско“"}, "schema": {"http": "HTTP (некриптиран)", "https": "HTTPS (криптиран)"}, "status": {"A": "A (не е свързан)", "B": "Б (свързан)", "C": "В (зарежда)"}}, "pv": {"titleAdd": "Добавяне на соларен метър", "titleEdit": "Редактиране на соларен метър"}, "section": {"additionalMeter": "Допълнителни електромери", "general": "Об<PERSON>и", "grid": "Мрежова връзка", "integrations": "Интеграции", "loadpoints": "Зарядни точки", "meter": "Солар и батерия", "system": "Система", "vehicles": "Превозни средства"}, "sponsor": {"addToken": "Въведете спонсорски токен", "changeToken": "Промяна на спонсорския токен", "description": "Моделът за спонсорство ни помага да поддържаме проекта и устойчиво да изграждаме нови и вълнуващи функции. Като спонсор получавате достъп до всички реализации на зарядни устройства.", "descriptionToken": "Получавате токена от {url}. Ние също предлагаме пробен токен за тестване.", "error": "Спонсорският токен не е валиден.", "labelToken": "Спонсорски токен", "title": "Спонсорство", "tokenRequired": "Необходимо е да конфигурирате спонсорен тоукън, за да може да създадете това устройство.", "tokenRequiredLearnMore": "Научи повече.", "trialToken": "Временен тоукън"}, "system": {"logs": "Логове", "restart": "Рестартиране", "restartRequiredDescription": "Моля, рестартирайте, за да видите ефекта.", "restartRequiredMessage": "Конфигурацията е променена.", "restartingDescription": "Моля, изчакайте...", "restartingMessage": "Рестартиране на evcc."}, "tariffs": {"description": "Определете вашите енергийни тарифи, за да изчислите разходите за вашите сесии на зареждане.", "title": "Тарифи"}, "title": {"description": "Показва се на главния екран и в раздела на браузъра.", "label": "Заглавие", "title": "Редактиране на заглавие"}, "validation": {"failed": "неуспешен", "label": "Състояние", "running": "проверка...", "success": "успешна проверка", "unknown": "неизвестен", "validate": "провери"}, "vehicle": {"cancel": "Отмени", "chargingSettings": "Настройки на зареждането", "defaultMode": "Режим по подразбиране", "defaultModeHelp": "Режим на зареждане при свързване с автомобил.", "delete": "Изтрий автомобил", "generic": "Други интеграции", "identifiers": "RFID идентификатори", "identifiersHelp": "Лист с RFID думи за идентифициране на автомобила. Една дума на ред. Актуалният идентификатор може да бъде намерен при респективната зарядна станция на общата страница.", "maximumCurrent": "Максимален ток", "maximumCurrentHelp": "Трябва да е повече от минималния ток.", "maximumPhases": "Максимален брой фази", "maximumPhasesHelp": "С колко фази може да зарежда този автомобил? Използва се за да бъде пресметнат минималният необходим фотоволтаичен излишък и плануване.", "minimumCurrent": "Мини<PERSON>а<PERSON>ен ток", "minimumCurrentHelp": "Използвайте стойности по-малко от 6 А само ако знаете какво правите.", "online": "Автомобил с онлайн свързаност", "priority": "Приоритет", "priorityHelp": "Висок приоритет означава, че този автомобил ще има по-предпочитам достъп до фотоволтаичния излишък.", "save": "Запази", "scooter": "Скутер", "template": "Производител", "titleAdd": "Добави Автомобил", "titleEdit": "Редактир<PERSON>й Автомобил", "validateSave": "Провери и Запази"}}, "footer": {"community": {"greenEnergy": "Слънчева енергия", "greenEnergySub1": "заредено с помощта на evcc", "greenEnergySub2": "от октомври 2022", "greenShare": "дял на соларна енергия", "greenShareSub1": "мощност предоставена от", "greenShareSub2": "енергия от слънцето и батерията", "power": "Мощност на зареждане", "powerSub1": "{activeClients} от {totalClients} участници", "powerSub2": "зарежда...", "tabTitle": "Общност"}, "savings": {"co2Saved": "{value} запазени", "co2Title": "CO₂ емисии", "configurePriceCo2": "Научете как да конфигурирате данните за цените и CO₂.", "footerLong": "{percent} енергия от слънцето", "footerShort": "{percent} слънчева енергия", "modalTitle": "Информация за зареждането", "moneySaved": "{value} запазени", "percentGrid": "{grid} кВч от мрежата", "percentSelf": "{self} кВч от фотоволтаици", "percentTitle": "Енергия от фотоволтаици", "period": {"30d": "последните 30 дни", "365d": "последните 365 дни", "thisYear": "тази година", "total": "всички времена"}, "periodLabel": "Период:", "priceTitle": "Цена", "referenceGrid": "Мрежа", "referenceLabel": "Референтни данни:", "tabTitle": "Моите данни"}, "sponsor": {"becomeSponsor": "Станете Спонсор", "becomeSponsorExtended": "Подкрепете ни директно, за да получите стикери.", "confetti": "Готови ли сте за конфети?", "confettiPromise": "Получавате стикери и дигитални конфети", "sticker": "... или evcc стикери?", "supportUs": "Нашата мисия е да направим слънчевата енергия масова. Подкрепете evcc със сума по ваша преценка.", "thanks": "Благодарим Ви, {sponsor}! С ваша помощ продължаваме да развиваме evcc.", "titleNoSponsor": "Подкрепете ни", "titleSponsor": "Вие сте спонсор", "titleTrial": "Режим на проба", "titleVictron": "Спонсорирано от Victron Energy", "trial": "Вие сте в пробен режим и можете да използвате всички функции. Моля, обмислете възможността да подкрепите проекта.", "victron": "Вие използвате evcc на хардуера на Victron Energy и имате достъп до всички функции."}, "telemetry": {"optIn": "Искам да споделям моите данни.", "optInMoreDetails": "Повече информация {0}.", "optInMoreDetailsLink": "тук", "optInSponsorship": "Изисква спонсорство."}, "version": {"availableLong": "налична е нова версия", "modalCancel": "Откажи", "modalDownload": "Свали", "modalInstalledVersion": "Инсталир<PERSON>на версия", "modalNoReleaseNotes": "Няма бележки за версията. Повече информация за новата версия:", "modalTitle": "Налична е нова версия", "modalUpdate": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON>", "modalUpdateNow": "Инсталирай сега", "modalUpdateStarted": "Стартирай новата версия на evcc...", "modalUpdateStatusStart": "Инсталацията започна:"}}, "forecast": {"co2": {"average": "Средно", "lowestHour": "Най-чистият час", "range": "Диа<PERSON>азон"}, "modalTitle": "Прогноза", "price": {"average": "Средно", "lowestHour": "Най-евтиният час", "range": "Диа<PERSON>азон"}, "solar": {"dayAfterTomorrow": "Вдругиден", "partly": "частично", "remaining": "оставащи", "today": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "Утре"}, "solarAdjust": "Настрой фотоволтаичната прогноза базирано на действителни данни{percent}.", "type": {"co2": "CO₂", "price": "Цена", "solar": "Фотоволтаична система"}}, "header": {"about": "Относно", "blog": "Блог", "docs": "Документация", "github": "GitHub", "login": "Автомобил-влизания в системата", "logout": "Изход", "nativeSettings": "Промяна на сървъра", "needHelp": "Нуждаете се от помощ?", "sessions": "Сесии за зареждане"}, "help": {"discussionsButton": "GitHub дискусии", "documentationButton": "Документация", "issueButton": "Докладвайте за грешка", "issueDescription": "Открихте ли странно или неправилно поведение?", "logsButton": "Преглед на дневниците", "logsDescription": "Проверете дневниците за грешки.", "modalTitle": "Нуждаете се от помощ?", "primaryActions": "Нещо не работи както трябва? Това са добри места, където можете да получите помощ.", "restart": {"cancel": "Отказ", "confirm": "Да, рестартирайте!", "description": "При нормални обстоятелства рестартирането не би трябвало да е необходимо. Моля, обмислете подаването на сигнал за грешка, ако трябва редовно да рестартирате evcc.", "disclaimer": "Забележка: evcc ще се прекрати и ще разчита на операционната система за рестартиране на услугата.", "modalTitle": "Сигурни ли сте, че искате да рестартирате?"}, "restartButton": "Рестартиране", "restartDescription": "Опитахте ли да го изключите и включите отново?", "secondaryActions": "Все още не можете да решите проблема си? Ето някои по-сериозни опции."}, "log": {"areaLabel": "Филтриране по област", "areas": "Всички области", "download": "Изтеглете пълния дневник", "levelLabel": "Филтриране по ниво на дневника", "nAreas": "{count} области", "noResults": "Няма съвпадащи записи в дневника.", "search": "Търсене", "selectAll": "Изберете всички", "showAll": "Показване на всички записи", "title": "Дневници", "update": "Автоматично обновяване"}, "loginModal": {"cancel": "Отказ", "error": "Входът не бе успешен: ", "iframeHint": "Отворете evcc в нов раздел.", "iframeIssue": "Вашата парола е правилна, но изглежда, че браузърът ви е загубил бисквитката за удостоверяване. Това може да се случи, ако стартирате evcc в iframe чрез HTTP.", "invalid": "Паролата е невалидна.", "login": "Вход", "password": "Парола", "reset": "Нулиране на паролата?", "title": "Удостоверяване"}, "main": {"chargingPlan": {"active": "Акти<PERSON><PERSON>н", "addRepeatingPlan": "Добавяне на повтарящ се план", "arrivalTab": "Пристигане", "day": "<PERSON><PERSON><PERSON>", "departureTab": "Заминаване", "goal": "Цел на зареждане", "modalTitle": "План за зареждане", "none": "няма", "planNumber": "План {number}", "preconditionDescription": "Зареди за {duration} преди тръгване, за да бъде кондиционирана батерията на автомобила.", "preconditionLong": "Късно зареждане", "preconditionOptionAll": "всичко", "preconditionOptionNo": "не", "preconditionShort": "Късно", "remove": "Премахване", "repeating": "повтарящ се", "repeatingPlans": "Повтарящи се планове", "selectAll": "Изберете всички", "time": "Време", "title": "<PERSON><PERSON><PERSON><PERSON>", "titleMinSoc": "Минимално зареждане", "titleTargetCharge": "Заминаване", "unsavedChanges": "Има незаписани промени. Приложи сега?", "update": "Прило<PERSON>и", "weekdays": "<PERSON><PERSON>и"}, "energyflow": {"battery": "Батерия", "batteryCharge": "Зареждане на батерията", "batteryDischarge": "Разреждане на батерията", "batteryGridChargeActive": "зареждането от мрежата е актижно", "batteryGridChargeLimit": "зареждане от мрежата, когато", "batteryHold": "Батерия (заключена)", "batteryTooltip": "{energy} от {total} ({soc})", "forecastTooltip": "прогноза: оставаща фотоволтаична продукция за днес", "gridImport": "Използвана енергия от мрежата", "homePower": "Потребление", "loadpoints": "Зарядно устройство | Зарядно устройство | {count} зарядни устройства", "noEnergy": "Няма данни от измервателния уред", "pv": "Фотоволтаична система", "pvExport": "Енергия, която се подава в електрическата мрежа", "pvProduction": "Производство", "selfConsumption": "Самопотребление"}, "heatingStatus": {"charging": "Загряване…", "connected": "В режим на изчакване.", "vehicleLimit": "Лимит на нагревателя", "waitForVehicle": "Готово. Чака се нагревателят …"}, "loadpoint": {"avgPrice": "⌀ Цена", "charged": "Заредено", "co2": "⌀ CO₂", "duration": "Продължителност на зареждането", "fallbackName": "Зарядна точка", "finished": "Финално време", "power": "Мощност", "price": "Цена", "remaining": "Оставащо време", "remoteDisabledHard": "{source}: изключено", "remoteDisabledSoft": "{source}: изключено адаптивното соларно зареждане", "solar": "Соларен"}, "loadpointSettings": {"batteryBoost": {"description": "Бързо зареждане от домашната батерия", "label": "Ба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бууст", "mode": "Налично само в соларен и мин+соларен режим", "once": "Буустът е активен за тази сесия на зареждане"}, "batteryUsage": "До<PERSON><PERSON><PERSON>на батерия", "currents": "Заряден ток", "default": "по подразбиране", "disclaimerHint": "Забележка:", "limitSoc": {"description": "Лимит на зареждане, който се използва, когато това превозно средство е свързано", "label": "Лимит по подразбиране"}, "maxCurrent": {"label": "Макс. ток"}, "minCurrent": {"label": "Мин. ток"}, "minSoc": {"description": "Превозното средство се зарежда „бързо“ до {0} в соларен режим. След това продължава със соларен излишък. Полезно за осигуряване на минимален пробег дори в по-тъмни дни.", "label": "Мин. заряд %"}, "onlyForSocBasedCharging": "Тези опции са налични само за превозни средства с известен заряд.", "phasesConfigured": {"label": "Фази", "no1p3pSupport": "Как е свързана вашата зарядна станция?", "phases_0": "Автоматично превключване", "phases_1": "1 фаза", "phases_1_hint": "({min} до {max})", "phases_3": "3 фази", "phases_3_hint": "({min} до {max})"}, "smartCostCheap": "Евтино зареждане от мрежата", "smartCostClean": "Чисто зареждане от мрежата", "title": "Настройки {0}", "vehicle": "Превозно средство"}, "mode": {"minpv": "Мин+Солар", "now": "Бързо", "off": "Изкл.", "pv": "Солар", "smart": "Смарт"}, "provider": {"login": "Вход", "logout": "Изход"}, "startConfiguration": "Нека да започнем с конфигурацията", "targetCharge": {"activate": "Активи<PERSON><PERSON><PERSON>е", "co2Limit": "Лимит на CO₂ от {co2}", "costLimitIgnore": "Конфигурираният {limit} ще бъде игнориран през този период.", "currentPlan": "Активен план", "descriptionEnergy": "До кога трябва да бъде заредена {targetEnergy} в превозното средство?", "descriptionSoc": "Кога трябва да бъде заредено превозното средство до {targetSoc}?", "goalReached": "Целта е вече достигната", "inactiveLabel": "Целево време", "nextPlan": "Следващ план", "notReachableInTime": "Целта ще бъде постигната {overrun} по-късно.", "onlyInPvMode": "Планът за зареждане работи само в соларен режим.", "planDuration": "Време за зареждане", "planPeriodLabel": "Период", "planPeriodValue": "{start} до {end}", "planUnknown": "Все още не е известно", "preview": "Преглед на плана", "priceLimit": "Лимит на цената от {price}", "remove": "Премахване", "setTargetTime": "няма", "targetIsAboveLimit": "Конфигурираният лимит за зареждане от {limit} ще бъде игнориран през този период.", "targetIsAboveVehicleLimit": "Лимитът на превозното средство е под целта за зареждане.", "targetIsInThePast": "Изберете време в бъдещето, Марти.", "targetIsTooFarInTheFuture": "Ще коригираме плана веднага щом научим повече за бъдещето.", "title": "Целево време", "today": "д<PERSON><PERSON><PERSON>", "tomorrow": "утре", "update": "Обновяване", "vehicleCapacityDocs": "Научете как да го конфигурирате.", "vehicleCapacityRequired": "Капацитетът на батерията на превозното средство е необходим за изчисляване на времето за зареждане."}, "targetChargePlan": {"chargeDuration": "Време за зареждане", "co2Label": "Емисия на CO₂ ⌀", "priceLabel": "Цена на енергията", "timeRange": "{day} {range} ч", "unknownPrice": "Все още не е известно"}, "targetEnergy": {"label": "<PERSON>и<PERSON><PERSON><PERSON>", "noLimit": "няма"}, "vehicle": {"addVehicle": "Добавяне на превозно средство", "changeVehicle": "Промяна на превозното средство", "detectionActive": "Откриване на превозно средство…", "fallbackName": "Превозно средство", "moreActions": "Още действия", "none": "Няма превозно средство", "notReachable": "Превозното средство не беше достъпно. Опитайте да рестартирате evcc.", "targetSoc": "<PERSON>и<PERSON><PERSON><PERSON>", "temp": "Температура", "tempLimit": "Целева температура", "unknown": "Гостуващо превозно средство", "vehicleSoc": "Зареждане"}, "vehicleStatus": {"awaitingAuthorization": "Изчакване на разрешение.", "batteryBoost": "Активно ускоряване на батерията.", "charging": "Зареждане…", "cheapEnergyCharging": "Налична е евтина енергия.", "cheapEnergyNextStart": "Евтината енергия в {duration}", "cheapEnergySet": "Лимитът на цената е зададен.", "cleanEnergyCharging": "Налична е чиста енергия.", "cleanEnergyNextStart": "Чиста енергия в {продължителност}.", "cleanEnergySet": "Лимитът на CO₂ е зададен.", "climating": "Открито е предварително кондициониране.", "connected": "Превозното средство е свързано", "disconnectRequired": "Процесът е прекратен. Моля, свържете се отново.", "disconnected": "Разединен.", "finished": "Завър<PERSON><PERSON>н.", "minCharge": "Минимално зареждане до {soc}.", "pvDisable": "Недостатъчен излишък. Скоро ще бъде пауза.", "pvEnable": "Наличен излишък. Скоро ще започне.", "scale1p": "Скоро ще се намали до еднофазно зареждане.", "scale3p": "Скоро ще се увеличи до трифазно зареждане.", "targetChargeActive": "Планът за зареждане е активен. Очаквано завършване след {duration}.", "targetChargePlanned": "Планът за таксуване започва в {duration}", "targetChargeWaitForVehicle": "Планът за зареждане е готов. Очаква се превозното средство…", "vehicleLimit": "Лимит на превозното средство", "vehicleLimitReached": "Достигнат е лимитът на превозното средство.", "waitForVehicle": "Готов. Очакване на превозното средство…", "welcome": "Кратко първоначално зареждане за потвърждаване на връзката."}, "vehicles": "Паркиране", "welcome": "Здравей на борда!"}, "notifications": {"dismissAll": "Отхвърли всички", "logs": "Вижте пълните дневници", "modalTitle": "Известия"}, "offline": {"configurationError": "Грешка при стартиране. Проверете конфигурацията си и рестартирайте.", "message": "Няма връзка със сървъра.", "restart": "Рестартиране", "restartNeeded": "Необходимо за прилагане на промените.", "restarting": "Сървърът ще бъде отново на линия след малко."}, "passwordModal": {"description": "Задайте парола за защита на настройките на конфигурацията. Използването на основния екран е възможно и без влизане в системата.", "empty": "Паролата не трябва да бъде празна.", "error": "Грешка: ", "labelCurrent": "Текуща парола", "labelNew": "Нова парола", "labelRepeat": "Повторете паролата", "newPassword": "Създайте парола", "noMatch": "Паролите не съвпадат.", "titleNew": "Задайте парола на администратора", "titleUpdate": "Обновяване на паролата на администратора", "updatePassword": "Обновяване на паролата"}, "session": {"cancel": "Отказ", "co2": "CO₂", "date": "Период", "delete": "Изтрий", "finished": "Завършено", "meter": "Километраж", "meterstart": "Начало на брояча", "meterstop": "Край на брояча", "odometer": "Пробег", "price": "Цена", "started": "Начално време", "title": "Сесия на зареждане"}, "sessions": {"avgPower": "⌀ Мощност", "avgPrice": "⌀ Цена", "chargeDuration": "Продължителност", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Цена {byGroup}", "byGroupLoadpoint": "По зарядна точка", "byGroupVehicle": "По превозно средство", "energy": "Заредена енергия", "energyGrouped": "Слънчева енергия срещу мрежова енергия", "energyGroupedByGroup": "Енергия {byGroup}", "energySubSolar": "{value} слънчева енергия", "energySubTotal": "{value} общо", "groupedCo2ByGroup": "Количество CO₂ {byGroup}", "groupedPriceByGroup": "Обща цена {byGroup}", "historyCo2": "CO₂-емисии", "historyCo2Sub": "{value} общо", "historyPrice": "Разходи за зареждане", "historyPriceSub": "{value} общо", "solar": "Делът на слънчевата енергия в електроенергията през годината", "solarByGroup": "Слънчев дял {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Енергия (kWh)", "chargeduration": "Продължителност", "co2perkwh": "CO₂/kWh", "created": "Начално време", "finished": "Завършено", "identifier": "Идентификатор", "loadpoint": "Зарядна точка", "meterstart": "Начално показание на електромера (kWh)", "meterstop": "Крайно показание на електромера (kWh)", "odometer": "Пробег (км)", "price": "Цена", "priceperkwh": "Цена/kWh", "solarpercentage": "Слънчева енергия (%)", "vehicle": "Превозно средство"}, "csvPeriod": "Изтегляне на {period} CSV", "csvTotal": "Изтегляне на общ CSV", "date": "Начало", "energy": "Заредено", "filter": {"allLoadpoints": "Всички зарядни точки", "allVehicles": "Всички превозни средства", "filter": "<PERSON>и<PERSON><PERSON><PERSON>р"}, "group": {"co2": "Емисии", "grid": "Мрежа", "price": "Цена", "self": "Слънчева енергия"}, "groupBy": {"loadpoint": "Зарядна точка", "none": "Общо", "vehicle": "Превозно средство"}, "loadpoint": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> пункт", "noData": "Няма сесии на зареждане този месец.", "overview": "Общ преглед", "period": {"month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Общо", "year": "Год<PERSON><PERSON>"}, "price": "Цена", "reallyDelete": "Наистина ли искате да изтриете тази сесия?", "showIndividualEntries": "Показване на отделни сесии", "solar": "Слънчева енергия", "title": "Сесии на зареждане", "total": "Общо", "type": {"co2": "CO₂", "price": "Цена", "solar": "Слънчева енергия"}, "vehicle": "Превозно средство"}, "settings": {"fullscreen": {"enter": "Въведете в режим на цял екран", "exit": "Изход от режим на цял екран", "label": "Цял екран"}, "hiddenFeatures": {"label": "Експериментален", "value": "Показване на експериментални функции на потребителския интерфейс."}, "language": {"auto": "Автоматичен", "label": "Език"}, "sponsorToken": {"expires": "Вашият спонсорски токен изтича след {inXDays}. {getNewToken} и го актуализирайте тук.", "getNew": "Вземете нов", "hint": "Забележка: В бъдеще ще автоматизираме това."}, "telemetry": {"label": "Телеметрия"}, "theme": {"auto": "Система", "dark": "Тъм<PERSON>н", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "Светъл"}, "time": {"12h": "12 часа", "24h": "24 часа", "label": "Формат на часовника"}, "title": "Потребителски интерфейс", "unit": {"km": "km", "label": "Единици", "mi": "<PERSON>или"}}, "smartCost": {"activeHours": "{active} от {total}", "activeHoursLabel": "Активни часове", "applyToAll": "Приложи навсякъде?", "batteryDescription": "Зарежда домашната батерия с енергия от мрежата.", "cheapTitle": "Евтино зареждане от мрежата", "cleanTitle": "Чисто зареждане от мрежата", "co2Label": "Емисия на CO₂", "co2Limit": "Граница на CO₂", "loadpointDescription": "Позволява временно бързо зареждане в соларен режим.", "modalTitle": "Интелигентно зареждане от мрежата", "none": "Няма", "priceLabel": "Цена на енергията", "priceLimit": "Граница на цената", "resetAction": "Премахни ограничението", "resetWarning": "Няма конфигурирана динамична цена или източник на въглероден двуокис. Въпреки това има лимит {limit}. Почистете си конфигурацията!", "saved": "Запазено."}, "startupError": {"configFile": "Използван конфигурационен файл:", "configuration": "Конфигурация", "description": "Моля, проверете конфигурационния си файл. Ако съобщението за грешка не помогне, проверете {0}.", "discussions": "Дискусии в GitHub", "fixAndRestart": "Моля, отстранете проблема и рестартирайте сървъра.", "hint": "Забележка: Възможно е също така да имате дефектно устройство (инвертор, измервателен уред и т.н.). Проверете мрежовите си връзки.", "lineError": "Гр<PERSON><PERSON>к<PERSON> в {0}.", "lineErrorLink": "Ред {0}", "restartButton": "Рестартиране", "title": "Грешка при стартиране"}}