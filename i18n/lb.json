{"batterySettings": {"batteryLevel": "Luedstand vun der Batterie", "bufferStart": {"above": "wann iwwer {soc}.", "full": "wann op {soc}.", "never": "nëmme mat genuch Iwwerschoss."}, "capacity": "{energy} vun {total}", "control": "<PERSON><PERSON><PERSON> s<PERSON>en", "discharge": "D'Entlueden am schnelle Modus a geplangte Lueden verhënneren.", "disclaimerHint": "Notiz:", "disclaimerText": "Dës Astellunge beaflossen nëmmen de Solarmodus. D'Luedeverhalen gëtt deementspriechend ugepasst.", "gridChargeTab": "Vum Netz oplueden", "legendBottomName": "Prioritéit fir d'Oplueden vun der Hausbatterie", "legendBottomSubline": "bis {soc} erreecht sinn.", "legendMiddleName": "Prioritéit fir d'Oplueden vum Gefier", "legendMiddleSubline": "wann d'<PERSON><PERSON><PERSON>terie iwwer {soc} ass.", "legendTopAutostart": "Automatesch starten", "legendTopName": "Batterie-ënnerstëtzt Opluede vum Gefier", "legendTopSubline": "wann d'<PERSON><PERSON><PERSON>terie iwwer {soc} ass.", "modalTitle": "Hausbatterie", "usageTab": "Batterieverbrauch"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON>, dee säi Verbrauch op de verfügbaren Iwwerschoss (z. B. smarten Heizstaf) selbststänneg upasst. evcc er<PERSON>, datt dëst Gerät selbststänneg säi Verbrauch reduzéiert, wann et noutwenneg ass.", "titleAdd": "Intelligente Verbraucher derbäi fügen", "titleEdit": "Intelligenten Verbraucher beaarbechten"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON> der<PERSON><PERSON> setzen", "titleEdit": "<PERSON><PERSON><PERSON> upassen"}, "charge": {"titleAdd": "Energiezieler derbäi fügen", "titleEdit": "Energiezieler beaarbechten"}, "charger": {"chargers": "Wallboxen", "generic": "<PERSON><PERSON><PERSON> Integra<PERSON>", "heatingdevices": "Heizungsgerät", "ocppHelp": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> an d'Konfiguratioun vun der Wallbox.", "ocppLabel": "URL vum OCPP-Server", "switchsockets": "Schaltbar Steckdousen", "template": "<PERSON><PERSON><PERSON><PERSON>", "titleAdd": {"charging": "Wallbox derbäi fügen"}, "titleEdit": {"charging": "Wallbox beaarbechten"}}, "circuits": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, datt d'Zomm vun all den Luedstatiounen déi mam Circuit verbonne sinn net déi konfiguréiert Leeschtung an aktuell Grenzen iwwerschreit. Circuiten kënnen agenist ginn fir eng Hierarchie opzebauen.", "title": "Management vun der Belaaschtung"}, "control": {"description": "Normalerweis sinn d'Standardwäerter gutt. Veränner se nëmmen wann s du weess wat s du mëss.", "descriptionInterval": "Kontrollschläifen-Update-Zyklus a Sekonnen. Definéiert wéi dacks evcc Meter-Daten liest, d'Luedekraaft upasst an d'UI aktualiséiert. Kuerz Intervalle (<30s) kënnen Oszillatiounen an ongewollt Verhale verursaachen.", "descriptionResidualPower": "Verschiibt d'Funktioun vun der Kontrollschläifen. Wann s du eng Hausbatterie hues, ass et recommandéiert ee Wäert vun 100 W ze setzen. Esou kritt d'Batterie eng liicht Prioritéit par Rapport zum Netz.", "labelInterval": "Aktualiséierungsintervall", "labelResidualPower": "Residuell <PERSON>", "title": "Kontrollverhalen"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON>", "broker": "Broker", "bucket": "<PERSON><PERSON><PERSON>", "capacity": "Capacitéit", "chargeStatus": "Status", "chargeStatusA": "net verbonnen", "chargeStatusB": "verbonnen", "chargeStatusC": "luet op", "chargeStatusE": "keng <PERSON>", "chargeStatusF": "Feeler", "chargedEnergy": "Opgelueden", "co2": "Netz CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency": "Wärung", "current": "Stroum", "currentRange": "Stroum", "enabled": "<PERSON><PERSON><PERSON>", "energy": "Energie", "feedinPrice": "Präis fir anzes<PERSON>n", "gridPrice": "Netz Präis", "heaterTempLimit": "Limitt vun der Heizung", "hemsType": "System", "identifier": "RFID-Identifikatioun", "no": "nee", "odometer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "org": "Organisatioun", "phaseCurrents": "Stroum L1, L2, L3", "phasePowers": "Leeschtung L1, L2, L3", "phaseVoltages": "Spannung L1, L2, L3", "phases1p3p": "Phasenëmschaltung", "power": "<PERSON>sch<PERSON>g", "powerRange": "<PERSON>sch<PERSON>g", "range": "Autonomie", "singlePhase": "Eephaseg", "soc": "Luedstand", "solarForecast": "PV-Previsioun", "temp": "Temperatur", "topic": "Sujet", "url": "URL", "vehicleLimitSoc": "Limitt vum Gefier", "yes": "jo"}, "deviceValueChargeStatus": {"A": "A (net verbonnen)", "B": "B (verbonnen)", "C": "C (luet)"}, "devices": {"auxMeter": "<PERSON><PERSON>ucher", "batteryStorage": "<PERSON><PERSON>ie", "solarSystem": "PV-Anlag"}, "editor": {"loading": "YAML-Editeur <PERSON><PERSON><PERSON> …"}, "eebus": {"description": "Konfiguratioun déi et evcc erméiglecht fir mat aneren EEBus-Geräter ze kommunizéieren.", "title": "EEBus"}, "ext": {"description": "Ka fir d'Gestioun vum Lueden oder fir Statistikzwecker verwennt ginn.", "titleAdd": "Zousätzlechen Zieler derbäi fügen", "titleEdit": "Zousätzlechen Zieler beaarbechten"}, "form": {"danger": "Oppassen", "deprecated": "obsolet", "example": "<PERSON><PERSON><PERSON><PERSON>", "optional": "optionell"}, "general": {"cancel": "Ofbriechen", "customHelp": "Erstell ee benotzerdefinéiert Gerät mam evcc-Plugin-System.", "customOption": "Benotzerdefinéiert Gerät", "delete": "Läschen", "docsLink": "D'Dokumentatioun kucken.", "experimental": "Experimentell", "hideAdvancedSettings": "Fortgeschratten Astellunge verstoppen", "off": "aus", "on": "un", "password": "<PERSON><PERSON><PERSON>", "readFromFile": "<PERSON><PERSON> der Datei liesen", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Späicheren", "showAdvancedSettings": "Fortgeschratten Astellungen uweisen", "telemetry": "Telemetrie", "templateLoading": "<PERSON><PERSON><PERSON>…", "title": "Titel", "validateSave": "Iwwerpréiwen & späicheren"}, "grid": {"title": "<PERSON><PERSON>eler", "titleAdd": "<PERSON><PERSON><PERSON>r derbäi fügen", "titleEdit": "<PERSON><PERSON><PERSON><PERSON>"}, "hems": {"description": "evcc mat engem aneren Heem Energiemanagement System verbannen.", "title": "HEMS"}, "icon": {"change": "änner<PERSON>"}, "influx": {"description": "Schreift Lueddaten an aner Moossen an InfluxDB. Benotzt Grafana oder aner Tools fir d'Donnéeën ze visualiséieren.", "descriptionToken": "Kuckt d'InfluxDB Dokumentatioun fir ze léieren wéi een eng erstellt. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "<PERSON><PERSON><PERSON>", "labelCheckInsecure": "Onsécher Verbindungen erlaben", "labelDatabase": "Datebank", "labelInsecure": "Zertifikatiwwerpréiwung", "labelOrg": "Organisatioun", "labelPassword": "<PERSON><PERSON><PERSON>", "labelToken": "API-Token", "labelUrl": "URL", "labelUser": "Numm vum Benotzer", "title": "InfluxDB", "v1Support": "Brauchs du Ënnerstëtzung fir InfluxDB 1.x?", "v2Support": "Zréck op InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Wallbox derbäi fügen"}, "addMeter": "Zousätzlechen Zieler derbäi fügen", "cancel": "Ofbriechen", "chargerError": {"charging": "Wallbox muss konfiguréiert sinn."}, "chargerLabel": {"charging": "Wallbox"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Verwennt Stroum vu 6 bis 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Verwennt Stroum vu 6 bis 32 A.", "chargerPowerCustom": "aneren", "chargerPowerCustomHelp": "<PERSON><PERSON> e<PERSON> definéieren.", "chargerTypeLabel": "Luedleeschtung", "chargingTitle": "<PERSON><PERSON>", "circuitHelp": "Laaschtmanagement-<PERSON><PERSON><PERSON><PERSON><PERSON>, fir sécher ze goen, dass d'Leeschtung a Stroumlimitten net iwwerschratt ginn.", "circuitLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "circuitUnassigned": "net zougewisen", "defaultModeHelp": {"charging": "Luedmo<PERSON> beim Uschléisse vum Gefier."}, "defaultModeHelpKeep": "De leschte Modus deen ausgewielt gouf gëtt bäibehal.", "defaultModeLabel": "Standard-Modus", "delete": "Läschen", "electricalSubtitle": "<PERSON><PERSON> sidd, da léiwer den Elektriker froen.", "electricalTitle": "Elektresch", "energyMeterHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, falls d'Wallbox keen integ<PERSON><PERSON><PERSON> huet.", "energyMeterLabel": "Energiezieler", "estimateLabel": "Luedstand zwëschen API-Updates interpoléieren", "maxCurrentHelp": "Muss méi grouss wéi de minimale Stroum sinn.", "maxCurrentLabel": "Maximale Stroum", "minCurrentHelp": "<PERSON><PERSON><PERSON> 6 A, wann s du weess, wat s du mëss.", "minCurrentLabel": "Minimale Stroum", "noVehicles": "<PERSON><PERSON> Gef<PERSON> konfiguré<PERSON>.", "phases1p": "1-phaseg", "phases3p": "3-phaseg", "phasesAutomatic": "Automatesch Phasen", "phasesAutomaticHelp": "Deng Wallbox unterstëtzt dat automatescht Ëmschalten zwëschen 1- und 3-phasegem Lueden. An der Haaptusiicht kanns du d'Verhale vun de Phase wärend dem Lueden upassen.", "phasesHelp": "Unzuel vun de <PERSON>n, déi un der Wallbox ugeschloss sinn .", "phasesLabel": "Phasen", "pollIntervalDanger": "Reegelméissegt Ofruffe vum Status vum Gefier kann dozou féieren, datt d'Batterie vum Gefier eidel gëtt. Verschidde Constructeure spären d'Lueden an esou Fäll och aktiv. Net recommandéiert! Benotz dës Astellung nëmmen, wann s du dir vun de der Risiken bewosst bass.", "pollIntervalHelp": "Zäit tëscht den API-Updates vum Gefier. Kuerz Intervalle kënnen d'Batterie vum Gefier belaaschten.", "pollIntervalLabel": "Update-Intervall", "pollModeAlways": "<PERSON><PERSON>", "pollModeAlwaysHelp": "Statusupdates ëmmer a regelméissegen Ofstänn duerchféieren.", "pollModeCharging": "lueden", "pollModeChargingHelp": "Status vum Gefier nëmme wärend dem Lueden ofruffen.", "pollModeConnected": "verbonnen", "pollModeConnectedHelp": "Status vum Gefier a regelméissegen Ofstänn ofruffen, wa verbonnen.", "pollModeLabel": "Update-<PERSON><PERSON><PERSON><PERSON>", "priorityHelp": "Luedepunkte mat mei hé<PERSON>jer Prioritéit, ginn éischter mat PV-Iwwerschoss beliwwert.", "priorityLabel": "Prioritéit", "save": "Späicheren", "showAllSettings": "All d'Astellungen uweisen", "solarBehaviorCustomHelp": "Definéier deng eege Schwellen a Verzögerungen fir d'Aus- an Aschalten.", "solarBehaviorDefaultHelp": "Nëmme mat PV-Iwwerschoss lueden. Start no {enableDelay} Iwwerschoss. Stop wa fir {disableDelay} net genuch Iwwerschoss disponibel ass.", "solarBehaviorLabel": "PV-Verhalen", "solarModeCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solarModeMaximum": "nëmme Sonn", "thresholdDisableDelayLabel": "Verzögerung fir d'Ausschalten", "thresholdDisableHelpInvalid": "Wgl. ee positive <PERSON><PERSON><PERSON> verwen<PERSON>.", "thresholdDisableHelpPositive": "<PERSON><PERSON> stoppen, wa méi {power} fir {delay} aus dem <PERSON>z bezu ginn.", "thresholdDisableHelpZero": "<PERSON><PERSON>, wann déi minimal Luedleeschtung fir {delay} net erreescht ka ginn.", "thresholdDisableLabel": "Netzstroum ausschalten", "thresholdEnableDelayLabel": "Verzögerung fir d'Aschalten", "thresholdEnableHelpInvalid": "Wgi. een negative <PERSON><PERSON><PERSON> verwen<PERSON>.", "thresholdEnableHelpNegative": "<PERSON>den starten, wa {surplus} Iwwerschoss fir {delay} verfügbar ass.", "thresholdEnableHelpZero": "<PERSON>en, wann déi minimal Luedleeschtung fir {delay} als Iwwerschoss verfügbar ass.", "thresholdEnableLabel": "Netzstroum aschalten", "titleAdd": "Luedpunkt derbäi fügen", "titleEdit": "Luedpunkt beaarbechten", "titleExample": "Garage, Carport, asw.", "titleLabel": "Titel", "vehicleAutoDetection": "automatesch Erkennung", "vehicleHelpAutoDetection": "Automatesch dat plausibelst Gefier auswielen. Manuell Iwwersteierung méiglech.", "vehicleHelpDefault": "Ëmmer <PERSON><PERSON><PERSON><PERSON>, daR<PERSON> d<PERSON>ef<PERSON> hei luet. Auto-Erkennung deaktivéiert. Manuell Iwwersteierung méiglech.", "vehicleLabel": "Standard-Gefier", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "Zousätzlechen Zieler derbäi fügen", "addGrid": "<PERSON><PERSON><PERSON>r derbäi fügen", "addLoadpoint": "Luedstatioun derbäi setzen", "addPvBattery": "PV oder Batterie derbäi setzen", "addTariffs": "Tariffer derb<PERSON>i fügen", "addVehicle": "<PERSON><PERSON><PERSON> der<PERSON><PERSON><PERSON> setzen", "configured": "konfigu<PERSON><PERSON><PERSON>", "edit": "beaarbechten", "loadpointRequired": "Op mannst een Luedepunkt muss konfiguréiert ginn.", "name": "Numm", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unconfigured": "net konfiguréiert", "vehicles": "<PERSON><PERSON>", "yaml": "Geräter aus der evcc.yaml Datei si net editiérbar."}, "messaging": {"description": "Notifikatiounen iwwer Är Opluedsessioune kréien.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "meter": {"cancel": "Ofbriechen", "delete": "Läschen", "generic": "Allgemeng Integratiounen", "option": {"aux": "Intelligente Verbraucher derbäi fügen", "battery": "Hausbatterie derbäi fügen", "ext": "Externen Zieler derbäi fügen", "pv": "Sonnen-Zieler derbäi fügen"}, "save": "Späicheren", "specific": "Spezifesch Integratiounen", "template": "<PERSON><PERSON><PERSON><PERSON>", "titleChoice": "Wat wëlls du derbäi setzen?", "validateSave": "Iwwerpréiwen & späicheren"}, "modbus": {"baudrate": "Baudrate", "comset": "ComSet", "connection": "Modbus Verbindung", "connectionHintSerial": "Den Apparat ass direkt per RS485 mat EVCC verbonnen.", "connectionHintTcpip": "Den Apparat kann direkt per LAN/Wifi duerch evcc konfiguréiert ginn.", "connectionValueSerial": "Seriell / USB", "connectionValueTcpip": "Netzwierk", "device": "Numm vum Apparat", "deviceHint": "Beispill: /dev/ttyUSB0", "host": "IP-Adress oder Hostname", "hostHint": "Beispill : *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus Protokoll", "protocolHintRtu": "Verbindung durch een RS485 zu Ethernet-Adapter ouni Protokoll-Iwwersetzung.", "protocolHintTcp": "Apparat huet eng nativ LAN/Wifi-Ënnerstëtzung oder ass duerch en RS485 zu Ethernet-Adapter mat <PERSON>ll-Iwwersetzung verbonnen.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Erlaabt méi Clienten Zougank zu engem eenzegen Modbus Apparat ze hunn.", "title": "Modbus-Proxy"}, "mqtt": {"authentication": "Authentifizéierung", "description": "Mat engem MQTT-Broker verbanne fir Daten mat anere Systemer op Ärem Netz auszetauschen.", "descriptionClientId": "Auteur v<PERSON> de <PERSON>n. <PERSON><PERSON> e<PERSON> `evcc-[rand]` ben<PERSON><PERSON><PERSON> g<PERSON>.", "descriptionTopic": "<PERSON><PERSON><PERSON> loosse fir d'Verëffentlechung ze deaktivéieren.", "labelBroker": "Broker", "labelCaCert": "Servercertifikat (CA)", "labelCheckInsecure": "Onsécher Verbindungen erlaben", "labelClientCert": "Certificat vum Client", "labelClientId": "Client ID", "labelClientKey": "Client-Schl<PERSON><PERSON>", "labelInsecure": "Validatioun vum Certificat", "labelPassword": "<PERSON><PERSON><PERSON>", "labelTopic": "<PERSON>a", "labelUser": "Benotzernumm", "publishing": "Verëffentlechen", "title": "MQTT"}, "network": {"descriptionHost": "Benotzt de Suffix .local fir mDNS z'aktivéieren. Ass relevant fir d'Entdeckung vun der mobiler App an e puer OCPP Luedstatiounen.", "descriptionPort": "Port fir de Web Interface an d'API. Du muss d'Browser-URL updaten, wann s du dëst <PERSON>.", "descriptionSchema": "Beaflosst nëmme wéi d'URL'en generéiert ginn. Wann s du HTTPS auswiels, da gëtt d'Verschlësselung net aktivéiert.", "labelHost": "Hostname", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Netzwierk"}, "options": {"boolean": {"no": "nee", "yes": "jo"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (unverschlësselt)", "https": "HTTPS (verschlësselt)"}, "status": {"A": "A (net verbonnen)", "B": "B (verbonnen)", "C": "C (luet)"}}, "pv": {"titleAdd": "Z<PERSON>er (PV) derbäi fügen", "titleEdit": "Zieler (PV) beaarbechten"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "Allgemeng", "grid": "Netzuschloss", "integrations": "Integra<PERSON><PERSON><PERSON>", "loadpoints": "Luedstatiounen", "meter": "PV & Batterie", "system": "System", "vehicles": "<PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "<PERSON><PERSON> aginn", "changeToken": "Token <PERSON>", "description": "De Sponsoring Modell hëlleft eis de Projet z'erhalen an nohalteg nei a spannend Fonctiounen z'entwéckelen. Als Sponsor kriss du Zougank zu all Wallbox-Implementatiounen.", "descriptionToken": "<PERSON> kriss den Token vun {url}. <PERSON> bidden och ee Test-Token un fir ze testen.", "error": "<PERSON> Sponsortoken ass ongülteg.", "labelToken": "Sponsor-Token", "title": "Sponsoring", "tokenRequired": "Du muss ee Sponsortoken konfiguréieren, <PERSON><PERSON> dëst Gerät kann erstallt ginn.", "tokenRequiredLearnMore": "<PERSON><PERSON>i <PERSON>form<PERSON>.", "trialToken": "Testtoken"}, "system": {"logs": "<PERSON><PERSON><PERSON>", "restart": "Neistart", "restartRequiredDescription": "Wgl. neistarten fir d'Ännerungen ze gesinn.", "restartRequiredMessage": "Configurat<PERSON><PERSON> ge<PERSON>t.", "restartingDescription": "Wgl. waarden…", "restartingMessage": "evcc gëtt nei gestart."}, "tariffs": {"description": "Defin<PERSON><PERSON> <PERSON>'Stroumtariffer fir d'Käschte vun den Opluedsessiounen ze berechnen.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Um Haaptbildschierm an am Browser Tab ugewisen.", "label": "Titel", "title": "Tite<PERSON> beaar<PERSON>chten"}, "validation": {"failed": "feelgeschloen", "label": "Status", "running": "<PERSON><PERSON><PERSON><PERSON>…", "success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "onbekannt", "validate": "<PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "Ofbriechen", "chargingSettings": "Astellungen fir ze lueden", "defaultMode": "Standard-Modus", "defaultModeHelp": "Luedmo<PERSON> beim Uschléisse vum Gefier.", "delete": "Läschen", "generic": "<PERSON><PERSON>", "identifiers": "RFID-Kennungen", "identifiersHelp": "Lëscht vun den RFID-Kennungen, fir d'Gefier ze identifizéieren. Eng Entrée pro Zeil. Op der Iwwersiichts-Säit gesäis du déi aktuell RFID-Kennung am jeeweilege Luedpunkt.", "maximumCurrent": "Maximale Stroum", "maximumCurrentHelp": "Muss méi grouss wéi de minimale Stroum sinn.", "maximumPhases": "Maximal Phasen", "maximumPhasesHelp": "Mat wéi ville Phase kann dëst <PERSON> lueden? Gëtt fir d'Berechnung vum minimalen Iwwerschoss a fir d'Planung benotzt.", "minimumCurrent": "Minimale Stroum", "minimumCurrentHelp": "Nëmme<PERSON> 6A goen, wann s du weess, wat s du mëss.", "online": "Autoe mat online API", "primary": "Allgemeng Integratiounen", "priority": "Prioritéit", "priorityHelp": "<PERSON><PERSON><PERSON> h<PERSON> Prioritéit bedeit, datt dëst Gefier Prioritéit zu anere Gefierer huet .", "save": "Späicheren", "scooter": "<PERSON><PERSON><PERSON><PERSON>", "template": "<PERSON><PERSON><PERSON><PERSON>", "titleAdd": "Auto derbäi fügen", "titleEdit": "Auto beaarbechten", "validateSave": "Validéieren & späicheren"}}, "footer": {"community": {"greenEnergy": "Solarenergie", "greenEnergySub1": "opgelueden mat evcc", "greenEnergySub2": "zënter Oktober 2022", "greenShare": "Sonnenundeel", "greenShareSub1": "Stroum gëtt bereetgestallt vun", "greenShareSub2": "Solar- & Batteriespäicher", "power": "Luedeleeschtung", "powerSub1": "{activeClients} vu {totalClients} Participanten", "powerSub2": "g<PERSON>tt elo opgelueden…", "tabTitle": "Live Gemeinschaft"}, "savings": {"co2Saved": "{value} agespuert", "co2Title": "CO₂ Emissiounen", "configurePriceCo2": "<PERSON><PERSON><PERSON><PERSON> wéi een de Präis an d'CO₂-Daten konfiguréiert.", "footerLong": "{percent} Solarenergie", "footerShort": "{percent} Solar", "modalTitle": "Iwwersiicht vum opgeluedene Stroum", "moneySaved": "{value} ges<PERSON>ert", "percentGrid": "{grid} <PERSON>h <PERSON>rou<PERSON>", "percentSelf": "{self} kWh Solar", "percentTitle": "Solarenergie", "period": {"30d": "dé<PERSON> lescht 30 Deeg", "365d": "lescht <PERSON> Deeg", "thisYear": "<PERSON><PERSON><PERSON>", "total": "gesamt"}, "periodLabel": "Zäitraum:", "priceTitle": "Energiepräis", "referenceGrid": "Netz", "referenceLabel": "Referenzdaten:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "Gitt ee Sponsor", "becomeSponsorExtended": "Ënnerstëtzt eis direkt fir Stickeren ze kréien.", "confetti": "Prett fir de Kon<PERSON>tti?", "confettiPromise": "<PERSON> k<PERSON> Stick<PERSON>n an digitale Konfetti", "sticker": "… oder evcc <PERSON>?", "supportUs": "<PERSON><PERSON>: Solarlueden zum Standard maachen. Zesumme mat Ärer finanzieller Ënnerstëtzung kënne mir dëst méiglech maachen.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Däi Bäitrag hëlleft evcc weider ze entwéckelen.", "titleNoSponsor": "Ënnerstëtzt eis", "titleSponsor": "Du bass ee Supporter", "titleTrial": "Testmodus", "titleVictron": "Ënnerstëtzt vu Victron Energy", "trial": "Du bass am Testmodus a kanns all Funktiounen benotzen. <PERSON> géifen eis driwwer freeën, wann s du Sponsor géifs ginn.", "victron": "<PERSON> benotz evcc mat Victron Energy Hardware an hues esou Zougang zu all Funktiounen."}, "telemetry": {"optIn": "<PERSON><PERSON> w<PERSON>l meng <PERSON> och bäid<PERSON>.", "optInMoreDetails": "<PERSON><PERSON><PERSON> sinn verfügbar {0}.", "optInMoreDetailsLink": "hei", "optInSponsorship": "Sponsoring er<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "version": {"availableLong": "nei Vers<PERSON> verfügbar", "modalCancel": "Ofbriechen", "modalDownload": "Download", "modalInstalledVersion": "Aktuell installéiert Versioun", "modalNoReleaseNotes": "Keng Versiounshiweiser verfügbar. Méi Informatioun iwwert déi nei Versioun:", "modalTitle": "Aktualiséierung verfügbar", "modalUpdate": "Installéieren", "modalUpdateNow": "<PERSON><PERSON>", "modalUpdateStarted": "Déi nei Versioun vun evcc starten…", "modalUpdateStatusStart": "Installatioun ugefaang:"}}, "forecast": {"co2": {"average": "<PERSON><PERSON><PERSON>", "lowestHour": "Déi properst Stonn", "range": "<PERSON><PERSON><PERSON><PERSON>"}, "modalTitle": "Prognose", "price": {"average": "Duerchschnitt", "lowestHour": "Bëllegst Stonn", "range": "<PERSON><PERSON><PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partly": "<PERSON><PERSON><PERSON><PERSON>", "remaining": "verbleiwend", "today": "<PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>"}, "solarAdjust": "Solarprognose unhand vun de reale Produktionsdaten upassen{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON><PERSON><PERSON>", "solar": "Solar"}}, "header": {"about": "<PERSON>wwer", "blog": "Blog", "docs": "Dokumentatioun", "github": "GitHub", "login": "<PERSON><PERSON><PERSON> v<PERSON>", "logout": "Ofmellen", "nativeSettings": "<PERSON>", "needHelp": "<PERSON><PERSON><PERSON><PERSON> du Hëllef?", "sessions": "Opluedsessiounen"}, "help": {"discussionsButton": "GitHub Diskussiounen", "documentationButton": "Dokumentatioun", "issueButton": "<PERSON>ug mellen", "issueDescription": "Hues du ee komescht oder falscht Verhalen identifizéiert?", "logsButton": "Logs ukucken", "logsDescription": "Kont<PERSON>é<PERSON> d'Logbicher op Feeler.", "modalTitle": "<PERSON><PERSON><PERSON><PERSON> du Hëllef?", "primaryActions": "Funktionnéiert eppes net esou wéi et soll? Dëst si gutt P<PERSON>zen fir Hëllef ze kréien.", "restart": {"cancel": "Ofbriechen", "confirm": "Jo, nei starten!", "description": "Ënner normalen Ëmstänn dierft ee Neistart net néideg sinn. Mell wgl. <PERSON>, wann s du evcc reegelméisseg nei starte muss.", "disclaimer": "Hiweis: evcc wäert gestoppt ginn a verléisst sech drop datt de Service nei start.", "modalTitle": "Bass du sécher datt s du nei starte wëlls?"}, "restartButton": "<PERSON><PERSON>en", "restartDescription": "Hues du schonns probéiert d'Gerät aus- an erëm unzemaachen?", "secondaryActions": "Nach ëmmer keng Léisung fonnt? Hei sinn e puer weider Méiglechkeeten."}, "log": {"areaLabel": "No Beräich filteren", "areas": "All Beräicher", "download": "Komplette Log eroflueden", "levelLabel": "No Log-Level filteren", "nAreas": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON>", "noResults": "<PERSON>g passend En<PERSON><PERSON> fonnt.", "search": "<PERSON><PERSON>", "selectAll": "alles auswielen", "showAll": "All Entréeë uweisen", "title": "<PERSON><PERSON><PERSON>", "update": "Automatesch Aktualiséieren"}, "loginModal": {"cancel": "Ofbriechen", "demoMode": "Login gëtt am Demomodus net ënnerstëtzt.", "error": "<PERSON>gin feelgeschloen: ", "iframeHint": "Evcc an engem neien Ta<PERSON> opmaachen.", "iframeIssue": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mee <PERSON> sch<PERSON>gt den Authentifikatiouns-<PERSON><PERSON> ofgel<PERSON>t ze hunn. Dëst ka geschéien wann s du evcc an engem iframe iwwer HTTP verwenns.", "invalid": "<PERSON><PERSON><PERSON> ass ongültig.", "login": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON> z<PERSON>?", "title": "Authentifizéierung"}, "main": {"chargingPlan": {"active": "Aktiv", "addRepeatingPlan": "Widderhuelte Planifikatioun derbäi fügen", "arrivalTab": "Arrivée", "day": "<PERSON><PERSON>", "departureTab": "<PERSON><PERSON><PERSON>", "goal": "Luedzil", "modalTitle": "Luedplanifi<PERSON><PERSON>un", "none": "keng", "planNumber": "Planifkatioun {number}", "preconditionDescription": "Luetzäit {Zeit} virum Fortfuere fir Batterie-Prekonditionéierung.", "preconditionLong": "Verspéit Lueden", "preconditionOptionAll": "Alles", "preconditionOptionNo": "<PERSON><PERSON>", "preconditionShort": "Spéit", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repeating": "wid<PERSON><PERSON><PERSON>nd", "repeatingPlans": "<PERSON>id<PERSON><PERSON><PERSON><PERSON>", "selectAll": "All auswielen", "time": "Zäit", "title": "Planifikatioun", "titleMinSoc": "<PERSON><PERSON>", "titleTargetCharge": "<PERSON><PERSON><PERSON>", "unsavedChanges": "Net gespäichert Ännerungen leie vir. Elo uwennen?", "update": "<PERSON><PERSON><PERSON>", "weekdays": "<PERSON><PERSON>"}, "energyflow": {"battery": "<PERSON><PERSON>ie", "batteryCharge": "<PERSON><PERSON><PERSON> op<PERSON>", "batteryDischarge": "<PERSON><PERSON><PERSON> en<PERSON>", "batteryGridChargeActive": "Netzlueden aktiv", "batteryGridChargeLimit": "<PERSON><PERSON><PERSON><PERSON> wann", "batteryHold": "Batterie (gespäert)", "batteryTooltip": "{energy} vun {total} ({soc})", "forecastTooltip": "Prognose: <PERSON><PERSON><PERSON>roduk<PERSON>n fir haut", "gridImport": "Verbrauch vum Stroumnetz", "homePower": "<PERSON><PERSON><PERSON><PERSON>", "loadpoints": "Wallbox | Wallbox | {count} Wallboxen", "noEnergy": "<PERSON><PERSON>", "pv": "Solaranlag", "pvExport": "Export an d'Stroumnetz", "pvProduction": "Produktioun", "selfConsumption": "<PERSON><PERSON><PERSON> Verbrauch"}, "heatingStatus": {"charging": "Wier<PERSON>…", "connected": "Standby.", "vehicleLimit": "Limitt vun der Heizung", "waitForVehicle": "Prett. Waarden op d'Heizung…"}, "loadpoint": {"avgPrice": "⌀ Präis", "charged": "Opgelueden", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON>", "fallbackName": "Luedstatioun", "finished": "Fäerdeg um", "power": "<PERSON>sch<PERSON>g", "price": "<PERSON><PERSON><PERSON><PERSON>", "remaining": "Reschtzäit", "remoteDisabledHard": "{source}: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remoteDisabledSoft": "{source}: Adaptatiivt PV-Lueden deaktivéiert", "solar": "<PERSON><PERSON>"}, "loadpointSettings": {"batteryBoost": {"description": "<PERSON><PERSON><PERSON> vun der Hausbatterie lueden.", "label": "<PERSON><PERSON><PERSON>", "mode": "Nëmmen am PV- a Min+PV-Modus verfügbar.", "once": "Bo<PERSON> ass dir dës Luedsessioun aktivéiert."}, "batteryUsage": "Hausbatterie", "currents": "Luedstroum", "default": "Standard", "disclaimerHint": "Notiz:", "limitSoc": {"description": "<PERSON><PERSON><PERSON><PERSON> dat benotzt g<PERSON>, wann dëst Gef<PERSON> verbonnen ass.", "label": "Standard Luedzil"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "<PERSON>'<PERSON><PERSON><PERSON> g<PERSON><PERSON> \"séier\" op {0} am Solarmodus opgelueden. Da geet et weider mat Solariwwerschëss. Dëst ass nëtzlech fir eng Minimum-Reechwäit och wärend méi däischter Deeg ze garantéieren.", "label": "<PERSON>. op<PERSON> %"}, "onlyForSocBasedCharging": "<PERSON><PERSON><PERSON> Op<PERSON> sinn nëmme fir Gefierer mat bekannte Luedstatioune verfügbar.", "phasesConfigured": {"label": "Phasen", "no1p3pSupport": "Wéi ass deng Luedstatioun ugeschloss?", "phases_0": "automatesch wiesselen", "phases_1": "1 Phase", "phases_1_hint": "({min} bis {max})", "phases_3": "3 Phasen", "phases_3_hint": "({min} bis {max})"}, "smartCostCheap": "Bëlleg vum Reseau oplueden", "smartCostClean": "<PERSON><PERSON><PERSON> vun <PERSON>", "title": "Astellungen {0}", "vehicle": "<PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+PV", "now": "<PERSON><PERSON><PERSON>", "off": "Aus", "pv": "PV", "smart": "<PERSON><PERSON><PERSON>"}, "provider": {"login": "<PERSON><PERSON><PERSON>", "logout": "ausloggen"}, "startConfiguration": "Konfiguratioun ufänken", "targetCharge": {"activate": "Aktivéieren", "co2Limit": "CO₂-Grenz vu {co2}", "costLimitIgnore": "Den agestellte {limit} gëtt an dësem Zäitraum ignoréiert.", "currentPlan": "Aktiv Planifikatioun", "descriptionEnergy": "Bis wéini soll {targetEnergy} an d'Gefier geluede ginn?", "descriptionSoc": "W<PERSON><PERSON> soll d'Gefier op {targetSoc} opgeluede ginn?", "goalReached": "<PERSON><PERSON><PERSON><PERSON> schonns erreecht", "inactiveLabel": "Zilzäit", "nextPlan": "Nächst Planifikatioun", "notReachableInTime": "Zilzäit gëtt {overrun} spéider erreecht.", "onlyInPvMode": "Luedplanifikatioun ass nëmmen am PV-Modus aktiv.", "planDuration": "Dauer vum Oplueden", "planPeriodLabel": "Zäitraum", "planPeriodValue": "{Start} bis {end}", "planUnknown": "nach net bekannt", "preview": "<PERSON><PERSON><PERSON><PERSON> vun der Planifikatioun", "priceLimit": "Präisgrenz vu {price}", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setTargetTime": "keng", "targetIsAboveLimit": "Dat konfiguréiert Luedzil vu {limit} gëtt wärend dëser Period ignoréiert.", "targetIsAboveVehicleLimit": "D'Limitt vum Gefier ass méi kleng wéi d'Luedzil.", "targetIsInThePast": "Déi gewielt Zäit ass an der Vergaangenheet.", "targetIsTooFarInTheFuture": "Mir wäerten d'Planifikatioun upassen soubal mir méi iwwer d'Zukunft wëssen.", "title": "Zilzäit", "today": "haut", "tomorrow": "muer", "update": "Aktualiséieren", "vehicleCapacityDocs": "Léier wéi du et konfiguréiers.", "vehicleCapacityRequired": "D'Kapazitéit vun der Batterie vum Gefier ass néideg fir d'Dauer vum Oplueden anzeschätzen."}, "targetChargePlan": {"chargeDuration": "Dauer vum Oplueden", "co2Label": "CO₂ Emissioun ⌀", "priceLabel": "Energiepräis", "timeRange": "{day} {range} h", "unknownPrice": "nach onbekannt"}, "targetEnergy": {"label": "Luedzil", "noLimit": "keent"}, "vehicle": {"addVehicle": "Gefier derbäi fügen", "changeVehicle": "<PERSON><PERSON><PERSON>", "detectionActive": "Erkennung vum Gefier…", "fallbackName": "<PERSON><PERSON><PERSON>", "moreActions": "Weider Aktiounen", "none": "<PERSON><PERSON>", "notReachable": "D'Gefier war net erreechbar. Probéiert evcc nei ze starten.", "targetSoc": "<PERSON><PERSON><PERSON><PERSON>", "temp": "Temperatur.", "tempLimit": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> Gefier", "vehicleSoc": "<PERSON><PERSON><PERSON><PERSON>"}, "vehicleStatus": {"awaitingAuthorization": "Waarden op d'Autorisatioun.", "batteryBoost": "Batterie Boost ass aktiv.", "charging": "<PERSON><PERSON>…", "cheapEnergyCharging": "Gënschteg Energie verfügbar.", "cheapEnergyNextStart": "Gënschteg Energie an {duration}.", "cheapEnergySet": "Präislimitt gesat.", "cleanEnergyCharging": "Gréng Energie verfügbar.", "cleanEnergyNextStart": "<PERSON><PERSON>ng Energie an {duration}.", "cleanEnergySet": "CO₂-Limitt gesetzt.", "climating": "Virklimatiséierung erkannt.", "connected": "Verbonne.", "disconnectRequired": "Virgang ofgebrach. Nach eng Kéier verbannen.", "disconnected": "Deconnectéiert.", "finished": "Ofgeschloss.", "minCharge": "Minimum oplueden bis {soc}.", "pvDisable": "Net genuch Iwwerschoss. Et gëtt geschwënn pauséiert.", "pvEnable": "Iwwerschoss verfügbar. Starte geschwënn.", "scale1p": "Reduktioun op eng eenzeg Phase.", "scale3p": "<PERSON>r<PERSON><PERSON><PERSON><PERSON> geschwënn op dräi Phasen.", "targetChargeActive": "Luedplanung aktiv. Ageschate Schluss an {duration}.", "targetChargePlanned": "Luedpladung fänkt un an {time} un.", "targetChargeWaitForVehicle": "Luedplang ass prett. Waarden op d'Gefier…", "vehicleLimit": "Limitt vum Gefier", "vehicleLimitReached": "Limitt vum Gefier erreecht.", "waitForVehicle": "Prett. Waarden op d'Gefier…", "welcome": "<PERSON><PERSON>z Lueden fir d'Verbindung ze confirméieren."}, "vehicles": "Parking", "welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON> wëllkomm!"}, "notifications": {"dismissAll": "Notifikatiounen e<PERSON>", "logs": "Vollständege Log ukucken", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "offline": {"configurationError": "Feeler beim <PERSON>. Iwwerpréif deng <PERSON>gurat<PERSON> a start nei.", "message": "<PERSON><PERSON>dung mam Server.", "restart": "Neistart", "restartNeeded": "Noutwendeg fir Ännerungen z'applizéieren.", "restarting": "Server ass geschwënn erëm verfügbar."}, "passwordModal": {"description": "Definéiert e Passwuert fir d'Konfiguratiounsastellungen ze schützen. Et kann een den Haaptbildschierm och ouni Login nach ëmmer ben<PERSON>zen.", "empty": "Passwuert duerf net eidel sinn", "error": "Feeler: ", "labelCurrent": "Aktuellt Passwuert", "labelNew": "<PERSON><PERSON>", "labelRepeat": "<PERSON><PERSON> w<PERSON>", "newPassword": "<PERSON><PERSON><PERSON>", "noMatch": "Passwierder stëmmen net iwwerteneen", "titleNew": "Administrator <PERSON><PERSON><PERSON>", "titleUpdate": "Administrator <PERSON><PERSON><PERSON>", "updatePassword": "<PERSON><PERSON><PERSON>"}, "session": {"cancel": "Ofbriechen", "co2": "CO₂", "date": "Zäitraum", "delete": "Läschen", "finished": "<PERSON><PERSON>", "meter": "Zielerstand", "meterstart": "<PERSON><PERSON><PERSON> Meter Liesung", "meterstop": "Last Meter <PERSON>", "odometer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON><PERSON>", "started": "Startzeit", "title": "Charging <PERSON>"}, "sessions": {"avgPower": "⌀ <PERSON><PERSON><PERSON>g", "avgPrice": "⌀ Präis", "chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Präis {byGroup}", "byGroupLoadpoint": "no Opluedpunkt", "byGroupVehicle": "no Gefier", "energy": "<PERSON><PERSON><PERSON><PERSON>", "energyGrouped": "<PERSON>ne- vs. <PERSON><PERSON><PERSON><PERSON>", "energyGroupedByGroup": "Stroum {byGroup}", "energySubSolar": "{value} Sonn", "energySubTotal": "{value} insgesamt", "groupedCo2ByGroup": "Quantitéit CO₂ {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON><PERSON><PERSON> {byGroup}", "historyCo2": "CO₂-Emissiounen", "historyCo2Sub": "{value} insgesamt", "historyPrice": "Opluedkäsch<PERSON>", "historyPriceSub": "{value} insgesamt", "solar": "Sonnenundeel iwwer d'Joer", "solarByGroup": "Sonnenundeel {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energie (kWh)", "chargeduration": "<PERSON><PERSON><PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "E<PERSON>allt", "finished": "Fäerdeg", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Luedstatioun", "meterstart": "Zielerstand am Ufank (kWh)", "meterstop": "Zielerstand zum Schluss (kWh)", "odometer": "Kilometerstand (km)", "price": "<PERSON><PERSON><PERSON><PERSON>", "priceperkwh": "Präis/kWh", "solarpercentage": "<PERSON><PERSON> (%)", "vehicle": "<PERSON><PERSON><PERSON>"}, "csvPeriod": "{period} CSV eroflueden", "csvTotal": "Gesamt CSV eroflueden", "date": "Ufank", "energy": "Opgelueden", "filter": {"allLoadpoints": "All Luedstatiounen", "allVehicles": "All Gefierer", "filter": "Filteren"}, "group": {"co2": "Emissiounen", "grid": "Netz", "price": "<PERSON><PERSON><PERSON><PERSON>", "self": "<PERSON><PERSON>"}, "groupBy": {"loadpoint": "<PERSON><PERSON>punk<PERSON>", "none": "Insgesamt", "vehicle": "<PERSON><PERSON><PERSON>"}, "loadpoint": "Luedstatioun", "noData": "Nach keng Opluedsessiounen fir dëse Mount.", "overview": "Iwwersiicht", "period": {"month": "Mount", "total": "Gesamt", "year": "<PERSON><PERSON>"}, "price": "<PERSON><PERSON><PERSON><PERSON>", "reallyDelete": "Wël<PERSON> du dës Se<PERSON>n wierklech läschen?", "showIndividualEntries": "<PERSON><PERSON><PERSON>", "solar": "<PERSON><PERSON>", "title": "Opluedsessiounen", "total": "Total", "type": {"co2": "CO₂", "price": "<PERSON><PERSON><PERSON><PERSON>", "solar": "<PERSON><PERSON>"}, "vehicle": "<PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Vollbild starten", "exit": "Aus dem Vollbild erausgoen", "label": "Vollbild"}, "hiddenFeatures": {"label": "Experimentell", "value": "Experimentell UI-Funk<PERSON><PERSON><PERSON> weisen."}, "language": {"auto": "Automatesch", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "sponsorToken": {"expires": "Däi Sponsor Token leeft aus an {inXDays}. {getNewToken} an update et hei.", "getNew": "<PERSON><PERSON> dir een neit", "hint": "N.b.: <PERSON> wäerten dëst an Zukunft automatiséieren."}, "telemetry": {"label": "Telemetrie"}, "theme": {"auto": "System", "dark": "<PERSON><PERSON><PERSON><PERSON>", "label": "Design", "light": "hell"}, "time": {"12h": "12 Stonnen", "24h": "24 Stonnen", "label": "Zeitformat"}, "title": "Duerstellung", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON><PERSON>", "mi": "<PERSON><PERSON>"}}, "smartCost": {"activeHours": "{active} vun {total}", "activeHoursLabel": "Aktiv Stonnen", "applyToAll": "Iwwerall uwennen?", "batteryDescription": "<PERSON><PERSON> d'Hausbatterie aus dem Netz.", "cheapTitle": "Gënschtegt Oplueden vum Netz", "cleanTitle": "Gréngt Opluede vum Netz", "co2Label": "CO₂-Emissioun", "co2Limit": "CO₂-Grenz", "loadpointDescription": "Aktivéiert iwwerganksméisseg Schnelloplueden am PV-Modus.", "modalTitle": "Smart Opluede vum Netz", "none": "keng", "priceLabel": "Energiepräis", "priceLimit": "Präisgrenz", "resetAction": "<PERSON><PERSON>", "resetWarning": "Et ass keen dynamischen Netzpräis a keng CO₂-Quelle konfiguréiert. Trotzdem ass eng Limitt vu {limit} ageriicht. Konfiguratioun opraumen?", "saved": "Gepäichert."}, "startupError": {"configFile": "Konfiguratiounsdatei benotzt:", "configuration": "Con<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Iwwerpréift dKonfiguratiounsdatei. Wann d'Fehlermeldung net hëlleft fir eng Léisung ze fannen, kuckt an eisem {0}.", "discussions": "GitHub Diskussiounen", "fixAndRestart": "Wgl. de <PERSON> behiewen an de Server nei starten.", "hint": "Notiz: <PERSON>t kéint och sinn datt s du ee defekten Apparat hues (In<PERSON><PERSON>, Meter, ...). Kontrolléier deng Netzwierkverbindungen.", "lineError": "An {0} gouf ee Feeler fonnt.", "lineErrorLink": "Linn {0}", "restartButton": "Neistart", "title": "Startup Feeler"}}