{"batterySettings": {"batteryLevel": "Рівень заряду батареї", "bufferStart": {"above": "коли вище {soc}.", "full": "коли при {soc}.", "never": "тільки з достатнім надлишком."}, "capacity": "{energy} із {total}", "control": "Контроль батареї", "discharge": "Запобігти розрядці в швидкому режимі та плановій зарядці.", "disclaimerHint": "Примітка:", "disclaimerText": "Ці налаштування впливають лише на сонячний режим. Поведінка зарядки регулюється відповідно.", "gridChargeTab": "Зарядка від електромережі", "legendBottomName": "Надайте пріоритет зарядці батареї домашнього накопичувача", "legendBottomSubline": "поки не досягне {soc}.", "legendMiddleName": "Пріоритет заряджання автомобіля", "legendMiddleSubline": "коли домашня батарея вище {soc}.", "legendTopAutostart": "Запустити автоматично", "legendTopName": "Заряджання автомобіля за допомогою акумулятора", "legendTopSubline": "коли домашня батарея вище {soc}.", "modalTitle": "Домашня батарея", "usageTab": "Використання батареї"}, "config": {"aux": {"description": "Пристрій, який регулює споживання на основі наявного надлишку (наприклад, розумні водонагрівачі). evcc очікує, що цей пристрій за потреби зменшить споживання енергії.", "titleAdd": "Додайте саморегулюючого споживача", "titleEdit": "Редагувати саморегулюючого споживача"}, "battery": {"titleAdd": "Додати батарею", "titleEdit": "Редагувати батарею"}, "charge": {"titleAdd": "Додати лічильник заряду", "titleEdit": "Редагувати лічильник заряду"}, "charger": {"chargers": "Зарядні пристрої для електромобілів", "generic": "Загальні інтеграції", "heatingdevices": "Нагрівальні прилади", "ocppHelp": "Скопіюйте цю адресу в конфігурацію зарядних пристроїв.", "ocppLabel": "URL-адреса OCPP-сервера", "switchsockets": "Перемикаються розетки", "template": "Виробник", "titleAdd": {"charging": "Додати зарядний пристрій", "heating": "Дода<PERSON>и Об<PERSON>г<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "Редагувати зарядний пристрій", "heating": "Редагувати обігрівач"}, "type": {"custom": {"charging": "Користувацький зарядний пристрій", "heating": "Користувацький обігрівач"}, "heatpump": "Користувацький тепловий насос", "sgready": "Користувацький тепловий насос (готовий до роботи з підтримкою теплового насоса, усі)", "sgready-boost": "Тепловий насос, що визначається користувачем (підтримка теплового насоса, підвищення потужності)", "switchsocket": "Користувацький перемикач-розетка"}}, "circuits": {"description": "Гарантує, що сума всіх точок навантаження, підключених до ланцюга, не перевищує налаштованих обмежень потужності та струму. Схеми можуть бути вкладеними для побудови ієрархії.", "title": "Керування навантаженням"}, "control": {"description": "Зазвичай стандартні значення підходять. Змінюйте їх, лише якщо знаєте, що робите.", "descriptionInterval": "Цикл оновлення контуру керування в секундах. Визначає, як часто evcc зчитує дані лічильника, регулює потужність заряджання та оновлює інтерфейс користувача. Короткі інтервали (< 30 с) можуть спричинити коливання та небажану поведінку.", "descriptionResidualPower": "Зміщує робочу точку контуру керування. Якщо у вас домашня батарея, рекомендується встановити значення 100 Вт. Таким чином батарея матиме невеликий пріоритет над використанням мережі.", "labelInterval": "Інтервал оновлення", "labelResidualPower": "Залишкова потужність", "title": "Контрольна поведінка"}, "deviceValue": {"amount": "Сума", "broker": "Брокер", "bucket": "Відро", "capacity": "Ємність", "chargeStatus": "Статус", "chargeStatusA": "не підключено", "chargeStatusB": "підключений", "chargeStatusC": "зарядка", "chargeStatusE": "немає електроенергії", "chargeStatusF": "помилка", "chargedEnergy": "Заряджений", "co2": "Мережа CO₂", "configured": "Налаштовано", "controllable": "Контрольована", "currency": "Валюта", "current": "Cтрум", "currentRange": "Струм", "enabled": "Ввімкнено", "energy": "Енергія", "feedinPrice": "Зб<PERSON>рна ціна", "gridPrice": "Ціна сітки", "heaterTempLimit": "Обмеження нагрівача", "hemsType": "Система", "identifier": "RFID-ідентифікатор", "no": "ні", "odometer": "Одометр", "org": "Організація", "phaseCurrents": "Cтрум L1, L2, L3", "phasePowers": "Потужність L1, L2, L3", "phaseVoltages": "Напруга L1, L2, L3", "phases1p3p": "Перемикач фаз", "power": "Потужність", "powerRange": "Потужність", "range": "Дальність", "singlePhase": "Однофазний", "soc": "Зарядити", "solarForecast": "Сонячний прогноз", "temp": "Температура", "topic": "Тема", "url": "URL", "vehicleLimitSoc": "Ліміт транспортного засобу", "yes": "так"}, "deviceValueChargeStatus": {"A": "A (не підключено)", "B": "B (підключено)", "C": "C (зарядка)"}, "devices": {"auxMeter": "Розумний споживач", "batteryStorage": "Акумуляторне зберігання", "solarSystem": "Сонячна система"}, "editor": {"loading": "Завантаження редактора YAML…"}, "eebus": {"description": "Конфігурація, яка дозволяє evcc спілкуватися з іншими пристроями EEBus.", "title": "EEBus'"}, "ext": {"description": "Може використовуватися для керування навантаженням або статистики.", "titleAdd": "Додати зовнішній лічильник", "titleEdit": "Редагувати зовнішній лічильник"}, "form": {"danger": "Небезпека", "deprecated": "зас<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "example": "Наприклад", "optional": "необов'язково"}, "general": {"cancel": "Скасувати", "customHelp": "Створіть користувацький пристрій за допомогою системи плагінів evcc.", "customOption": "Пристрій, визначений користувачем", "delete": "Видалити", "docsLink": "Переглянути документацію.", "experimental": "Експериментальний", "hideAdvancedSettings": "Приховати розширені налаштування", "invalidFileSelected": "Вибрано недійсний файл", "noFileSelected": "Не вибрано файл.", "off": "вимкнено", "on": "на", "password": "Пароль", "readFromFile": "Читати з файлу", "remove": "Вилучити", "save": "зберегти", "selectFile": "Переглянути", "showAdvancedSettings": "Показати розширені налаштування", "telemetry": "Телеметрія", "templateLoading": "Завантаження...", "title": "Назва", "validateSave": "Перевірити та зберегти"}, "grid": {"title": "Мережевий лічильник", "titleAdd": "Додати лічильник електромережі", "titleEdit": "Редагувати лічильник електромережі"}, "hems": {"description": "Підключіть evcc до іншої домашньої системи керування енергією.", "title": "HEMS"}, "icon": {"change": "зміна"}, "influx": {"description": "Записує дані про нарахування та інші показники в InfluxDB. Використовуйте Grafana або інші інструменти для візуалізації даних.", "descriptionToken": "Перегляньте документацію InfluxDB, щоб дізнатися, як її створити. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Відро", "labelCheckInsecure": "Дозволити самопідписані сертифікати", "labelDatabase": "База даних", "labelInsecure": "Перевірка сертифіката", "labelOrg": "Організація", "labelPassword": "Пароль", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Ім'я користувача", "title": "InfluxDB'", "v1Support": "Потрібна підтримка для InfluxDB 1.x?", "v2Support": "Назад до InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Додати зарядний пристрій", "heating": "Додати обігрів<PERSON>ч"}, "addMeter": "Додати спеціальний лічильник енергії", "cancel": "Скасувати", "chargerError": {"charging": "Необхідно налаштувати зарядний пристрій.", "heating": "Потрібно налаштувати обігрівач."}, "chargerLabel": {"charging": "Зарядний пристрій", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Використовуватиметься діапазон струму від 6 до 16 А.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Буде використовувати діапазон струму від 6 до 32 А.", "chargerPowerCustom": "інші", "chargerPowerCustomHelp": "Визначте настроюваний діапазон струму.", "chargerTypeLabel": "Тип зарядного пристрою", "chargingTitle": "Поведінка", "circuitHelp": "Призначення керування навантаженням для забезпечення неперевищення обмежень потужності та струму.", "circuitLabel": "Схема", "circuitUnassigned": "непризначений", "defaultModeHelp": {"charging": "Режим зарядки при підключенні автомобіля.", "heating": "Встановлюється під час запуску системи."}, "defaultModeHelpKeep": "Зберігає останній вибраний режим.", "defaultModeLabel": "Режим за замовчуванням", "delete": "Видалити", "electricalSubtitle": "Якщо ви сумніваєтеся, запитайте свого електрика.", "electricalTitle": "Електричний", "energyMeterHelp": "Додатковий лічильник, якщо зарядний пристрій не має вбудованого.", "energyMeterLabel": "Енерголічильник", "estimateLabel": "Інтерполювати рівень оплати між оновленнями API", "maxCurrentHelp": "Повинен бути більшим за мінімальний струм.", "maxCurrentLabel": "Максимал<PERSON>ний струм", "minCurrentHelp": "Опускайтеся нижче 6 А, лише якщо знаєте, що робите.", "minCurrentLabel": "Міні<PERSON><PERSON><PERSON><PERSON><PERSON>ий струм", "noVehicles": "Транспортні засоби не налаштовані.", "option": {"charging": "Додати точку заряду", "heating": "Додати нагрівальний пристроїв"}, "phases1p": "1-фаза", "phases3p": "3-<PERSON><PERSON><PERSON>н<PERSON>", "phasesAutomatic": "Автоматичні фази", "phasesAutomaticHelp": "Ваш зарядний пристрій підтримує автоматичне перемикання між 1- і 3-фазним заряджанням. На головному екрані ви можете налаштувати поведінку фаз під час заряджання.", "phasesHelp": "Кількість підключених фаз.", "phasesLabel": "Фази", "pollIntervalDanger": "Регулярне опитування автомобіля може розрядити акумулятор автомобіля. Деякі виробники транспортних засобів можуть активно забороняти зарядку в цьому випадку. Не рекомендується! Використовуйте це, лише якщо ви усвідомлюєте ризики.", "pollIntervalHelp": "Час між оновленнями API автомобіля. Короткі проміжки часу можуть розрядити акумулятор автомобіля.", "pollIntervalLabel": "Інтервал оновлення", "pollModeAlways": "завжди", "pollModeAlwaysHelp": "Завжди запитуйте оновлення статусу через регулярні проміжки часу.", "pollModeCharging": "зарядка", "pollModeChargingHelp": "Вимагати оновлення статусу автомобіля лише під час заряджання.", "pollModeConnected": "підключений", "pollModeConnectedHelp": "Регулярно оновлюйте статус автомобіля при підключенні.", "pollModeLabel": "Оновити поведінку", "priorityHelp": "Вищий пріоритет – отримання переважного доступу до надлишку сонячної енергії.", "priorityLabel": "Пріоритет", "save": "Зберегти", "showAllSettings": "Показати всі налаштування", "solarBehaviorCustomHelp": "Визначте власні порогові значення ввімкнення та вимкнення та затримки.", "solarBehaviorDefaultHelp": "Почати після {enableDelay} достатнього надлишку. Зупинити, коли надлишку недостатньо для {disableDelay}.", "solarBehaviorLabel": "Солар", "solarModeCustom": "звичай", "solarModeMaximum": "максимальна сонячна", "thresholdDisableDelayLabel": "Вимкнути затримку", "thresholdDisableHelpInvalid": "Використовуйте додатне значення.", "thresholdDisableHelpPositive": "Зупинка, коли з мережі використовується більше {power} протягом {delay}.", "thresholdDisableHelpZero": "Зупин<PERSON><PERSON><PERSON><PERSON><PERSON>, коли мінімальна необхідна потужність не може бути задоволена протягом {delay}.", "thresholdDisableLabel": "Вимкніть електромережу", "thresholdEnableDelayLabel": "Увімкнути затримку", "thresholdEnableHelpInvalid": "Використовуйте від’ємне значення.", "thresholdEnableHelpNegative": "Початок, коли надлишок {surplus} буде доступний протягом {delay}.", "thresholdEnableHelpZero": "Почи<PERSON><PERSON><PERSON>, коли може бути задоволена мінімальна необхідна потужність для {delay}.", "thresholdEnableLabel": "Увімкнути живлення мережі", "titleAdd": {"charging": "Додати точку зарядки", "heating": "Додати нагрівальний пристрій", "unknown": "Додати зарядний пристроїв або обігрівач"}, "titleEdit": {"charging": "Редагувати точку заряджання", "heating": "Редагувати нагрівальний пристрій", "unknown": "Редагувати зарядний пристрій або обігрівач"}, "titleExample": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>, навіс для автомобіля тощо.", "heating": "Тепловий насос, обігрівач тощо."}, "titleLabel": "Назва", "vehicleAutoDetection": "автоматичне визначення", "vehicleHelpAutoDetection": "Автоматично вибирає найбільш вірогідний транспортний засіб. Можливе ручне перевизначення.", "vehicleHelpDefault": "Завжди припускайте, що цей автомобіль заряджається тут. Автоматичне визначення вимкнено. Можливе ручне перевизначення.", "vehicleLabel": "Автомобіль за замовчуванням", "vehiclesTitle": "Транспортні засоби"}, "main": {"addAdditional": "Додати додатковий лічильник", "addGrid": "Додати вимірювач сітки", "addLoadpoint": "Додати зарядний пристрої або обігрівач", "addPvBattery": "Додати сонячну або батарею", "addTariffs": "Додайте тарифи", "addVehicle": "Додати транспорт", "configured": "налаштовано", "edit": "редагувати", "loadpointRequired": "Потрібно налаштувати принаймні одну точку заряджання.", "name": "Ім'я", "title": "Конфігурація", "unconfigured": "не налаштовано", "vehicles": "Мій транспорт", "yaml": "Пристрої з evcc.yaml не можна редагувати."}, "messaging": {"description": "Отримувати повідомлення про ваші сеанси зарядки.", "title": "Сповіщення"}, "meter": {"cancel": "Скасувати", "delete": "Видалити", "generic": "Загальні інтеграції", "option": {"aux": "Додайте саморегульованого споживача", "battery": "Додати лічильник батареї", "ext": "Додати зовнішній лічильники", "pv": "Додати сонячний лічильник"}, "save": "Зберегти", "specific": "Конкретні інтеграції", "template": "Виробник", "titleChoice": "Що ви хочете додати?", "validateSave": "Перевірте та збережіть"}, "modbus": {"baudrate": "Швидкість передачі даних", "comset": "ComSet'", "connection": "Підключення Modbus", "connectionHintSerial": "Пристрій безпосередньо підключається до evcc через інтерфейс RS485.", "connectionHintTcpip": "Пристрій адресується з evcc через LAN/Wifi.", "connectionValueSerial": "Послідовний / USB", "connectionValueTcpip": "Мережа", "device": "Назва пристрою", "deviceHint": "Приклад: /dev/ttyUSB0", "host": "IP-адреса або ім'я хоста", "hostHint": "Приклад: *********", "id": "ID Modbus", "port": "Порт", "protocol": "Протокол Modbus", "protocolHintRtu": "Підключення через адаптер RS485 до Ethernet без трансляції протоколу.", "protocolHintTcp": "Пристрій має власну підтримку LAN/Wi-Fi або підключений через адаптер RS485 до Ethernet із трансляцією протоколу.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Дозволити кільком клієнтам доступ до одного пристрою Modbus.", "title": "Проксі-сервер <PERSON>"}, "mqtt": {"authentication": "Аутентифікація", "description": "Підключіться до брокера MQTT для обміну даними з іншими системами у вашій мережі.", "descriptionClientId": "Автор повідомлень. Якщо використовується порожній `evcc-[rand]`.", "descriptionTopic": "Залиште пустим, щоб вимкнути публікацію.", "labelBroker": "Брокер", "labelCaCert": "Сертифікат сервера (CA)", "labelCheckInsecure": "Дозволити самопідписані сертифікати", "labelClientCert": "Сертифікат клієнта", "labelClientId": "ID клієнта", "labelClientKey": "Ключ клієнта", "labelInsecure": "Перевірка сертифіката", "labelPassword": "Пароль", "labelTopic": "Тема", "labelUser": "Ім'я користувача", "publishing": "Видавництво", "title": "MQTT"}, "network": {"descriptionHost": "Використовуйте суфікс .local, щоб увімкнути mDNS. Важливо для виявлення мобільного додатку та деяких зарядних пристроїв OCPP.", "descriptionPort": "Порт для веб-інтерфейсу та API. Вам потрібно буде оновити URL-адресу веб-переглядача, якщо ви зміните це.", "descriptionSchema": "Впливає лише на те, як генеруються URL-адреси. Вибір HTTPS не вмикає шифрування.", "labelHost": "Ім'я хоста", "labelPort": "Порт", "labelSchema": "Схема", "title": "Мережа"}, "options": {"boolean": {"no": "ні", "yes": "так"}, "endianness": {"big": "великий байт", "little": "маленький байт"}, "operationMode": {"heating": "Опалення", "standby": "Очікування"}, "schema": {"http": "HTTP (незашифрований)", "https": "HTTPS (зашифрований)"}, "status": {"A": "A (не підключено)", "B": "B (підключений)", "C": "C (зарядка)"}}, "pv": {"titleAdd": "Додати сонячний лічильник", "titleEdit": "Редагувати сонячний лічильник"}, "section": {"additionalMeter": "Додаткові метри", "general": "Загальний", "grid": "Сітка", "integrations": "Інтегр<PERSON><PERSON><PERSON><PERSON>", "loadpoints": "Заряджання та нагрівання", "meter": "Сонячна енергія та батарея", "system": "Система", "vehicles": "Транспортні засоби"}, "sponsor": {"addToken": "Введіть маркер", "changeToken": "Змінити маркер", "description": "Модель спонсорства допомагає нам підтримувати проект і стабільно створювати нові та цікаві функції. Як спонсор ви отримуєте доступ до всіх реалізацій зарядних пристроїв.", "descriptionToken": "Ви отримуєте маркер із {url}. Ми також пропонуємо пробний токен для тестування.", "error": "Маркер спонсора недійсний.", "labelToken": "Токен спонсора", "title": "Спонсорство", "tokenRequired": "Перш ніж створити цей пристрій, потрібно налаштувати маркер спонсора.", "tokenRequiredLearnMore": "Ді<PERSON><PERSON><PERSON><PERSON>е<PERSON>ь більше.", "tokenRequiredShort": "Токен спонсора не налаштовано.", "trialToken": "Пробний жетон"}, "system": {"backupRestore": {"backup": {"action": "Завантажити резервну копію...", "confirmationButton": "Завантажити резервну копію", "confirmationText": "Будь ласка, введіть свій пароль, щоб завантажити файл бази даних.", "description": "Зробіть резервну копію своїх даних у файл. Цей файл можна використовувати для відновлення даних у разі системного збою.", "title": "Резервне копіювання"}, "cancel": "Скасувати", "confirmWithPassword": "Підтвердити дію", "description": "Резервне копіювання, відновлення та скидання даних. Корисно, якщо ви хочете перенести свої дані на іншу систему.", "note": "Примітка: Усі вищезазначені дії впливають лише на дані вашої бази даних. Файл конфігурації evcc.yaml залишається незмінним.", "reset": {"action": "Скинути...", "confirmationButton": "Скинути та перезапустити", "confirmationText": "Це остаточно видалить вибрані дані. Спочатку переконайтеся, що ви завантажили резервну копію.", "description": "Маєте проблеми з конфігурацією та хочете почати спочатку? Видаліть усі дані та почніть з чистого аркуша.", "sessions": "Сеансів зарядки", "sessionsDescription": "Видаляє історію сеансів заряджання.", "settings": "Конфігурація та налаштування", "settingsDescription": "Видаляє всі налаштовані пристрої, служби, плани, кеші тощо.", "title": "Скинути"}, "restore": {"action": "Відновити...", "confirmationButton": "Відновлення та перезапуск", "confirmationText": "Це перезапише всю вашу базу даних. Спочатку переконайтеся, що ви завантажили резервну копію.", "description": "Відновіть дані з резервної копії. Це перезапише всі ваші поточні дані.", "labelFile": "Файл резервної копії", "title": "Відновити"}, "title": "Резервне копіювання та відновлення"}, "logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "restart": "Перезапустіть", "restartRequiredDescription": "Перезапустіть, щоб побачити ефект.", "restartRequiredMessage": "Конфігурація змінена.", "restartingDescription": "Будь ласка, зачекайте…", "restartingMessage": "Перезапуск evcc."}, "tariffs": {"description": "Визначте свої тарифи на електроенергію, щоб розрахувати вартість сеансів заряджання.", "title": "Тарифи"}, "title": {"description": "Відображається на головному екрані та вкладці браузера.", "label": "Назва", "title": "Редагувати назву"}, "validation": {"failed": "не вдалося", "label": "Статус", "running": "перевір<PERSON>а…", "success": "успіх", "unknown": "невідомо", "validate": "перевірити"}, "vehicle": {"cancel": "Скасувати", "chargingSettings": "Налаштування зарядки", "defaultMode": "Режим за замовчуванням", "defaultModeHelp": "Режим зарядки при підключенні автомобіля.", "delete": "Видалити", "generic": "Інші інтеграції", "identifiers": "RFID ідентифікатори", "identifiersHelp": "Список RFID-рядків для ідентифікації транспортного засобу. Один запис на рядок. Поточний ідентифікатор можна знайти на відповідній зарядній станції на сторінці огляду.", "maximumCurrent": "Максимал<PERSON>ний струм", "maximumCurrentHelp": "Має бути більше ніж мінімальний струм.", "maximumPhases": "Максимум фаз", "maximumPhasesHelp": "Скільки фаз може заряджати цей автомобіль? Використовується для розрахунку необхідного мінімального надлишку сонячної енергії та тривалості плану.", "minimumCurrent": "Міні<PERSON><PERSON><PERSON><PERSON><PERSON>ий струм", "minimumCurrentHelp": "Опускайтеся нижче 6А, лише якщо знаєте, що робите.", "online": "Транспорт з онлайн API", "primary": "Загальні інтеграції", "priority": "Пріоритет", "priorityHelp": "Вищий пріоритет означає, що цей транспортний засіб отримує пріоритетний доступ до надлишку сонячної енергії.", "save": "Зберегти", "scooter": "Скутер", "template": "Виробник", "titleAdd": "Додати Транспорт", "titleEdit": "Редагувати Транспорт", "validateSave": "Перевірити та зберегти"}}, "footer": {"community": {"greenEnergy": "Сонячна", "greenEnergySub1": "заряджено з evcc", "greenEnergySub2": "з жовтня 2022", "greenShare": "Сонячна частка", "greenShareSub1": "енергія надається", "greenShareSub2": "сонячна енергія та батарея", "power": "Потужність заряджання", "powerSub1": "{activeClients} з {totalClients} учасників", "powerSub2": "зарядка…", "tabTitle": "Жива спільнота"}, "savings": {"co2Saved": "{valve} збережено", "co2Title": "Викиди CO₂", "configurePriceCo2": "Діз<PERSON>йтеся, як налаштувати дані про ціну та CO₂.", "footerLong": "{percent} сонячної енергії", "footerShort": "{percent} сонячної", "modalTitle": "Огляд зарядної енергії", "moneySaved": "{value} збережено", "percentGrid": "{grid} кВт/год мережі", "percentSelf": "{self} кВт/год сонячної енергії", "percentTitle": "Сонячна енергія", "period": {"30d": "за останні 30 днів", "365d": "за останні 365 днів", "thisYear": "цього року", "total": "з самого початку"}, "periodLabel": "Період:", "priceTitle": "Ціна енергії", "referenceGrid": "сітка", "referenceLabel": "Довідкові дані:", "tabTitle": "Мої дані"}, "sponsor": {"becomeSponsor": "Стати спонсором", "becomeSponsorExtended": "Підтримайте нас безпосередньо, щоб отримати наклейки.", "confetti": "Готові до конфетті?", "confettiPromise": "Ви отримуєте наклейки та цифрові конфетті", "sticker": "… або наклейки evcc?", "supportUs": "Наша місія — зробити заряджання від сонячних батарей нормою. Допоможіть evcc, заплативши стільки, скільки можете.", "thanks": "Дякуємо, {sponsor}! Ваш внесок допомагає розвивати evcc далі.", "titleNoSponsor": "Підтримайте нас", "titleSponsor": "Ви спонсор", "titleTrial": "Пробний режим", "titleVictron": "Спонсор Victron Energy", "trial": "Ви перебуваєте в пробному режимі та можете використовувати всі функції. Будь ласка, подумайте про підтримку проекту.", "victron": "Ви використовуєте evcc на обладнанні Victron Energy і маєте доступ до всіх функцій."}, "telemetry": {"optIn": "Я хочу внести свої дані.", "optInMoreDetails": "Дета<PERSON><PERSON>н<PERSON><PERSON><PERSON> {0}.", "optInMoreDetailsLink": "тут", "optInSponsorship": "Спонсорування необхідне."}, "version": {"availableLong": "доступна нова версія", "modalCancel": "Скасувати", "modalDownload": "Завант<PERSON><PERSON><PERSON>ти", "modalInstalledVersion": "Встановлена версія", "modalNoReleaseNotes": "Жодних нотаток про випуск немає. Детальніше про нову версію:", "modalTitle": "Доступна нова версія", "modalUpdate": "Встановити", "modalUpdateNow": "Встановити зараз", "modalUpdateStarted": "Початок нової версії evcc…", "modalUpdateStatusStart": "Встановлення розпочато:"}}, "forecast": {"co2": {"average": "Середній", "lowestHour": "Найчистіша година", "range": "Діапазон"}, "modalTitle": "Прогноз", "price": {"average": "Середній", "lowestHour": "Найдешевша година", "range": "Діапазон"}, "solar": {"dayAfterTomorrow": "Післязавтра", "partly": "частково", "remaining": "залишилося", "today": "Сьогодні", "tomorrow": "Завтра"}, "solarAdjust": "Скоригуйте сонячний прогноз на основі реальних даних виробництва{percent}.", "type": {"co2": "CO₂", "price": "Ціна", "solar": "Сонячна"}}, "header": {"about": "Про evcc", "authProviders": {"confirmLogout": "Ви впевнені, що хочете відключити {title}?", "title": "Статус авторизації"}, "blog": "Блог", "docs": "Документація", "github": "GitHub", "login": "Автомобільні логіни", "logout": "Вийти", "nativeSettings": "Змінити сервер", "needHelp": "Потрібна Допомога?", "sessions": "Сеанси Зарядки"}, "help": {"discussionsButton": "Обговорення GitHub", "documentationButton": "Документація", "issueButton": "Повідомити про помилку", "issueDescription": "Знайшли дивну чи неправильну поведінку?", "logsButton": "Переглянути журнали", "logsDescription": "Перевірте журнали на наявність помилок.", "modalTitle": "Потрібна допомога?", "primaryActions": "Щось не працює так, як повинно? Це хороші місця, де можна отримати допомогу.", "restart": {"cancel": "Скасувати", "confirm": "Так, перезапустити!", "description": "За звичайних обставин перезапуск не має бути необхідним. Якщо вам потрібно регулярно перезапускати evcc, будь ласка, створіть повідомлення про проблему.", "disclaimer": "Примітка: evcc завершить роботу і покладатиметься на операційну систему для перезапуску служби.", "modalTitle": "Ви впевнені, що хочете перезапустити?"}, "restartButton": "Перезапустити", "restartDescription": "Спробували вимкнути і знову увімкнути?", "secondaryActions": "Все ще не можете вирішити свою проблему? Ось кілька жорсткіших варіантів."}, "log": {"areaLabel": "Фільтрувати за областю", "areas": "Всі області", "download": "Завантажити повний журнал", "levelLabel": "Фільтрувати за рівнем журналу", "nAreas": "{count} області", "noResults": "Немає відповідних записів журналу.", "search": "По<PERSON><PERSON>к", "selectAll": "вибрати все", "showAll": "Показати всі записи", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update": "Автоматичне оновлення"}, "loginModal": {"cancel": "Скасувати", "demoMode": "Вхід не підтримується в демо-режимі.", "error": "Помилка входу: ", "iframeHint": "Відкрийте evcc у новій вкладці.", "iframeIssue": "Ваш пароль правильний, але ваш браузер, здається, втратив файл cookie для автентифікації. Це може статися, якщо ви запускаєте evcc в iframe через HTTP.", "invalid": "Пароль недійсний.", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "Пароль адміністратора", "reset": "Скинути пароль?", "title": "Аутентифікація"}, "main": {"chargingPlan": {"active": "Активний", "addRepeatingPlan": "Додати повторюваний план", "arrivalTab": "Прибуття", "day": "День", "departureTab": "Від'їзд", "goal": "Зарядка гол", "modalTitle": "Тарифний план", "none": "немає", "planNumber": "План {number}", "preconditionDescription": "Зарядіть {тривалість} перед відправленням для попередньої підготовки акумулятора.", "preconditionLong": "Пізня зарядка", "preconditionOptionAll": "все", "preconditionOptionNo": "ні", "preconditionShort": "Пізно", "remove": "Yсувати", "repeating": "повторення", "repeatingPlans": "Повторювані плани", "selectAll": "Вибрати все", "time": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "titleMinSoc": "Мін<PERSON><PERSON><PERSON><PERSON>ьна плата", "titleTargetCharge": "Від'їзд", "unsavedChanges": "Є незбережені зміни. Застосувати зараз?", "update": "Застосувати", "weekdays": "<PERSON><PERSON><PERSON><PERSON>"}, "energyflow": {"battery": "Батарея", "batteryCharge": "Зарядження батареї", "batteryDischarge": "Розрядження батареї", "batteryGridChargeActive": "активна зарядка мережі", "batteryGridChargeLimit": "зарядка мережі коли", "batteryHold": "Акумулятор (locked)", "batteryTooltip": "{energy} із {total} ({soc})", "forecastTooltip": "Прогноз: залишкове виробництво сонячної енергії сьогодні", "gridImport": "Використання мережі", "homePower": "Споживання", "loadpoints": "Зарядний пристрій| Зарядний пристрій | {count} зарядні пристрої", "noEnergy": "Немає даних лічильника", "pv": "Сонячна система", "pvExport": "Експорт мережі", "pvProduction": "Виробництво", "selfConsumption": "Власне споживання"}, "heatingStatus": {"charging": "Опалення…", "connected": "Режим очікування.", "vehicleLimit": "Обмеження нагрівача", "waitForVehicle": "Готов<PERSON>. Очікування обігрівача…"}, "loadpoint": {"avgPrice": "⌀ Ціна", "charged": "Заряджено", "co2": "⌀ CO₂", "duration": "Тривалість", "fallbackName": "Точка зарядки", "finished": "<PERSON>а<PERSON> закінчення", "power": "Потужність", "price": "Вартість", "remaining": "Залишилося", "remoteDisabledHard": "{source}: вимкнено", "remoteDisabledSoft": "{source}: вимкнув адаптивну сонячну зарядку", "solar": "Сонячна"}, "loadpointSettings": {"batteryBoost": {"description": "Швидка зарядка від домашнього акумулятора.", "label": "Батарея Boost", "mode": "Доступно лише в режимі сонячної батареї та режимі min+sonal.", "once": "Boost активний для цього сеансу зарядки."}, "batteryUsage": "Домашня батарея", "currents": "Струм Зарядки", "default": "за замовчуванням", "disclaimerHint": "Примітка:", "limitSoc": {"description": "Ліміт заряджання, який використовується, коли цей автомобіль під’єднано.", "label": "Ліміт за замовчуванням"}, "maxCurrent": {"label": "Макс. струм"}, "minCurrent": {"label": "<PERSON>і<PERSON>. струм"}, "minSoc": {"description": "Для надзвичайних ситуацій. Транспортний засіб «швидко» заряджається до {0} від усіх доступних сонячних батарей, а потім продовжує працювати лише з сонячним надлишком.", "label": "Мін. заряд %"}, "onlyForSocBasedCharging": "Ці параметри доступні лише для автомобілів із відомим рівнем заряду.", "phasesConfigured": {"label": "Фази", "no1p3pSupport": "Як підключений зарядний пристрій?", "phases_0": "авто перемикання", "phases_1": "1 фаза", "phases_1_hint": "({min} до {max})", "phases_3": "3 фаза", "phases_3_hint": "({min} до {max})"}, "smartCostCheap": "Дешева зарядка від мережі", "smartCostClean": "Чиста зарядка мережі", "title": "Налаштування {0}", "vehicle": "Транспортний засіб"}, "mode": {"minpv": "Мін+Сонце", "now": "Швидко", "off": "Вим<PERSON>.", "pv": "Сонячна", "smart": "Розумний"}, "provider": {"login": "увійти", "logout": "вийти"}, "startConfiguration": "Почнемо налаштування", "targetCharge": {"activate": "Активувати", "co2Limit": "Ліміт CO₂ для {co2}", "costLimitIgnore": "Налаштований {ліміт} буде ігноруватися протягом цього періоду.", "currentPlan": "Активний план", "descriptionEnergy": "До якого часу {targetEnergy} повинен бути завантажений в транспортний засіб?", "descriptionSoc": "Коли автомобіль повинен бути заряджений на {targetSoc}?", "goalReached": "Мета вже досягнута", "inactiveLabel": "Запланований час", "nextPlan": "Наступний план", "notReachableInTime": "Ціль буде досягнуто {overrun} пізніше.", "onlyInPvMode": "План заряджання працює тільки в сонячному режимі.", "planDuration": "<PERSON><PERSON><PERSON> зарядки", "planPeriodLabel": "Період", "planPeriodValue": "{start} до {end}", "planUnknown": "ще не відомо", "preview": "Попередній перегляд плану", "priceLimit": "ліміт ціни {price}", "remove": "Вилучити", "setTargetTime": "жодного", "targetIsAboveLimit": "Налаштований ліміт оплати {limit} протягом цього періоду ігноруватиметься.", "targetIsAboveVehicleLimit": "Ліміт транспортних засобів нижчий від цільового тарифу.", "targetIsInThePast": "Виберіть час у майбутньому.", "targetIsTooFarInTheFuture": "Ми скоригуємо план, як тільки дізнаємося більше про майбутнє.", "title": "Запланований Час", "today": "сьогодні", "tomorrow": "завтра", "update": "Оновлення", "vehicleCapacityDocs": "Діз<PERSON>йтеся, як це налаштувати.", "vehicleCapacityRequired": "Для оцінки часу заряджання потрібна ємність акумулятора автомобіля."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON> зарядки", "co2Label": "Викиди CO₂ ⌀", "priceLabel": "Ціна на енергію", "timeRange": "{day} {range} год", "unknownPrice": "ще невідомо"}, "targetEnergy": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noLimit": "жодного"}, "vehicle": {"addVehicle": "Додати транспортний засіб", "changeVehicle": "Змінити транспортний засіб", "detectionActive": "Виявлення транспортного засобу…", "fallbackName": "Транспортний засіб", "moreActions": "Більше дій", "none": "Без транспортного засобу", "notReachable": "Автомобіль недоступний. Спробуйте перезапустити evcc.", "targetSoc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "temp": "Темп.", "tempLimit": "<PERSON>ім<PERSON>т темп", "unknown": "Гостьовий транспортний засіб", "vehicleSoc": "Заряд"}, "vehicleStatus": {"awaitingAuthorization": "Очікування авторизації.", "batteryBoost": "Підвищення заряду батареї активне.", "charging": "Зарядка…", "cheapEnergyCharging": "Доступна дешева енергія.", "cheapEnergyNextStart": "Дешева енергія в {duration}.", "cheapEnergySet": "Ціновий ліміт встановлено.", "cleanEnergyCharging": "Чиста енергія доступна.", "cleanEnergyNextStart": "Чиста енергія в {duration}.", "cleanEnergySet": "Встановлено обмеження CO₂.", "climating": "Виявлено попередню підготовку.", "connected": "Підключено.", "disconnectRequired": "Сеанс припинено. Підключіться повторно.", "disconnected": "Відключено.", "feedinPriorityNextStart": "Високі тарифи на електроенергію почнуть діяти через {duration}.", "feedinPriorityPausing": "Заряджання сонячною енергією призупинено для максимізації подачі енергії.", "finished": "Готово.", "minCharge": "Мінімальна зарядка до {soc}.", "pvDisable": "Недостатньо надлишків. Скоро припинення.", "pvEnable": "Надлишки доступні. Скоро почнеться.", "scale1p": "Зменшення до 1-фазного живлення скоро.", "scale3p": "Збільшення до 3-фазного живлення скоро.", "targetChargeActive": "Запланований заряд активний. Орієнтовний фініш через {duration}.", "targetChargePlanned": "Запланована зарядка починається о {duration}.", "targetChargeWaitForVehicle": "План заряджання готовий. Очікування на транспортний засіб…", "vehicleLimit": "Ліміт транспортного засобу", "vehicleLimitReached": "Ліміт транспортного засобу досягнуто.", "waitForVehicle": "Готовий. Очікування на транспортний засіб…", "welcome": "Короткий початковий заряд для підтвердження підключення."}, "vehicles": "Паркування", "welcome": "Привіт на борту!"}, "notifications": {"dismissAll": "Відхилити все", "logs": "Переглянути повні журнали", "modalTitle": "Сповіщення"}, "offline": {"configurationError": "Помилка під час запуску. Перевірте конфігурацію та перезапустіть.", "message": "Не підключено до сервера.", "restart": "Перезапустіть", "restartNeeded": "Необхідно для застосування змін.", "restarting": "Сервер повернеться за мить."}, "passwordModal": {"description": "Встановіть пароль для захисту параметрів конфігурації. Використання головного екрана все ще можливо без входу.", "empty": "Пароль не повинен бути порожнім", "error": "Помилка: ", "labelCurrent": "Поточний пароль", "labelNew": "Новий пароль", "labelRepeat": "Повторіть пароль", "newPassword": "Створити пароль", "noMatch": "Паролі не збігаються", "titleNew": "Встановити пароль адміністратора", "titleUpdate": "Оновити пароль адміністратора", "updatePassword": "Оновити пароль"}, "session": {"cancel": "Скасувати", "co2": "CO₂", "date": "Період", "delete": "Видалити", "finished": "Завершено", "meter": "Л<PERSON>чильник", "meterstart": "Початок лічильника", "meterstop": "Зупинка лічильника", "odometer": "Проб<PERSON>г", "price": "Ціна", "started": "Розпочато", "title": "Сеанс зарядки"}, "sessions": {"avgPower": "⌀ потужність", "avgPrice": "⌀ Ціна", "chargeDuration": "Тривалість", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Ціна {byGroup}", "byGroupLoadpoint": "через пункт зарядки", "byGroupVehicle": "транспортним засобом", "energy": "Заряджена енергія", "energyGrouped": "Сонячна енергія проти електромережі", "energyGroupedByGroup": "Енерг<PERSON>я {byGroup}", "energySubSolar": "{value} сонячний", "energySubTotal": "{value} всього", "groupedCo2ByGroup": "CO₂-Сума {byGroup}", "groupedPriceByGroup": "Зага<PERSON><PERSON>на вартість {byGroup}", "historyCo2": "CO₂-Викиди", "historyCo2Sub": "{value} всього", "historyPrice": "Витрати на зарядку", "historyPriceSub": "{value} всього", "solar": "Сонячна частка за рік", "solarByGroup": "Сонячна акція {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Енергія (кВт/год)", "chargeduration": "Тривалість", "co2perkwh": "CO₂/kWh'", "created": "Створено", "finished": "Завершено", "identifier": "Ідентифікатор", "loadpoint": "Точка зарядки", "meterstart": "Лічильник пуску (кВт/год)", "meterstop": "Лічильник зупинки (кВт/год)", "odometer": "Проб<PERSON><PERSON> (км)", "price": "Ціна", "priceperkwh": "Ціна/kWh", "solarpercentage": "Соляр (%)", "vehicle": "Транспортний засіб"}, "csvPeriod": "Завант<PERSON><PERSON><PERSON><PERSON><PERSON> {period} CSV", "csvTotal": "Завантажити повний CSV", "date": "Почати", "energy": "Заряджено", "filter": {"allLoadpoints": "всі точки зарядки", "allVehicles": "всі транспортні засоби", "filter": "Фільтр"}, "group": {"co2": "Викиди", "grid": "Сітка", "price": "Ціна", "self": "Соляр"}, "groupBy": {"loadpoint": "Точка зарядки", "none": "Всього", "vehicle": "Транспортний засіб"}, "loadpoint": "Точка зарядки", "noData": "Ніяких зарядних сесій цього місяця.", "overview": "Огляд", "period": {"month": "Місяць", "total": "Всього", "year": "<PERSON><PERSON><PERSON>"}, "price": "Вартість", "reallyDelete": "Ви дійсно хочете видалити цей сеанс?", "showIndividualEntries": "Показати окремі сеанси", "solar": "Сонячна", "title": "Сеанси зарядки", "total": "Всього", "type": {"co2": "CO₂", "price": "Ціна", "solar": "Соляр"}, "vehicle": "Транспортний засіб"}, "settings": {"fullscreen": {"enter": "Перейти в повноекранний режим", "exit": "Вийти з повноекранного режиму", "label": "Повноекранний"}, "hiddenFeatures": {"label": "Експериментальний", "value": "Показати експериментальні функції інтерфейсу."}, "language": {"auto": "Автоматично", "label": "Мова"}, "sponsorToken": {"expires": "Термін дії вашого спонсорського токена закінчується {inXDays}. {getNewToken} і оновіть його тут.", "getNew": "Візьми свіжий", "hint": "Примітка: Ми автоматизуємо це в майбутньому."}, "telemetry": {"label": "Телеметрія"}, "theme": {"auto": "система", "dark": "темна", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "світла"}, "time": {"12h": "12 г", "24h": "24 г", "label": "Формат часу"}, "title": "Інтерфейс користувача", "unit": {"km": "км", "label": "Одиниці вимірювання", "mi": "милі"}}, "smartCost": {"activeHours": "{active} зі {total}", "activeHoursLabel": "Активні години", "applyToAll": "Застосовувати всюди?", "batteryDescription": "Заряджає домашній акумулятор енергією з мережі.", "cheapTitle": "Дешева зарядка від мережі", "cleanTitle": "Чиста зарядка мережі", "co2Label": "Викиди CO₂", "co2Limit": "Ліміт CO₂", "loadpointDescription": "Вмикає тимчасову швидку зарядку в сонячному режимі.", "modalTitle": "Розумна зарядка", "none": "немає", "priceLabel": "Ціна енергії", "priceLimit": "<PERSON>ім<PERSON>т ціни", "resetAction": "Зняти обмеження", "resetWarning": "Дина<PERSON><PERSON>чна ціна мережі або джерело CO₂ не налаштовані. Однак ліміт все ще існує в {limit}. Очистити конфігурацію?", "saved": "Збережено."}, "smartFeedInPriority": {"activeHoursLabel": "Призупинені години", "description": "Призупиняє заряджання під час високих цін, щоб надати пріоритет вигідному постачанню електроенергії з мережі.", "priceLabel": "Коефіцієнт подачі електроенергії", "priceLimit": "Обмеження подачі електроенергії", "resetWarning": "Динамічний «зелений» тариф не налаштовано. Однак ліміт все ще існує в {limit}. Очистити конфігурацію?", "title": "Пріоритет подачі електроенергії"}, "startupError": {"configFile": "Використаний файл конфігурації:", "configuration": "Конфігурація", "description": "Будь ласка, перевірте файл конфігурації. Якщо повідомлення про помилку не допомогло, перевірте {0}.", "discussions": "Обговорення GitHub", "fixAndRestart": "Будь ласка, виправте проблему та перезапустіть сервер.", "hint": "Примітка: Також може бути, що у вас несправний пристрій (інвертор, лічильник, …). Перевірте підключення до мережі.", "lineError": "Помилка в {0}.", "lineErrorLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "restartButton": "Перезапустити", "title": "Помилка запуску"}}