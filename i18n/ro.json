{"batterySettings": {"batteryLevel": "Nivelul bateriei", "bufferStart": {"above": "cand e peste {soc}", "full": "cand atinge starea de incarcare", "never": "numai cand exista surplus suficient"}, "capacity": "{energy} din {total}", "control": "Controlul bateriei", "discharge": "“Preveniți descărcarea în modul rapid și încărcarea planificată.”", "disclaimerHint": "Nota:", "disclaimerText": "Aceste setari afecteaza doar modul solar. Procesul de incarcare este ajustat corespunzator", "legendBottomName": "prioritate casa", "legendBottomSubline": "neutilizat pentru incarcare", "legendMiddleName": "in primul rand vehiculul", "legendMiddleSubline": "in al doilea rand casa", "legendTopAutostart": "porneste automat", "legendTopName": "Incarcare ajutata de baterie", "legendTopSubline": "fara intre<PERSON>", "modalTitle": "Setari bateriei"}, "config": {"battery": {"titleAdd": "adăugați contorul de baterie", "titleEdit": "Editați contorul de baterie."}, "deviceValue": {"capacity": "Capacitatea", "chargeStatus": "Stare", "chargedEnergy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current": "<PERSON>nt", "enabled": "Activat", "energy": "Energie", "odometer": "<PERSON><PERSON><PERSON><PERSON>", "phaseCurrents": "Curent L1, L2, L3", "phasePowers": "Putere L1, L2, L3", "phaseVoltages": "Voltaj L1, L2, L3", "power": "<PERSON><PERSON>", "range": "Autonomie", "soc": "Stare de încărcare"}, "form": {"example": "Exemplu", "optional": "optional"}, "grid": {"titleAdd": "Adăugați Contorul de Grilă", "titleEdit": "Editați Contorul de bransament"}, "main": {"addLoadpoint": "Adăugați punct de încărcare", "addPvBattery": "Adăugați solar sau baterie", "addVehicle": "Adauga masina", "edit": "<PERSON><PERSON><PERSON>", "title": "Configuratie", "unconfigured": "neconfigurat", "vehicles": "Masinile mele", "yaml": "Configurat în evcc.yaml. Nu poate fi editat în interfața"}, "meter": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "șterge", "save": "Salveaza", "template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleChoice": "Ce vrei să adaugi?", "validateSave": "Valideaza si salveaza"}, "pv": {"titleAdd": "Adauga SmartMeter", "titleEdit": "Editeaza SmartMeter"}, "validation": {"failed": "esuat", "label": "Stare", "running": "validez...", "success": "re<PERSON><PERSON><PERSON>", "unknown": "necunoscut", "validate": "<PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Sterge", "generic": "Alte integrari", "online": "Masini cu API oline", "save": "Salveaza", "scooter": "<PERSON><PERSON>", "template": "Producator", "titleAdd": "Ada<PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>", "validateSave": "Valideaza si salveaza"}}, "footer": {"community": {"greenEnergy": "Solar", "greenEnergySub1": "încărcat cu evcc", "greenEnergySub2": "din Octombrie 2022", "greenShare": "Procent solar", "greenShareSub1": "putere furnizată de", "greenShareSub2": "solar și baterie", "power": "Putere de încărcare", "powerSub1": "{activeClients} din {totalClients} participanți", "powerSub2": "înc<PERSON><PERSON><PERSON>…", "tabTitle": "Comunitate live"}, "savings": {"co2Saved": "{value} salvat", "co2Title": "Emisii CO₂", "configurePriceCo2": "Invata cum sa configurezi pretul si datele despre CO₂", "footerLong": "{percent} energie solară", "footerShort": "{percent} solar", "modalTitle": "Sumar energie încărcare", "moneySaved": "{value} salvat", "percentGrid": "{grid} kWh rețea", "percentSelf": "{self} kWh solar", "percentTitle": "Energie solară", "period": {"30d": "ultimele 30 de zile", "365d": "ultimul an", "total": "de la inceput"}, "periodLabel": "Perioada:", "priceTitle": "Preț Energie", "referenceGrid": "bransament", "referenceLabel": "Date de referinta", "tabTitle": "<PERSON>le mele"}, "sponsor": {"becomeSponsor": "Sponsorizează-ne", "confetti": "Ești pregătit să sărbătorești?", "confettiPromise": "Primești abțibilduri si confetti digital", "sticker": "… sau abțibilduri evcc?", "supportUs": "Țelul nostru este ca energia solara să devina standard. Ajută și tu la dezvoltarea EVCC plătind atât cât valorează pt tine.", "thanks": "<PERSON><PERSON><PERSON><PERSON>, {sponsor}! Contribuția ta ajuta la dezvoltarea evcc.", "titleNoSponsor": "Sponsorizeaza-ne", "titleSponsor": "Ești sponsor"}, "telemetry": {"optIn": "<PERSON><PERSON><PERSON> să trimit datele mele.", "optInMoreDetails": "<PERSON> multe detalii {0}.", "optInMoreDetailsLink": "aici", "optInSponsorship": "Trebuie să fii sponsor."}, "version": {"availableLong": "versiune nouă disponibilă", "modalCancel": "<PERSON><PERSON><PERSON><PERSON>", "modalDownload": "Descă<PERSON><PERSON>i", "modalInstalledVersion": "<PERSON><PERSON><PERSON><PERSON> instalată", "modalNoReleaseNotes": "Nu există informații despre aceasta versiune. Vezi:", "modalTitle": "Versiune nouă disponibilă", "modalUpdate": "Instalează", "modalUpdateNow": "Instalează acum", "modalUpdateStarted": "Pornesc noua versiune evcc...", "modalUpdateStatusStart": "Instalare pornita:"}}, "header": {"about": "<PERSON><PERSON><PERSON>", "blog": "Blog", "docs": "Documentație", "github": "GitHub", "login": "<PERSON><PERSON>", "needHelp": "Ai nevoie de Ajutor ?", "sessions": "Sesiuni de Incărcare"}, "help": {"discussionsButton": "Discutii GitHub", "documentationButton": "Documentatie", "issueButton": "Raporteaza un Bug", "issueDescription": "Functionare bizara sau gresita ?", "modalTitle": "Ai nevoie de ajutor ?", "primaryActions": "Ceva nu functioneaza cum ar trebui? Aici este locul potrivit sa primesti ajutor.", "restart": {"cancel": "Anuleaza", "confirm": "Da, reporneste!", "description": "In mod normal, restarul nu ar trebui sa fie necesar. Te rog sa trimiti un raport daca trebuie sa restartezi des EVCC.", "disclaimer": "Nota: evcc se va inchide si se va baza pe OS sa restarteze serviciul sau.", "modalTitle": "Esti sigur ca vrei sa dai restart?"}, "restartButton": "<PERSON><PERSON>", "restartDescription": "Ai incercat sa inchizi si sa deschizi din nou?", "secondaryActions": "Inca nu ati rezolvat problema? Iata niste alte optiuni"}, "main": {"chargingPlan": {"active": "Activ", "arrivalTab": "<PERSON><PERSON><PERSON><PERSON>", "day": "<PERSON><PERSON>", "departureTab": "Plecare", "goal": "Scopul incarcarii", "modalTitle": "Plan de incarcare", "none": "nimic", "remove": "Elimina", "time": "<PERSON><PERSON>", "title": "Plan", "titleMinSoc": "Incarcare minima", "titleTargetCharge": "Plecare", "update": "Aplica"}, "energyflow": {"battery": "Baterie", "batteryCharge": "Incarca bateria", "batteryDischarge": "Descărcare baterie", "batteryHold": "Baterie (inchisa)", "batteryTooltip": "{energy} din {total} ({soc})", "gridImport": "Folosirea rețelei", "homePower": "Consum", "loadpoints": "Încarcator| Încarcator | {count} încarcatoare", "noEnergy": "Lips<PERSON> date contor", "pvExport": "Export in rețea", "pvProduction": "Producție", "selfConsumption": "Consum propriu"}, "heatingStatus": {"charging": "Incalzeste...", "waitForVehicle": "Pregatit. <PERSON><PERSON><PERSON> dupa incalzitor…"}, "loadpoint": {"avgPrice": "⌀ Pret", "charged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON>", "fallbackName": "Punct de încărcare", "power": "<PERSON><PERSON>", "price": "Σ Pret", "remaining": "<PERSON><PERSON>", "remoteDisabledHard": "{source}: inchis", "remoteDisabledSoft": "{source}: incarcarea solar-adaptiva e oprita", "solar": "Solar"}, "loadpointSettings": {"currents": "<PERSON><PERSON>", "default": "default", "disclaimerHint": "Nota:", "limitSoc": {"description": "Limita de incarcare care e folosita cand e conectat a aceasta masina.", "label": "Limita default"}, "maxCurrent": {"label": "Amperaj maxim"}, "minCurrent": {"label": "<PERSON><PERSON><PERSON> minim"}, "minSoc": {"description": "Vehiculul primește încărcare rapida {0} din toata energia solara disponibilă, apoi continua încărcarea cu surplusul solar.", "label": "<PERSON>. <PERSON> %"}, "onlyForSocBasedCharging": "Aceste optiuni sunt active doar pentru masini carora le cunoaste gradul de incarcare.", "phasesConfigured": {"label": "Faze", "no1p3pSupport": "Cum e conectat incarcatorul tau?", "phases_0": "auto-comutare", "phases_1": "faza 1", "phases_1_hint": "({min} la {max})", "phases_3": "3 faze", "phases_3_hint": "({min} la {max})"}, "title": "<PERSON><PERSON><PERSON> {0}", "vehicle": "Vehicul"}, "mode": {"minpv": "Min+Solar", "now": "Rapid", "off": "<PERSON><PERSON>", "pv": "Solar"}, "provider": {"login": "autentificare", "logout": "deconectează"}, "targetCharge": {"activate": "Activează", "co2Limit": "limita CO₂ de {co2}", "costLimitIgnore": "{limit} configurata de va fi ingnorata in acest timp.", "currentPlan": "Plan activ", "descriptionEnergy": "Pâna când ar trebui ca {targetEnergy} să fie încărcată in vehicul?", "descriptionSoc": "Când ar trebui vehiculul să fie încărcat la {targetSoc}?", "inactiveLabel": "<PERSON><PERSON> dorit", "notReachableInTime": "Telul nu a fost atins la timp. Estimare: {endTime}.", "onlyInPvMode": "Planul de incarcare merge doar in mod Solar.", "planDuration": "<PERSON><PERSON>", "planPeriodLabel": "Perioada", "planPeriodValue": "{start} la {end}", "planUnknown": "necunoscut", "preview": "<PERSON><PERSON><PERSON>", "priceLimit": "pretul limita de {price}", "remove": "Elimina", "setTargetTime": "nimic", "targetIsAboveLimit": "<PERSON><PERSON> configurata - {limit}, va fi ignorata in timpul incarcarii.", "targetIsAboveVehicleLimit": "Mareste limita de incarcare masina ({limit}) pentru a atinge incarcarea dorita", "targetIsInThePast": "Alege o dată in viitor, DORELE.", "targetIsTooFarInTheFuture": "Vom ajusta planul de îndată ce vom avea mai multe informații despre viitor.", "title": "<PERSON><PERSON> dorit", "today": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "mâine", "update": "Actualizare", "vehicleCapacityDocs": "Invata cum sa configurezi.", "vehicleCapacityRequired": "Capacitatea bateriei masinii e necesara pentru a estima timpul de incarcare."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON>", "co2Label": "Emisie CO₂ ⌀", "priceLabel": "Prețul energiei", "timeRange": "{day} {range} h", "unknownPrice": "încă ne<PERSON>"}, "targetEnergy": {"label": "<PERSON><PERSON>", "noLimit": "nimic"}, "vehicle": {"addVehicle": "Adăugați un vehicul", "changeVehicle": "Schimbă vehiculul", "detectionActive": "Detectare vehicul…", "fallbackName": "vehicul", "moreActions": "<PERSON> multe acțiuni", "none": "Lipsă Vehicul", "targetSoc": "Limită", "temp": "Temp.", "tempLimit": "Temp. limit", "unknown": "Vehicul musafir", "vehicleSoc": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vehicleStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "cheapEnergyCharging": "Energie ieftina disponibila. {price} (limit {limit})", "cleanEnergyCharging": "Energie curata disponibila. {co2} (limit {limit})", "climating": "Preconditionare detectata.", "connected": "Conectat.", "disconnected": "Deconectat.", "minCharge": "Incarcare minima {soc}.", "pvDisable": "Surplus insuficient FV. <PERSON> in {remaining}…", "pvEnable": "Surplus FV disponibil. Pornesc în {remaining}…", "scale1p": "Reduc încărcarea la faza 1 în {remaining}…", "scale3p": "Schimbam încărcarea la 3 faze în {remaining}…", "targetChargeActive": "Încarcare țintă activă…", "targetChargePlanned": "Înc<PERSON>rcarea pornește la {time}.", "targetChargeWaitForVehicle": "Încărcare terminată. Aștept după vehicul…", "vehicleLimitReached": "<PERSON><PERSON> {soc} a fost atinsă.", "waitForVehicle": "Pregătit. Aștept după vehicul…"}, "vehicles": "Parcare"}, "notifications": {"dismissAll": "<PERSON><PERSON><PERSON> tot", "modalTitle": "Notific<PERSON><PERSON>"}, "offline": {"message": "Nu se poate conecta la server."}, "session": {"cancel": "Anulează", "co2": "CO₂", "date": "Perioada", "delete": "Șterge", "finished": "Încheiată", "meter": "<PERSON><PERSON>", "meterstart": "Index contor de pornire", "meterstop": "Index oprire contor", "odometer": "<PERSON><PERSON><PERSON><PERSON>", "price": "Pret", "started": "Pornit", "title": "Sesiune de încărcare"}, "sessions": {"avgPower": "⌀ Putere", "avgPrice": "⌀ Pret", "chargeDuration": "<PERSON><PERSON>", "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energie (kWh)", "created": "<PERSON><PERSON><PERSON>", "finished": "Terminat", "identifier": "Identificator", "loadpoint": "Punct de încărcare", "meterstart": "Start contor (kWh)", "meterstop": "Stop contor (kWh)", "odometer": "Kilometraj (km)", "vehicle": "Vehicul"}, "csvPeriod": "Descarca {period} CSV", "csvTotal": "Descarca total CSV", "date": "Start", "energy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter": {"allLoadpoints": "toate punctele de incarcare", "allVehicles": "toate masinile", "filter": "Filtru"}, "loadpoint": "Punct <PERSON><PERSON><PERSON><PERSON><PERSON>", "noData": "Nu exista sesiuni de incarcare luna aceasta.", "price": "Σ Pret", "reallyDelete": "Esti sigur că dorești să ștergi această sesiune?", "solar": "Solar", "title": "Sesiuni <PERSON>", "total": "Total", "vehicle": "Vehicul"}, "settings": {"hiddenFeatures": {"label": "Experimentală", "value": "Arată caracteristice experimentale."}, "language": {"auto": "Automat", "label": "Limba"}, "sponsorToken": {"expires": "Tokenul de sponsorizare expira {inXDays}. {getNewToken} si adauga-l in fisierul de configurare.", "getNew": "Descarcati unul nou", "hint": "Nota: Vom automatiza acest proces in viitor."}, "telemetry": {"label": "Telemetrie"}, "theme": {"auto": "sistem", "dark": "întunecat", "label": "Design", "light": "<PERSON><PERSON><PERSON>"}, "title": "Configuratie", "unit": {"km": "km", "label": "Unități de măsura", "mi": "mile"}}, "smartCost": {"activeHours": "{active} din {total}", "activeHoursLabel": "Ore de functionare", "co2Label": "emisii CO₂", "co2Limit": "limita CO₂", "loadpointDescription": "Activeaza temporar Incarcarea Rapida in mod Solar.", "modalTitle": "Incarcare Smart Grid", "none": "nimic", "priceLabel": "Pretul energiei", "priceLimit": "<PERSON><PERSON>"}, "startupError": {"configFile": "<PERSON><PERSON><PERSON> configurare folo<PERSON>t:", "configuration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Verifică fișierul de configurație. Dacă mesajul de eroare nu te ajută, verifică {0}.", "discussions": "Discută pe GitHub", "fixAndRestart": "Repară eroarea și restartează serverul.", "hint": "Nota: E posibil să ai un dispozitiv defect (invertor, contor etc). Verifică conexiunile la rețea.", "lineError": "<PERSON><PERSON><PERSON> la {0}.", "lineErrorLink": "linia {0}", "restartButton": "<PERSON><PERSON>", "title": "Eroare la pornire încărcare"}}