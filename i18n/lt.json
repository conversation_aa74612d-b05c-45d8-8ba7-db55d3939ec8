{"batterySettings": {"batteryLevel": "<PERSON><PERSON><PERSON><PERSON> įkrova", "bufferStart": {"above": "kai virš {soc}.", "full": "kai {soc}.", "never": "tik su pakankamu perte<PERSON>i."}, "capacity": "{energy} iš {total}", "control": "<PERSON><PERSON><PERSON><PERSON>", "discharge": "Neleisti kaupiklio i<PERSON>krov<PERSON> režime Greitas ir planiniame įkrovime.", "disclaimerHint": "Pastaba:", "disclaimerText": "Šie nustatymai paveikia tik įkrovimą Saulė. Įkrovimo algoritmas atiti<PERSON>i pakeič<PERSON>.", "gridChargeTab": "Įkrovimas iš tinklo", "legendBottomName": "Prioritetas namo ka<PERSON>lio įkrovimui", "legendBottomSubline": "kol pasieks {soc}.", "legendMiddleName": "Prioritetas automobilio įkrovimui", "legendMiddleSubline": "kai namo kaup<PERSON> yra virš {soc}.", "legendTopAutostart": "<PERSON><PERSON>ti automatiškai", "legendTopName": "Automobilio įkrovimas su kaupiklio pagalba", "legendTopSubline": "kai kaup<PERSON> yra virš {soc}.", "modalTitle": "<PERSON><PERSON>", "usageTab": "<PERSON><PERSON><PERSON><PERSON>"}, "config": {"aux": {"description": "Įrenginys, kuris kore<PERSON> savo suvartojimą pagal esamą energijos perteklių (pvz., išmanieji vandens šildytuvai). evcc tikisi, kad šis įrenginys prireikus sumažins energijos suvartojimą.", "titleAdd": "Pridėti Savireguliuojantį vartotoją", "titleEdit": "Redaguoti Savireguliuojantį vartotoją"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON> kaupiklį", "titleEdit": "Redaguoti kaupiklį"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON> įkroviklio skaitiklį", "titleEdit": "Redaguoti įkrovimo skaitiklį"}, "charger": {"chargers": "Elektromobilių įkrovikliai", "generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heatingdevices": "Šildymo prietaisai", "ocppHelp": "Nukopijuokite šį adresą į jūsų įkroviklio nustatymus.", "ocppLabel": "OCPP-Serverio URL", "switchsockets": "<PERSON><PERSON><PERSON><PERSON>", "template": "Gamintojas", "titleAdd": {"charging": "<PERSON><PERSON><PERSON><PERSON> Įkroviklį", "heating": "<PERSON><PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "Redaguoti įkroviklį", "heating": "Redaguoti Šildytuvą"}, "type": {"custom": {"charging": "Vartotojo nustatytas įkroviklis", "heating": "Vartotojo nustatyt<PERSON>"}, "heatpump": "Vartotojo nustatytas <PERSON>", "sgready": "Vartotojo nustatytas š<PERSON> (SG-Ready, visi)", "sgready-boost": "Vartotojo nustatytas šilum<PERSON> (SG-Ready, boost)", "switchsocket": "Vartotojo apibrėžta išmanioji rozetė"}}, "circuits": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad naudo<PERSON>t visus į grandinę sujungtus įkroviklius, ne<PERSON><PERSON><PERSON><PERSON>ėte sukonfigūruotų galios ir srov<PERSON> ribų. Grandines galima sud<PERSON>, kad būtų sukurta hierarchija.", "title": "Apkrovos valdymas"}, "control": {"description": "Paprastai numatytosios re<PERSON>š<PERSON>ės yra p<PERSON>. Keiskite jas tik jei <PERSON>, k<PERSON> da<PERSON>.", "descriptionInterval": "Valdymo cikl<PERSON> interval<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, ka<PERSON> evcc nuskaito skaitiklių duomenis, koreguoja įkrovimo galią ir atnaujina vartotojo sąsają. Pernelyg trumpi intervalai (< 30 s) gali sukelti svyravimus ir nepageidaujamus rezultatus.", "descriptionResidualPower": "Pakeičia valdymo sistemos reguliavimo tašką. Jei turite namų kaupiklį, rekomenduojame nustatyti 100 W vertę. <PERSON><PERSON> tur<PERSON> nedidelį prioritetą, palyginus su tinklu.", "labelInterval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelResidualPower": "Likutinė galia", "title": "<PERSON><PERSON><PERSON>"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON>", "broker": "Broker sistema", "bucket": "Bucket", "capacity": "<PERSON><PERSON><PERSON>", "chargeStatus": "Statusas", "chargeStatusA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chargeStatusB": "prijun<PERSON><PERSON>", "chargeStatusC": "įkraunama", "chargeStatusE": "nėra elektros", "chargeStatusF": "k<PERSON>a", "chargedEnergy": "Įkrauta", "co2": "Tinklo CO₂", "configured": "Sukonfigūruota", "controllable": "Valdomas", "currency": "Valiuta", "current": "Srovė", "currentRange": "Srovė", "enabled": "Aktyvuotas", "energy": "Energija", "feedinPrice": "<PERSON><PERSON><PERSON> į tinklą kaina", "gridPrice": "<PERSON><PERSON><PERSON><PERSON> pirk<PERSON> kaina", "heaterTempLimit": "Šil<PERSON><PERSON>", "hemsType": "Sistema", "identifier": "RFID-Identifikatorius", "no": "ne", "odometer": "Odometras", "org": "Organizacija", "phaseCurrents": "Srovė L1, L2, L3", "phasePowers": "Galia L1, L2, L3", "phaseVoltages": "Įtampa L1, L2, L3", "phases1p3p": "Fazių perjungimas", "power": "Galia", "powerRange": "Galia", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singlePhase": "Vienfazis", "soc": "Įkrova", "solarForecast": "<PERSON><PERSON><PERSON> pro<PERSON>", "temp": "Temperat<PERSON>ra", "topic": "<PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "Automobilio limitas", "yes": "taip"}, "deviceValueChargeStatus": {"A": "A (neprijungtas)", "B": "B (prijungtas)", "C": "C (įkrauna)"}, "devices": {"auxMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batteryStorage": "<PERSON><PERSON><PERSON><PERSON>", "solarSystem": "<PERSON><PERSON><PERSON>"}, "editor": {"loading": "Užkraunamas YAML redaktorius…"}, "eebus": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, leidžianti evcc susisiekti su EEBus įrenginiais.", "title": "EEBus"}, "ext": {"description": "<PERSON><PERSON> b<PERSON><PERSON> naudo<PERSON><PERSON> a<PERSON>krov<PERSON> valdymo ar statistikos tikslais.", "titleAdd": "<PERSON><PERSON><PERSON><PERSON> Išorinį skaitiklį", "titleEdit": "Redaguoti Išorinį skaitiklį"}, "form": {"danger": "<PERSON><PERSON><PERSON><PERSON>", "deprecated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "example": "Pa<PERSON><PERSON><PERSON><PERSON>", "optional": "pasirinktinai"}, "general": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "customHelp": "Sukurkite vartotojo a<PERSON>brėžtą įrenginį naudodami evcc įskiepių sistemą.", "customOption": "Vartotojo apibrėžtas įrenginys", "delete": "<PERSON><PERSON><PERSON><PERSON>", "docsLink": "Žiūrėkite dokumentaciją.", "experimental": "Eksperimentinis", "hideAdvancedSettings": "Slėpti išplėstinius nustatymus", "invalidFileSelected": "Pasirinktas netinkamas failas", "noFileSelected": "Nepasirinktas failas.", "off": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on": "Įjungta", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readFromFile": "Skai<PERSON><PERSON> i<PERSON>o", "remove": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "selectFile": "Naršyti", "showAdvancedSettings": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>ė<PERSON><PERSON><PERSON> nustatymus", "telemetry": "Telemetrija", "templateLoading": "Įkrauname...", "title": "Pavadinimas", "validateSave": "Pat<PERSON><PERSON><PERSON> ir <PERSON>"}, "grid": {"title": "<PERSON><PERSON><PERSON>", "titleAdd": "<PERSON><PERSON><PERSON><PERSON> skaitiklį", "titleEdit": "Redaguoti tinklo skaitiklį"}, "hems": {"description": "Prijunkite evcc prie kitos namų energijos valdymo sistemos.", "title": "HEMS"}, "icon": {"change": "p<PERSON><PERSON>i"}, "influx": {"description": "Įrašo įkrovimo ir kitus duomenis į InfluxDB. Duomenims vizualizuoti naudokite \"Grafana\" ar pan<PERSON><PERSON><PERSON> įrankius.", "descriptionToken": "Patikrinkite InfluxDB dokumentaciją, kad <PERSON><PERSON>, kaip j<PERSON> sukurti. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Le<PERSON>i savarankiškai pasira<PERSON><PERSON><PERSON> sertifikatus", "labelDatabase": "Duomenų bazė", "labelInsecure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelOrg": "Organizacija", "labelPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelToken": "API žetonas (token)", "labelUrl": "URL", "labelUser": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "title": "InfluxDB", "v1Support": "Reikia InfluxDB 1.x palaikymo?", "v2Support": "Atgal į InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "<PERSON><PERSON><PERSON><PERSON> įkroviklį", "heating": "Pridė<PERSON> šildytuvą"}, "addMeter": "<PERSON><PERSON><PERSON><PERSON> energijos skaitiklį", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "chargerError": {"charging": "<PERSON><PERSON><PERSON> sukonfigū<PERSON>oti įkroviklį.", "heating": "Reikia sukonfigūruoti šildytuvą."}, "chargerLabel": {"charging": "Įkroviklis", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Naudos įkrovimo srovę nuo 6 iki 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "<PERSON>udos įkrovimo srovę nuo 6 iki 32 A.", "chargerPowerCustom": "kiti", "chargerPowerCustomHelp": "Nurodykite įkrovimo s<PERSON> ribas.", "chargerTypeLabel": "Įkroviklio tipas", "chargingTitle": "Elgesys", "circuitHelp": "Apkrovos v<PERSON>, <PERSON><PERSON><PERSON><PERSON>, kad ne<PERSON><PERSON> viršytos galios ir s<PERSON> rib<PERSON>.", "circuitLabel": "Grandinė", "circuitUnassigned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultModeHelp": {"charging": "Įkrovimo režimas prijungus automobilį.", "heating": "Nustatoma sistemos paleidimo metu."}, "defaultModeHelpKeep": "Išsaugo paskutinį pasirinktą režimą.", "defaultModeLabel": "Numaty<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "electricalSubtitle": "<PERSON><PERSON> a<PERSON>, pasitarkite su elektriku.", "electricalTitle": "Elektros parametrai", "energyMeterHelp": "<PERSON><PERSON><PERSON><PERSON>, jei įkroviklyje n<PERSON>ra integ<PERSON>.", "energyMeterLabel": "<PERSON><PERSON><PERSON><PERSON>", "estimateLabel": "Interpoliuoti įkrovos lygį tarp API naujinimų", "maxCurrentHelp": "<PERSON><PERSON> <PERSON><PERSON><PERSON> did<PERSON> nei minimali s<PERSON>ė.", "maxCurrentLabel": "<PERSON>", "minCurrentHelp": "Tik j<PERSON>, <PERSON><PERSON>, leiskite žemiau 6 A.", "minCurrentLabel": "<PERSON>", "noVehicles": "Nėra sukonfigūruotų automobilių.", "option": {"charging": "Pridėti įkrovimo vietą", "heating": "Pridėti šildy<PERSON>"}, "phases1p": "1-fazė", "phases3p": "3-<PERSON><PERSON><PERSON><PERSON>", "phasesAutomatic": "Automatin<PERSON><PERSON> f<PERSON>", "phasesAutomaticHelp": "Jūsų įkroviklis palaiko automatinį perjungimą tarp 1 ir 3 fazių įkrovimo. Pagrindiniame ekrane galite reguliuoti fazių elgesį įkrovimo metu.", "phasesHelp": "Prijungtų fazių skaičius.", "phasesLabel": "Fazė<PERSON>", "pollIntervalDanger": "Reguliarios automobilio užklausos gali iškrauti automobilio akumuliatorių. Kai kurie automobilių gamintojai gali užblokuoti duomenis ar aktyviai neleisti įkrauti. Nerekomenduojama! Naudokite tik pilnai suprasdami riziką.", "pollIntervalHelp": "Laikas tarp transporto priemonės API atnaujinimų. Trumpi intervalai gali iškrauti transporto priemonės akumuliatorių.", "pollIntervalLabel": "<PERSON><PERSON><PERSON><PERSON>", "pollModeAlways": "nuolat", "pollModeAlwaysHelp": "Nuolat reguliariais intervalais reikalauti būsenos atnaujinimų.", "pollModeCharging": "įkraunant", "pollModeChargingHelp": "Tik įkraunant reikalauti automobilio būsenos atnaujinimų.", "pollModeConnected": "kai prijungtas", "pollModeConnectedHelp": "Reguliariai atnaujinti automobilio būseną kai prijungtas.", "pollModeLabel": "Naujinim<PERSON> elgsena", "priorityHelp": "Aukštesnis prioritetas gauna pirmenybę naudoti saulės energijos perteklių.", "priorityLabel": "Pirmenybė", "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "showAllSettings": "<PERSON><PERSON><PERSON> visus nustatymus", "solarBehaviorCustomHelp": "Apibrėžkite savo įjungimo ir išjungimo slen<PERSON> ir del<PERSON>.", "solarBehaviorDefaultHelp": "<PERSON><PERSON><PERSON><PERSON> po {enableDelay} esant pertek<PERSON>ui. <PERSON><PERSON><PERSON>, kai nepa<PERSON>ka pertek<PERSON> {disableDelay}.", "solarBehaviorLabel": "<PERSON><PERSON><PERSON>", "solarModeCustom": "pritaik<PERSON><PERSON>", "solarModeMaximum": "<PERSON><PERSON><PERSON><PERSON>", "thresholdDisableDelayLabel": "Išjungimo <PERSON>", "thresholdDisableHelpInvalid": "Prašome naudoti teigiamą vertę.", "thresholdDisableHelpPositive": "<PERSON><PERSON><PERSON>, kai iš tinklo il<PERSON>u nei {delay} naudojama daugiau nei {power}.", "thresholdDisableHelpZero": "<PERSON><PERSON><PERSON>, kai minimalios reikalingos galios trūksta ilgiau nei {delay}.", "thresholdDisableLabel": "Tinklo galia išjungimui", "thresholdEnableDelayLabel": "Įjungimo delsa", "thresholdEnableHelpInvalid": "Prašome naudoti neigiamą reikšmę.", "thresholdEnableHelpNegative": "<PERSON><PERSON><PERSON><PERSON>, kai {surplus} perte<PERSON><PERSON> yra ilgiau nei {delay}.", "thresholdEnableHelpZero": "<PERSON><PERSON><PERSON><PERSON> po {delay} esant minimaliam reikalingam energijos pertekliui.", "thresholdEnableLabel": "Tinklo galia įjungimui", "titleAdd": {"charging": "Pridėti Įkrovimo Vietą", "heating": "Pridėti Šildymo Prietai<PERSON>ą", "unknown": "Pridėti Įkroviklį arba Šildytuvą"}, "titleEdit": {"charging": "Redaguoti Įkroviklį", "heating": "Redaguoti Šildymo Prietaisą", "unknown": "Redaguoti Įkroviklį arba Šildytuvą"}, "titleExample": {"charging": "<PERSON><PERSON><PERSON><PERSON>, Aikštelė ir pan.", "heating": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>."}, "titleLabel": "Pavadinimas", "vehicleAutoDetection": "automatinis automobilio aptikimas", "vehicleHelpAutoDetection": "Automatiškai parenka labiausiai tikėtiną automobilį. <PERSON><PERSON><PERSON> rank<PERSON> k<PERSON>.", "vehicleHelpDefault": "<PERSON><PERSON>, kad <PERSON>ia įkraunamas šis automobilis. Automatinis aptikimas išjungtas. Galimas rankinis koregavimas.", "vehicleLabel": "Numatytasis automobilis", "vehiclesTitle": "Automobiliai"}, "main": {"addAdditional": "Prid<PERSON>ti papildomą skaitiklį", "addGrid": "<PERSON><PERSON><PERSON><PERSON> skaitiklį", "addLoadpoint": "Pridėti įkroviklį arba šildytuvą", "addPvBattery": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>s elektrinę arba kaupiklį", "addTariffs": "<PERSON><PERSON><PERSON><PERSON>", "addVehicle": "<PERSON><PERSON><PERSON><PERSON> automobilį", "configured": "sukonfigūruota", "edit": "<PERSON><PERSON><PERSON><PERSON>", "loadpointRequired": "<PERSON><PERSON> b<PERSON><PERSON> su<PERSON> bent vienas įkroviklis.", "name": "Vardas", "title": "Konfigūracija", "unconfigured": "nesukonfigūruota", "vehicles": "Mano automobiliai", "yaml": "Įtaisas iš evcc.yaml , čia konfi<PERSON>oti neina."}, "messaging": {"description": "Gaukite pranešimus apie įkrovimo sesijas.", "title": "Pranešimai"}, "meter": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "<PERSON><PERSON><PERSON><PERSON> integra<PERSON>", "option": {"aux": "Pridėti savireguliuojantį vartotoją", "battery": "<PERSON><PERSON><PERSON><PERSON> skaitiklį", "ext": "<PERSON><PERSON><PERSON><PERSON> išorinį skaitiklį", "pv": "<PERSON><PERSON><PERSON><PERSON> skaitiklį"}, "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "specific": "Specifin<PERSON><PERSON> in<PERSON>gra<PERSON>", "template": "Gamintojas", "titleChoice": "Ką norite prid<PERSON>ti?", "validateSave": "Pat<PERSON><PERSON><PERSON> ir <PERSON>"}, "modbus": {"baudrate": "Duomenų perdavimo greitis", "comset": "ComSet tipas", "connection": "Modbus jung<PERSON>", "connectionHintSerial": "Įrenginys prijungtas prie evcc tiesiai per RS485.", "connectionHintTcpip": "evcc pasiekia įrenginį per LAN/Wifi.", "connectionValueSerial": "Serijinis portas / USB", "connectionValueTcpip": "Tin<PERSON><PERSON>", "device": "Įrenginio pavadinimas", "deviceHint": "Pvz: /dev/ttyUSB0", "host": "IP adresas ar hostname", "hostHint": "Pvz: *********", "id": "Modbus'o ID", "port": "Portas", "protocol": "Modbus'o protokolas", "protocolHintRtu": "Prisijungimas per RS485 - Ethernet adapterį be protokolo keitimo.", "protocolHintTcp": "Įrenginys turi LAN / Wi-Fi arba yra prijungtas per RS485 - Ethernet adapterį su protokolo keitimu.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Leisti keliems klientams pasiekti vieną „Modbus“ įrenginį.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Autentifikavimas", "description": "Prisijunkite prie MQTT brokerio, kad k<PERSON><PERSON> duomenimis su kitomis tinklo sistemo<PERSON>.", "descriptionClientId": "Pranešimų autorius. <PERSON><PERSON> naudo<PERSON> tu<PERSON> `evcc-[rand]` .", "descriptionTopic": "Palikite tuščią, kad išjungtumėte publikavimą.", "labelBroker": "Broker", "labelCaCert": "<PERSON><PERSON> (CA)", "labelCheckInsecure": "Le<PERSON>i savarankiškai pasira<PERSON><PERSON><PERSON> sertifikatus", "labelClientCert": "Kliento sertif<PERSON>", "labelClientId": "Kliento ID", "labelClientKey": "Kliento raktas", "labelInsecure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelTopic": "<PERSON><PERSON>", "labelUser": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "publishing": "<PERSON><PERSON><PERSON><PERSON>", "title": "MQTT"}, "network": {"descriptionHost": "Naudokite .local priesagą, kad įgalintumėte mDNS. Tai svarbu norint aptikti progrmėlę mobiliesiems ir kai kuriuos OCPP įkroviklius.", "descriptionPort": "Žiniatinklio sąsajos ir API prievadas. Jei tai pakeisite, turėsite atnaujinti naršyklės URL.", "descriptionSchema": "Turi įtakos tik URL generavimui. Pasirinkus HTTPS šifravimas nebus įjungtas.", "labelHost": "Hostname", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Tin<PERSON><PERSON>"}, "options": {"boolean": {"no": "ne", "yes": "taip"}, "endianness": {"big": "mažėjantys <PERSON>", "little": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "operationMode": {"heating": "<PERSON><PERSON><PERSON><PERSON>", "standby": "Budėji<PERSON>"}, "schema": {"http": "HTTP (nešifruotas)", "https": "HTTPS (užšifruotas)"}, "status": {"A": "A (neprijungtas)", "B": "B (prijungtas)", "C": "C (įkrauna)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON> skaitiklį", "titleEdit": "Redaguoti sa<PERSON>ės skaitiklį"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON><PERSON>", "general": "Pag<PERSON><PERSON><PERSON>i", "grid": "Elektros tinklas", "integrations": "Integracijos", "loadpoints": "Įkrovimas ir <PERSON>", "meter": "<PERSON><PERSON><PERSON> i<PERSON>", "system": "Sistema", "vehicles": "Automobiliai"}, "sponsor": {"addToken": "Įveskite žetoną", "changeToken": "Pakeisti žetoną", "description": "Rėmimo modelis padeda mums iš<PERSON>kyti projektą ir tvariai kurti naujas, įdomias funkcijas. <PERSON>p rėmėjas jūs gaunate prieigą prie visų įkroviklių implementacijų.", "descriptionToken": "<PERSON><PERSON><PERSON> (token) gaunate iš {url}. <PERSON><PERSON> pat <PERSON> bandomąjį žetoną testavimui.", "error": "<PERSON><PERSON><PERSON><PERSON><PERSON> (token) negalioja.", "labelToken": "Rėmėjo <PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "tokenRequired": "<PERSON><PERSON>š konfigūruodami šį įrenginį turite sukonfigūruoti rėmėjo žetoną.", "tokenRequiredLearnMore": "<PERSON><PERSON><PERSON><PERSON>ug<PERSON>.", "tokenRequiredShort": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> (token).", "trialToken": "<PERSON><PERSON><PERSON>"}, "system": {"backupRestore": {"backup": {"action": "Atsisiųsti atsarginę kopiją...", "confirmationButton": "Atsisiųsti atsarginę kopiją", "confirmationText": "Norėdami atsisiųsti duomenų bazės failą, įveskite slaptažodį.", "description": "Sukurkite duomenų atsarginę kopiją faile. Šis failas gali būti naudojamas duomenims atkurti sistemos gedimo atveju.", "title": "Atsarginė kopija"}, "cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirmWithPassword": "<PERSON><PERSON><PERSON><PERSON>", "description": "Duomenų atsarginių kopij<PERSON> kūrima<PERSON>, atkūrimas ir nustatymas iš naujo. Patogu ir kai norite perkelti duomenis į kitą sistemą.", "note": "Pastaba: Visi aukščiau išvardyti veiksmai paveikia tik jūsų duomenų bazės duomenis. Konfigūracijos failas evcc.yaml lieka nepakitęs.", "reset": {"action": "Atstatyti...", "confirmationButton": "Atstatyti ir paleisti iš naujo", "confirmationText": "Tai visam laikui ištrins jūsų pasirinktus duomenis. Pirmiausia įsitikinkite, kad atsisiuntėte atsarginę kopiją.", "description": "Kyla problemų dėl konfigūracijos ir norite pradėti iš naujo? Ištrinkite visus duomenis ir pradėkite iš naujo.", "sessions": "Įkrovimo sesijos", "sessionsDescription": "Ištrina įkrovimo sesijų istoriją.", "settings": "Konfigūracija ir nustatymai", "settingsDescription": "<PERSON><PERSON><PERSON><PERSON> visus sukonfigūruotus įrenginius, paslaugas, planus, talpyklas ir kt.", "title": "Atstaty<PERSON>"}, "restore": {"action": "Atkurti...", "confirmationButton": "Atkurti ir paleisti iš naujo", "confirmationText": "Tai <PERSON>š<PERSON> visą jūsų duomenų bazę. Pirmiausia įsitikinkite, kad atsisiuntėte atsarginę kopiją.", "description": "Atkurkite duomenis iš <PERSON> kop<PERSON>. <PERSON> per<PERSON> visus dabartinius duomenis.", "labelFile": "Atsargin<PERSON><PERSON> kop<PERSON>", "title": "Atkurti"}, "title": "Atsarginių kopijų kūrimas ir atkūrimas"}, "logs": "Žurnalai", "restart": "Paleisti iš naujo", "restartRequiredDescription": "Paleiskite i<PERSON>, kad įsigaliotų pake<PERSON>i.", "restartRequiredMessage": "Nustatymai pasikeitė.", "restartingDescription": "Palau<PERSON><PERSON>…", "restartingMessage": "Paleidžiama iš naujo."}, "tariffs": {"description": "Nustatykite savo energijos tarifu<PERSON>, kad a<PERSON><PERSON>otum<PERSON> įkrovimo sesijų iš<PERSON>.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Rodoma pagrindiniame ekrane ir nar<PERSON><PERSON><PERSON><PERSON> k<PERSON>.", "label": "Pavadinimas", "title": "Redaguot<PERSON> pavadin<PERSON>"}, "validation": {"failed": "<PERSON><PERSON><PERSON><PERSON>", "label": "Statusas", "running": "tikrinama…", "success": "sėkmingai", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "validate": "t<PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "chargingSettings": "Įkrovimo nustatymai", "defaultMode": "Numaty<PERSON><PERSON>", "defaultModeHelp": "Įkrovimo režimas prijungiant automobilį.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "Kitos integracijos", "identifiers": "RFID identifikatoriai", "identifiersHelp": "RFID identifikatorių, skirtų transporto priemonei identifikuoti, <PERSON><PERSON><PERSON><PERSON><PERSON>. Vienas įrašas eilutėje. Dabartinį identifikatorių galite rasti atitinkamo įkroviklio apžvalgos puslapyje.", "maximumCurrent": "Didžiausia srovė", "maximumCurrentHelp": "<PERSON><PERSON> <PERSON><PERSON><PERSON> did<PERSON> nei minimali s<PERSON>ė.", "maximumPhases": "<PERSON><PERSON><PERSON>lus fazi<PERSON> s<PERSON>", "maximumPhasesHelp": "Kiek fazių įkrovimui gali panaudoti šis automobilis? Naudojama norint apskaičiuoti reikiamą minimalų saulės energijos perteklių ir planuoti trukmę.", "minimumCurrent": "Minimali srovė", "minimumCurrentHelp": "Ne mažiau 6A. Nebent tiksliai žinote, k<PERSON> da<PERSON>.", "online": "Automobiliai su internetiniu API", "primary": "<PERSON><PERSON><PERSON><PERSON> integra<PERSON>", "priority": "<PERSON><PERSON><PERSON><PERSON>", "priorityHelp": "Didesnis prioritetas reiškia, kad šiam automobiliui suteikiama pirmenybė saulės energijos pertek<PERSON>ui.", "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "scooter": "<PERSON><PERSON><PERSON>", "template": "Gamintojas", "titleAdd": "<PERSON><PERSON><PERSON><PERSON> Automobilį", "titleEdit": "Koreguoti Automobilį", "validateSave": "Pat<PERSON><PERSON><PERSON> ir <PERSON>"}}, "footer": {"community": {"greenEnergy": "<PERSON><PERSON><PERSON> energi<PERSON>", "greenEnergySub1": "įkrauta su evcc", "greenEnergySub2": "nuo 2022 Spalio", "greenShare": "<PERSON><PERSON><PERSON>", "greenShareSub1": "galios tie<PERSON> ir", "greenShareSub2": "ener<PERSON><PERSON>", "power": "Įkrovimo galia", "powerSub1": "{activeClients} iš {totalClients} dalyvi<PERSON>", "powerSub2": "įkrauna…", "tabTitle": "Bendruome<PERSON><PERSON><PERSON>"}, "savings": {"co2Saved": "{value} ne<PERSON><PERSON><PERSON><PERSON>", "co2Title": "CO₂ Išmetimai", "configurePriceCo2": "Išmokite sukonfigūruoti kainą ir CO₂ duomenis.", "footerLong": "{percent} sa<PERSON><PERSON><PERSON> energija", "footerShort": "{percent} saul<PERSON>s", "modalTitle": "Įkrovimo energi<PERSON> a<PERSON>", "moneySaved": "{value} sutaupyta", "percentGrid": "{grid} kWh tinklo", "percentSelf": "{self} <PERSON>h saul<PERSON>s", "percentTitle": "<PERSON><PERSON><PERSON>", "period": {"30d": "paskutinės 30 dienų", "365d": "paskutinės 365 dienos", "thisYear": "<PERSON><PERSON><PERSON> metais", "total": "<PERSON><PERSON>"}, "periodLabel": "Laikotarpis:", "priceTitle": "<PERSON><PERSON><PERSON><PERSON>", "referenceGrid": "<PERSON><PERSON><PERSON>", "referenceLabel": "Atskaitos duomenys:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "Tapkite rėmėju", "becomeSponsorExtended": "Paremkite mus tiesiogiai ir gaukite lipdukų.", "confetti": "Norite konfeti?", "confettiPromise": "Gausite lipdukų ir skaitmeninių konfeti", "sticker": "… ar evcc lipduk<PERSON>?", "supportUs": "<PERSON><PERSON><PERSON><PERSON> misi<PERSON> - <PERSON><PERSON><PERSON>, kad įkrovimas saulės energija taptų įprastu. Padėkite mums ir paremkite evcc.", "thanks": "<PERSON><PERSON><PERSON><PERSON>, {sponsor}! <PERSON><PERSON><PERSON> prisidedate prie evcc vystymo.", "titleNoSponsor": "Paremkite mus", "titleSponsor": "Remiate projektą", "titleTrial": "<PERSON><PERSON><PERSON>", "titleVictron": "Rėmėjas - Victron Energy", "trial": "<PERSON><PERSON>s naudojate bandomąjį režimą ir galite naudoti visas funkcijas. Apsvarstykite galimybę paremti projektą.", "victron": "Naudojate evcc Victron Energy aparatinę įrangą ir turite prieigą prie visų funkcijų."}, "telemetry": {"optIn": "<PERSON><PERSON> p<PERSON> savo duomenimis.", "optInMoreDetails": "Daugiau informacijos rasite {0}.", "optInMoreDetailsLink": "čia", "optInSponsorship": "Gali tik rėmėjai."}, "version": {"availableLong": "yra nau<PERSON> versija", "modalCancel": "<PERSON><PERSON><PERSON><PERSON>", "modalDownload": "Atsisiųsti", "modalInstalledVersion": "Instaliuota versija", "modalNoReleaseNotes": "<PERSON><PERSON><PERSON><PERSON> past<PERSON> nėra. Daugiau informacijos apie naują versiją:", "modalTitle": "<PERSON><PERSON> na<PERSON> versija", "modalUpdate": "Instaliuoti", "modalUpdateNow": "Instaliuoti dabar", "modalUpdateStarted": "Startuoja nauja evcc versija…", "modalUpdateStatusStart": "Instaliavimas pradėtas:"}}, "forecast": {"co2": {"average": "<PERSON><PERSON><PERSON><PERSON>", "lowestHour": "Švariausia valanda", "range": "Diapazonas"}, "modalTitle": "Prognozė", "price": {"average": "<PERSON><PERSON><PERSON><PERSON>", "lowestHour": "Pigiausia valanda", "range": "Diapazonas"}, "solar": {"dayAfterTomorrow": "<PERSON><PERSON><PERSON>", "partly": "<PERSON><PERSON><PERSON>", "remaining": "likę", "today": "Šiandien", "tomorrow": "<PERSON><PERSON><PERSON><PERSON>"}, "solarAdjust": "Pakoreguoti saulės prognozę remiantis faktiškais gamybos duomenimis{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}}, "header": {"about": "<PERSON><PERSON>", "authProviders": {"confirmLogout": "Ar tikrai norite atjungti {title}?", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "blog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docs": "Dokumentacija (Vokiečių k.)", "github": "GitHub", "login": "Automobilių prisijungimai", "logout": "<PERSON>si<PERSON><PERSON><PERSON>", "nativeSettings": "<PERSON><PERSON><PERSON><PERSON> Serverį", "needHelp": "<PERSON><PERSON><PERSON>?", "sessions": "Įkrovimo Sesijos"}, "help": {"discussionsButton": "GitHub diskusijos", "documentationButton": "Dokumentacija", "issueButton": "Pranešti apie klaidą", "issueDescription": "Pastebėjote keistą ar netinkamą veikimą?", "logsButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logsDescription": "Patikrinkite ar žurnaluose nėra klaidų.", "modalTitle": "<PERSON><PERSON>a paga<PERSON>?", "primaryActions": "<PERSON><PERSON><PERSON> veikia ne taip, kaip tur<PERSON>? Pagalbos ieškokite čia.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>!", "description": "Įprastai restartavimas neturėtų būti reika<PERSON>. Jei jums vis reikia restartuoti, p<PERSON><PERSON>stykite, gal derėtų pranešti apie problemą.", "disclaimer": "Pastaba: evcc sustos, ir pasiti<PERSON>s operacinės sistemos pagalba paleidžiant iš naujo.", "modalTitle": "Ar tikrai norite <PERSON>?"}, "restartButton": "<PERSON><PERSON><PERSON><PERSON>", "restartDescription": "Ar paband<PERSON>te išjungti ir vėl įjungti?", "secondaryActions": "Vis dar nepavyksta išspręsti problemos? Čia rasite drastiškesnes opcijas."}, "log": {"areaLabel": "<PERSON>ltru<PERSON>i pagal sritis", "areas": "Visos sritys", "download": "Atsisiųsti visą žurnalą", "levelLabel": "Filtruoti pagal žurnalo lygį", "nAreas": "{count} sritys", "noResults": "Nėra atitinkančių žurnalo įrašų.", "search": "Ieškoti", "selectAll": "pasirinkti visus", "showAll": "<PERSON><PERSON><PERSON> visus įrašus", "title": "Žurnalai", "update": "Automatinis <PERSON>"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "demoMode": "Demo režime prisijungti negalima.", "error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: ", "iframeHint": "Atidaryti evcc naujame <PERSON>.", "iframeIssue": "<PERSON><PERSON><PERSON><PERSON> slap<PERSON>, bet atrodo, kad jū<PERSON><PERSON> nar<PERSON><PERSON><PERSON> at<PERSON>akė autentifikavimo slapuko. <PERSON><PERSON> gal<PERSON>, jei pale<PERSON> evcc per iframe naudojant HTTP.", "invalid": "<PERSON>eising<PERSON>.", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "reset": "Atstatyti slaptažodį?", "title": "Autentifikavimas"}, "main": {"chargingPlan": {"active": "Aktyvus", "addRepeatingPlan": "Pridėti pasikartojantį įkrovimo planą", "arrivalTab": "Atvykimas", "day": "<PERSON><PERSON>", "departureTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goal": "Įkrovimo tikslas", "modalTitle": "Įkrovimo planas", "none": "nėra", "planNumber": "Planas {number}", "preconditionDescription": "Įkrovimas {duration} p<PERSON><PERSON>, bat<PERSON><PERSON>.", "preconditionLong": "<PERSON><PERSON><PERSON><PERSON> įkrovimas", "preconditionOptionAll": "viskas", "preconditionOptionNo": "ne", "preconditionShort": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "repeating": "ka<PERSON><PERSON><PERSON>", "repeatingPlans": "Pasikart<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON> visus", "time": "<PERSON><PERSON>", "title": "Planas", "titleMinSoc": "Minimali įkrova", "titleTargetCharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsavedChanges": "Yra neišsaugotų pakeitimų. Išsaugoti dabar?", "update": "<PERSON><PERSON><PERSON><PERSON>", "weekdays": "<PERSON><PERSON>"}, "energyflow": {"battery": "<PERSON><PERSON><PERSON><PERSON>", "batteryCharge": "<PERSON><PERSON><PERSON><PERSON> įkraunamas", "batteryDischarge": "<PERSON><PERSON><PERSON><PERSON>", "batteryGridChargeActive": "įkraunama iš tinklo", "batteryGridChargeLimit": "įkrauti iš tin<PERSON>, kai", "batteryHold": "<PERSON><PERSON><PERSON><PERSON> (užblokuotas)", "batteryTooltip": "{energy} iš {total} ({soc})", "forecastTooltip": "prognozė: likusi sa<PERSON>s energijos gamyba šiandien", "gridImport": "<PERSON><PERSON>", "homePower": "<PERSON><PERSON>", "loadpoints": "Įkroviklis | Įkroviklis | {count} įkrovikliai", "noEnergy": "Nėra skaitiklių duomenų", "pv": "<PERSON><PERSON><PERSON>", "pvExport": "Tinklo eksportas", "pvProduction": "Gamyba", "selfConsumption": "<PERSON><PERSON><PERSON><PERSON>"}, "heatingStatus": {"charging": "Š<PERSON><PERSON><PERSON>…", "connected": "Bud<PERSON><PERSON><PERSON>.", "vehicleLimit": "Šil<PERSON><PERSON>", "waitForVehicle": "Paruošta. Laukiame šildytuvo…"}, "loadpoint": {"avgPrice": "⌀ <PERSON>na", "charged": "Įkrauta", "co2": "⌀ CO₂", "duration": "Trukmė", "fallbackName": "Įkroviklis", "finished": "Bus baigta", "power": "Galia", "price": "<PERSON><PERSON>", "remaining": "Liko", "remoteDisabledHard": "{source}: <PERSON><PERSON><PERSON><PERSON><PERSON>", "remoteDisabledSoft": "{source}: <PERSON><PERSON><PERSON><PERSON> įkrovimas iš<PERSON>as", "solar": "<PERSON><PERSON><PERSON>"}, "loadpointSettings": {"batteryBoost": {"description": "Greitas įkrovimas iš namų kaupik<PERSON>.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> paspartintas įkrovimas", "mode": "<PERSON><PERSON><PERSON> tik <PERSON> ir <PERSON>+<PERSON><PERSON> režim<PERSON>se.", "once": "Paspartinimas aktyvuotas šiai įkrovimo sesijai."}, "batteryUsage": "<PERSON><PERSON>", "currents": "Įkrovimo Srovė", "default": "standartiškai", "disclaimerHint": "Pastaba:", "limitSoc": {"description": "Įkrovimo limitas, naudoja<PERSON>, prijungus šį automobilį.", "label": "Standartinis limitas"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Automobilis įkraunamas „Gre<PERSON>i” iki {0} nustatyme „Saulė”, toliau įkraunamas tik saulės energijos pertekliumi. Padeda užtikrinti minimalią įkrovą dienomis, kai mažai saul<PERSON>.", "label": "Minimali įkrova %"}, "onlyForSocBasedCharging": "<PERSON><PERSON> p<PERSON>ai galimi tik automobiliams su žinomu įkrovos lygiu.", "phasesConfigured": {"label": "Fazė<PERSON>", "no1p3pSupport": "<PERSON><PERSON> prijungtas jūsų įkroviklis?", "phases_0": "automatinis perjungimas", "phases_1": "1 fazė", "phases_1_hint": "({min} iki {max})", "phases_3": "3 fazės", "phases_3_hint": "({min} iki {max})"}, "smartCostCheap": "Įkrovimas pigiai iš tinklo", "smartCostClean": "Įkrovimas švariai iš tinklo", "title": "<PERSON>ustatymai {0}", "vehicle": "Automobilis"}, "mode": {"minpv": "Min+<PERSON>ė", "now": "G<PERSON><PERSON>", "off": "Stop", "pv": "<PERSON>", "smart": "<PERSON><PERSON><PERSON><PERSON>"}, "provider": {"login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON>si<PERSON><PERSON><PERSON>"}, "startConfiguration": "Pradėkime konfigūruoti", "targetCharge": {"activate": "Aktyvuoti", "co2Limit": "CO₂ riba iš {co2}", "costLimitIgnore": "Šiuo laikotarpiu sukonfigūruotas {limit} bus ignoruojamas.", "currentPlan": "Planas aktyvus", "descriptionEnergy": "<PERSON><PERSON> kada {targetEnergy} tur<PERSON>t<PERSON> būti įkrauta į automobilį?", "descriptionSoc": "Kada automobilis turėtų būti įkrautas iki {targetSoc}?", "goalReached": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "inactiveLabel": "Suplanuotas laikas", "nextPlan": "Kitas planas", "notReachableInTime": "Tikslas bus pasiektas {overrun} vėliau.", "onlyInPvMode": "Įkrovimo planas veikia tik nustatyme Saulė.", "planDuration": "Įkrovimo laikas", "planPeriodLabel": "Periodas", "planPeriodValue": "{start} iki {end}", "planUnknown": "dar <PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceLimit": "kainos limitas {price}", "remove": "<PERSON><PERSON><PERSON><PERSON>", "setTargetTime": "nesuplanuotas", "targetIsAboveLimit": "Sukonfigūruotas įkrovimo limitas {limit} šiuo periodu bus ignoruojamas.", "targetIsAboveVehicleLimit": "Automobilyje nustatytas limitas yra mažesnis už įkrovimo tikslą.", "targetIsInThePast": "Pasirinkite laik<PERSON> ate<PERSON>.", "targetIsTooFarInTheFuture": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kai tik gausime naujų duomenų.", "title": "Planinis Įkrovimas", "today": "šiandien", "tomorrow": "rytoj", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip tai sukonfigū<PERSON>oti.", "vehicleCapacityRequired": "<PERSON><PERSON> n<PERSON> įkrovimo trukm<PERSON>, reikalinga automobilio baterijos talpa."}, "targetChargePlan": {"chargeDuration": "Įkrovimo trukmė", "co2Label": "CO₂ emisijos ⌀", "priceLabel": "<PERSON><PERSON><PERSON><PERSON> kaina", "timeRange": "{day} {range} val", "unknownPrice": "dar <PERSON><PERSON><PERSON><PERSON>"}, "targetEnergy": {"label": "Limitas", "noLimit": "nėra"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON><PERSON> automobilį", "changeVehicle": "<PERSON><PERSON><PERSON><PERSON> automobilį", "detectionActive": "Bandome atpažinti automobilį…", "fallbackName": "Automobilis", "moreActions": "Daugiau ve<PERSON>", "none": "Nėra automobilio", "notReachable": "Automobilis nepasiekiamas. Pabandykite restartuoti evcc.", "targetSoc": "Limitas", "temp": "t.", "tempLimit": "Temp. limitas", "unknown": "Nežinomas automobilis", "vehicleSoc": "Baterija"}, "vehicleStatus": {"awaitingAuthorization": "Laukiama autorizacijos.", "batteryBoost": "<PERSON><PERSON><PERSON><PERSON> paspartin<PERSON> ka<PERSON>.", "charging": "Įkraunama…", "cheapEnergyCharging": "Šiuo metu energija yra pigi.", "cheapEnergyNextStart": "<PERSON>i energija už {duration}.", "cheapEnergySet": "Kainos riba nustatyta.", "cleanEnergyCharging": "Šiuo metu energija yra <PERSON>.", "cleanEnergyNextStart": "Švari energija už {duration}.", "cleanEnergySet": "CO₂ riba nustatyta.", "climating": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>nks<PERSON> k<PERSON>.", "connected": "Prijungtas.", "disconnectRequired": "Se<PERSON>ja <PERSON>. Prisijunkite iš naujo.", "disconnected": "Neprijungtas.", "feedinPriorityNextStart": "Auk<PERSON><PERSON> pardavimo kainos pras<PERSON>a už {duration}.", "feedinPriorityPausing": "Įkrovimas saulės energija pristab<PERSON>, tam, kad pasina<PERSON>ti aukšta energijos pardavimo kaina.", "finished": "Baigta.", "minCharge": "Minimalus įkrovimas iki {soc}.", "pvDisable": "Trūks<PERSON> pertekliaus. Pauzė netrukus.", "pvEnable": "<PERSON><PERSON><PERSON><PERSON><PERSON>, įkrovimas netrukus.", "scale1p": "Sumažinimas į vienfazį įkrovimą netrukus.", "scale3p": "Padidinimas į trifazį įkrovimą netrukus.", "targetChargeActive": "Įkrovimo planas aktyvuotas. Numatoma pabaiga už {duration}.", "targetChargePlanned": "Suplanuotas įkrovimas prasidės už {duration}.", "targetChargeWaitForVehicle": "Įkrovimas pagal plan<PERSON> le<PERSON>. Laukiama automobilio…", "vehicleLimit": "Automobilyje nustatytas limitas", "vehicleLimitReached": "Automobilyje nustatytas limitas pasiektas.", "waitForVehicle": "Paruošta. Laukiama automobilio…", "welcome": "<PERSON><PERSON> pirminis įkrovimas jungties patikrini<PERSON>i."}, "vehicles": "Autoparkas", "welcome": "Sveiki!"}, "notifications": {"dismissAll": "Išvalyti visus", "logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus žurnalus", "modalTitle": "Pranešimai"}, "offline": {"configurationError": "Klaida startuojant. Patikrinkite konfigūraciją ir restartuokite.", "message": "Nėra ryšio su serveriu.", "restart": "<PERSON><PERSON><PERSON><PERSON>", "restartNeeded": "<PERSON><PERSON><PERSON>, kad pakeitimai įsigaliotų.", "restarting": "<PERSON>is netrukus vėl veiks."}, "passwordModal": {"description": "Nustatykite slaptažodį konfigūracijos apsaugai. Pagrindiniu ekranu galima naudotis ir neprisijungus.", "empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> negali bū<PERSON> t<PERSON>", "error": "<PERSON>laid<PERSON>: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON>", "labelNew": "<PERSON><PERSON><PERSON>", "labelRepeat": "Pakartoti slaptažodį", "newPassword": "Sukurti slaptažodį", "noMatch": "Slaptažodžiai nesutampa", "titleNew": "Nustatyti Administratoriaus slaptažodį", "titleUpdate": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "updatePassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį"}, "session": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "Periodas", "delete": "<PERSON><PERSON><PERSON><PERSON>", "finished": "<PERSON><PERSON><PERSON><PERSON>", "meter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meterstart": "Skaitiklis pradžia", "meterstop": "Skaitiklis pabaiga", "odometer": "Odometras", "price": "<PERSON><PERSON>", "started": "<PERSON><PERSON><PERSON><PERSON>", "title": "Įkrovimo sesija"}, "sessions": {"avgPower": "⌀ Galia", "avgPrice": "⌀ <PERSON>na", "chargeDuration": "Trukmė", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Kaina {byGroup}", "byGroupLoadpoint": "pagal Įkroviklį", "byGroupVehicle": "pagal Automobilį", "energy": "Įkrauta Energija", "energyGrouped": "<PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON> Energi<PERSON>", "energyGroupedByGroup": "Energija {byGroup}", "energySubSolar": "{value} saul<PERSON>s", "energySubTotal": "{value} suma", "groupedCo2ByGroup": "CO₂-Kiekis {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON> {byGroup}", "historyCo2": "CO₂-Tarša", "historyCo2Sub": "{value} suma", "historyPrice": "Įkrovimo i<PERSON><PERSON>", "historyPriceSub": "{value} suma", "solar": "<PERSON><PERSON><PERSON> energijos dalis per metus", "solarByGroup": "<PERSON><PERSON><PERSON> energi<PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energija (kWh)", "chargeduration": "Trukmė", "co2perkwh": "CO₂/kWh", "created": "Sukurta", "finished": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Įkroviklis", "meterstart": "Skaitiklis pradžia (kWh)", "meterstop": "Skaitiklis pabaiga (kWh)", "odometer": "Odometras (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Kaina/kWh", "solarpercentage": "<PERSON><PERSON><PERSON> (%)", "vehicle": "Automobilis"}, "csvPeriod": "Atsisiųsti {period} CSV", "csvTotal": "Atsisiųsti visą CSV", "date": "Pradžia", "energy": "Įkrauta", "filter": {"allLoadpoints": "visi įkrovikliai", "allVehicles": "visi automobiliai", "filter": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "group": {"co2": "<PERSON><PERSON><PERSON>", "grid": "Tink<PERSON>", "price": "<PERSON><PERSON>", "self": "<PERSON><PERSON><PERSON>"}, "groupBy": {"loadpoint": "Įkroviklis", "none": "<PERSON><PERSON>", "vehicle": "Automobilis"}, "loadpoint": "Įkroviklis", "noData": "Šį mėnesį įkrovimų nebuvo.", "overview": "Apžvalga", "period": {"month": "M<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON>", "year": "Metai"}, "price": "<PERSON><PERSON>", "reallyDelete": "Ar tikrai norite ištrinti šią įkrovimo sesiją?", "showIndividualEntries": "Rodyti individualias sesijas", "solar": "<PERSON><PERSON><PERSON>", "title": "Įkrovimo Sesijos", "total": "<PERSON><PERSON>", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}, "vehicle": "Automobilis"}, "settings": {"fullscreen": {"enter": "Įjungti visą ekraną", "exit": "Išjungti visą ekraną", "label": "Visas ekranas"}, "hiddenFeatures": {"label": "Eksperimentinė", "value": "Rodyti eksperimentines funkcijas."}, "language": {"auto": "Automatiškai", "label": "Kalba"}, "sponsorToken": {"expires": "<PERSON><PERSON><PERSON><PERSON> rėmėjo žetonas baigsis {inXDays}. {getNewToken} ir i<PERSON><PERSON>ug<PERSON>te jį čia.", "getNew": "Gaukite naują", "hint": "Pastaba: ateityje šis procesas bus automatizuotas."}, "telemetry": {"label": "Telemetrija"}, "theme": {"auto": "sistemos", "dark": "tamsus", "label": "<PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>"}, "time": {"12h": "12 val.", "24h": "24 val.", "label": "Laiko formatas"}, "title": "Vartotojo sąsaja", "unit": {"km": "km", "label": "Vienetai", "mi": "mylios"}}, "smartCost": {"activeHours": "{active} iš {total}", "activeHoursLabel": "Aktyvumo valandos", "applyToAll": "<PERSON>ritaikyti visur?", "batteryDescription": "Įkrauna kaupiklį energija iš tin<PERSON>.", "cheapTitle": "<PERSON>us įkrovimas iš tinklo", "cleanTitle": "<PERSON><PERSON><PERSON> įkrovimas iš tinklo", "co2Label": "CO₂ emisijos", "co2Limit": "CO₂ limitas", "loadpointDescription": "Laikinai aktyvuoja greitą įkrovimą (<PERSON><PERSON><PERSON>).", "modalTitle": "Išmanaus tinklo įkrovimas", "none": "nėra", "priceLabel": "<PERSON><PERSON><PERSON><PERSON> kaina", "priceLimit": "Kainos limitas", "resetAction": "<PERSON><PERSON><PERSON><PERSON>", "resetWarning": "Nėra sukonfigūruotos dinami<PERSON>s tinklo kainos ar <PERSON>. Tačiau vis dar yra {limit} apribojimas. Išvalyti konfigūraciją?", "saved": "Išsaugota."}, "smartFeedInPriority": {"activeHoursLabel": "Prist<PERSON><PERSON><PERSON> valandomis", "description": "Pristabdo įkrovimą aukštos energijos kainos periodu, prioritetas - pelningam energijos pardavimui.", "priceLabel": "<PERSON><PERSON><PERSON><PERSON> kaina", "priceLimit": "<PERSON>rda<PERSON><PERSON> limitas", "resetWarning": "Kinta<PERSON> pardavimo tarifas nesukonfigūruotas. <PERSON><PERSON><PERSON><PERSON>, yra įvestas limitas {limit}. Išvalyti konfigūraciją?", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "startupError": {"configFile": "Naudoja<PERSON> kon<PERSON>gu<PERSON> failas:", "configuration": "Konfiguracija", "description": "Patikrinkite konfigūracijos failą. <PERSON><PERSON> k<PERSON> žin<PERSON> j<PERSON> ne<PERSON>, atsakymų ieškokite mūsų {0}.", "discussions": "GitHub <PERSON>", "fixAndRestart": "Pabandykite Ištaisyti klaidą ir perkrauti serverį.", "hint": "Pastaba: <PERSON><PERSON>, kad <PERSON> veikia įrenginiai (inverteris, skai<PERSON><PERSON><PERSON>, ...). Patikrinkite tiklo j<PERSON>.", "lineError": "<PERSON><PERSON><PERSON> {0}.", "lineErrorLink": "e<PERSON><PERSON><PERSON> {0}", "restartButton": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}}