{"batterySettings": {"batteryLevel": "Carga da bateria", "bufferStart": {"above": "quando acima {soc}.", "full": "quando estiver a {soc}.", "never": "apenas com excedente de produção suficiente."}, "capacity": "{energy} de {total}", "control": "Controlo da bateria", "discharge": "Prevenir a descarga no modo rápido e na carga planeada.", "disclaimerHint": "Nota:", "disclaimerText": "Estas configurações só afetam o modo solar. O comportamento de carregamento é ajustado de acordo.", "gridChargeTab": "Carregamento pela Rede", "legendBottomName": "“Dar prioridade ao carregamento da bateria da casa”", "legendBottomSubline": "até atingir {soc}.", "legendMiddleName": "“Dar prioridade ao carregamento do veículo”", "legendMiddleSubline": "quando a bateria doméstica está acima de {soc}.", "legendTopAutostart": "Iniciar automaticamente", "legendTopName": "Carregamento de veículos com a bateria", "legendTopSubline": "quando a bateria da casa está acima de {soc}.", "modalTitle": "Bateria da Casa", "usageTab": "Gestão da bateria"}, "config": {"aux": {"description": "Equipamento capaz de ajustar o seu consumo baseado no excedente disponível (como caldeiras AQS inteligentes). O evcc assume que esse equipamento reduz o seu consumo automaticamente, se necessário.", "titleAdd": "Adicionar Equipamento com capacidade de auto-ajuste de consumos", "titleEdit": "Editar Equipamento com capacidade de auto-ajuste de consumos"}, "battery": {"titleAdd": "Adicionar <PERSON>", "titleEdit": "Editar <PERSON>"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON>"}, "charger": {"chargers": "Carregadores de Veículos Eléctricos", "generic": "Integrações genéricas", "heatingdevices": "Equipamentos de aquecimento", "ocppHelp": "Copie este endereço para a configuração do seu carregador.", "ocppLabel": "URL do servidor OCPP", "switchsockets": "<PERSON><PERSON><PERSON> comanda<PERSON>", "template": "Fabricante", "titleAdd": {"charging": "<PERSON><PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "<PERSON><PERSON>"}}, "circuits": {"description": "Assegura que a soma de todos os Postos de Carregamento conectados a um circuito não excede os limites de potência e corrente configurados. Os circuitos podem ser combinados para construir uma hierarquia.", "title": "Gestão de Cargas"}, "control": {"description": "Normalmente funciona com os valores padrão. Mude-os apenas se sabe o que está a fazer.", "descriptionInterval": "Intervalo de atualização do loop de controlo em segundos. Define com que frequência o evcc lê dados do contador, ajusta o poder de carregamento e atualiza a interface do usuário. Intervalos curtos (< 30s) podem causar oscilações e comportamentos indesejados.", "descriptionResidualPower": "Altera o ponto de operação do circuito de controle. Se você tem uma bateria em casa, é recomendável definir um valor de 100 W. Assim, a bateria terá uma ligeira prioridade sobre o uso da Rede.", "labelInterval": "Intervalo das actualizações", "labelResidualPower": "Potência residual", "title": "Comportamento de controlo"}, "deviceValue": {"amount": "Quantidade", "broker": "Broker", "bucket": "Bucket", "capacity": "Capacidade", "chargeStatus": "Estado", "chargeStatusA": "Des<PERSON><PERSON>", "chargeStatusB": "ligado", "chargeStatusC": "a carregar", "chargeStatusE": "sem energia", "chargeStatusF": "erro", "chargedEnergy": "Carregado", "co2": "Rede CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON>", "controllable": "Controlável", "currency": "<PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON>", "currentRange": "<PERSON><PERSON><PERSON>", "enabled": "<PERSON><PERSON>do", "energy": "Energia", "feedinPrice": "Preço de injeção na rede", "gridPrice": "Preço da Rede", "heaterTempLimit": "Limite do aquecimento", "hemsType": "Sistema", "identifier": "Identificador RFID", "no": "não", "odometer": "Totalizador Km", "org": "Organização", "phaseCurrents": "Corrente L1, L2, L3", "phasePowers": "Energia L1, L2, L3", "phaseVoltages": "Voltagem L1, L2, L3", "phases1p3p": "Comutador de fases", "power": "Energia em tempo real", "powerRange": "Potência", "range": "Autonomia", "singlePhase": "Monofásico", "soc": "Carga", "solarForecast": "Previsão solar", "temp": "Temperatura", "topic": "Topic", "url": "URL", "vehicleLimitSoc": "Limite do veículo", "yes": "sim"}, "deviceValueChargeStatus": {"A": "A (não ligado)", "B": "B (ligado)", "C": "C (a carregar)"}, "devices": {"auxMeter": "“Consumidor inteligente”", "batteryStorage": "“Bateria”", "solarSystem": "Sistema fotovoltaico"}, "editor": {"loading": "A carregar editor YAML…"}, "eebus": {"description": "Configuração que permite o evcc comunicar com outros dispositivos EEBus.", "title": "EEBus"}, "ext": {"description": "Pode ser usado para gestão de cargas ou fins estatísticos.", "titleAdd": "<PERSON><PERSON><PERSON><PERSON> ex<PERSON>o", "titleEdit": "<PERSON><PERSON> ex<PERSON>o"}, "form": {"danger": "Perigo", "deprecated": "obsoleto", "example": "Exemplo", "optional": "opcional"}, "general": {"cancel": "<PERSON><PERSON><PERSON>", "customHelp": "Criar um dispositivo definido pelo utilizador utilizando o sistema de plugins do evcc.", "customOption": "Dispositivo definido pelo utilizador", "delete": "<PERSON><PERSON><PERSON>", "docsLink": "Ver documentação.", "experimental": "Experimental", "hideAdvancedSettings": "Ocultar definições avançadas", "off": "desligado", "on": "ligado", "password": "Palavra-passe", "readFromFile": "<PERSON><PERSON> <PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "save": "Guardar", "showAdvancedSettings": "Mostrar definições avançadas", "telemetry": "Telemetria", "templateLoading": "Carregando...", "title": "Nome", "validateSave": "Validar e guardar"}, "grid": {"title": "Contador de Rede", "titleAdd": "<PERSON><PERSON><PERSON><PERSON>e", "titleEdit": "Editar Con<PERSON>or <PERSON> Rede"}, "hems": {"description": "Conectar evcc a outro sistema de gestão de energia doméstica.", "title": "HEMS"}, "icon": {"change": "alterar"}, "influx": {"description": "Escreve dados de carregamento e outras métricas no InfluxDB. Use o Grafana ou outras ferramentas para visualizar os dados.", "descriptionToken": "Verifique a documentação do InfluxDB para aprender a criar uma. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Permitir certificados auto-assinados", "labelDatabase": "Base de dados", "labelInsecure": "Validação do Certificado", "labelOrg": "Organização", "labelPassword": "Palavra-passe", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Nome do utilizador", "title": "InfluxDB", "v1Support": "Necessita apoio para o InfluxDB 1.x?", "v2Support": "Voltar ao InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "<PERSON><PERSON><PERSON><PERSON>"}, "addMeter": "Adicionar um Contador de Carregador dedicado", "cancel": "<PERSON><PERSON><PERSON>", "chargerError": {"charging": "É necessário configurar um Carregador EV."}, "chargerLabel": {"charging": "Carregador"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Utilização de uma gama de correntes de 6 a 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Utilização de uma gama de correntes de 6 a 32 A.", "chargerPowerCustom": "outro", "chargerPowerCustomHelp": "Definir um intervalo de Correntes personalizado.", "chargerTypeLabel": "Tipo de Carregador", "chargingTitle": "<PERSON>", "circuitHelp": "Atribuição de gestão de carga para garantir que os limites de potência e corrente não são excedidos.", "circuitLabel": "Circuito", "circuitUnassigned": "não atribuído", "defaultModeHelp": {"charging": "Modo de carregamento ao ligar o veículo."}, "defaultModeHelpKeep": "Mantém o último modo de carregamento selecionado.", "defaultModeLabel": "Modo predefinido", "delete": "<PERSON><PERSON><PERSON>", "electricalSubtitle": "Em caso de dúvida, pergunte ao seu eletricista.", "electricalTitle": "Elétrico", "energyMeterHelp": "Contador adicional se o Carregador não tiver um integrado.", "energyMeterLabel": "Contador de energia", "estimateLabel": "Interpolar o nível de carga entre actualizações da API", "maxCurrentHelp": "Deve ser superior à Corrente mínima.", "maxCurrentLabel": "<PERSON><PERSON><PERSON>", "minCurrentHelp": "Defina apenas abaixo dos 6 A, se souber o que está a fazer.", "minCurrentLabel": "<PERSON><PERSON><PERSON>", "noVehicles": "<PERSON>enhum veículo configurado.", "phases1p": "Monofásico", "phases3p": "Trifásico", "phasesAutomatic": "Fases automáticas", "phasesAutomaticHelp": "Se o seu carregador suporta a comutação automática entre o carregamento de 1 e 3 fases. No menu principal, pode ajustar o comportamento das fases durante o carregamento.", "phasesHelp": "Número de fases ligadas ao carregador.", "phasesLabel": "Fases", "pollIntervalDanger": "Intervalos curtos de actualização das leituras podem descarregar a bateria do veículo. Alguns fabricantes podem impedir a carga nestes casos. Não recomendável! Apenas utilize esta opção se está consciente dos riscos.", "pollIntervalHelp": "Tempo entre as actualizações da API do veículo. Intervalos curtos podem descarregar a bateria do veículo.", "pollIntervalLabel": "Intervalo de atualização", "pollModeAlways": "sempre", "pollModeAlwaysHelp": "Solicite sempre actualizações de estado em intervalos regulares.", "pollModeCharging": "a carregar", "pollModeChargingHelp": "Solicitar actualizações do estado do veículo apenas durante o carregamento.", "pollModeConnected": "ligado", "pollModeConnectedHelp": "Atualizar o estado do veículo em intervalos regulares quando ligado.", "pollModeLabel": "Comportamento das atualizações", "priorityHelp": "Os Postos de Carregamento com maior prioridade têm acesso prioritário ao excedente solar.", "priorityLabel": "Prioridade", "save": "Guardar", "showAllSettings": "<PERSON><PERSON> to<PERSON> as defini<PERSON><PERSON><PERSON>", "solarBehaviorCustomHelp": "Defina os seus próprios limites e latências de ativação e desativação.", "solarBehaviorDefaultHelp": "Apenas carregar com excedente solar. Iniciar após {enableDelay} de excedente. Parar quando não houver excedente suficiente para {disableDelay}.", "solarBehaviorLabel": "Comportamento solar", "solarModeCustom": "personalizado", "solarModeMaximum": "máximo solar", "thresholdDisableDelayLabel": "Desativar atraso", "thresholdDisableHelpInvalid": "Por favor utilize um valor positivo.", "thresholdDisableHelpPositive": "<PERSON><PERSON><PERSON> o carregamento, quando for utilizada mais de {power} da Rede durante {delay}.", "thresholdDisableHelpZero": "Interromper quando a potência de carga mínima não for atingida durante {delay}.", "thresholdDisableLabel": "Desativar a energia da Rede", "thresholdEnableDelayLabel": "Ativar atraso", "thresholdEnableHelpInvalid": "Por favor utilize um valor negativo.", "thresholdEnableHelpNegative": "Iniciar o carregamento, quando {surplus} excedente estiver disponível durante {delay}.", "thresholdEnableHelpZero": "Iniciar quando o excedente de energia de carga mínima estiver disponível durante {delay}.", "thresholdEnableLabel": "Ativar a energia da Rede", "titleAdd": "Adicionar Posto de Carregamento", "titleEdit": "Editar Posto de Carregamento", "titleExample": "Garagem, Carport, etc.", "titleLabel": "Nome", "vehicleAutoDetection": "detecção automática", "vehicleHelpAutoDetection": "Seleciona automaticamente o veículo mais provável. É possível cancelar manualmente.", "vehicleHelpDefault": "Presumir sempre que este veículo está a carregar aqui. Detecção automática desativada. É possível a desativação manual.", "vehicleLabel": "<PERSON>e<PERSON><PERSON>lo prede<PERSON>", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "Adicionar <PERSON>tador adicional", "addGrid": "<PERSON><PERSON><PERSON><PERSON>e", "addLoadpoint": "Adicionar Posto de Carregamento", "addPvBattery": "Adicionar solar ou bateria", "addTariffs": "<PERSON><PERSON><PERSON><PERSON>(s)", "addVehicle": "<PERSON><PERSON><PERSON><PERSON>", "configured": "configurado", "edit": "editar", "loadpointRequired": "Tem de ser configurado pelo menos um ponto de carga.", "name": "Nome", "title": "Configuração", "unconfigured": "não configurado", "vehicles": "Os meus Veículos", "yaml": "Os dispositivos de evcc.yaml não são editáveis."}, "messaging": {"description": "Receber mensagens sobre as sessões de carregamento.", "title": "Notificações"}, "meter": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "generic": "Integrações genéricas", "option": {"aux": "Adiciona<PERSON> <PERSON>rregu<PERSON>", "battery": "<PERSON><PERSON><PERSON><PERSON>", "ext": "<PERSON><PERSON><PERSON><PERSON> ex<PERSON>o", "pv": "<PERSON><PERSON><PERSON><PERSON>"}, "save": "Guardar", "specific": "Integrações específicas", "template": "Fabricante", "titleChoice": "O que quer adicionar?", "validateSave": "Validar e guardar"}, "modbus": {"baudrate": "Baud rate", "comset": "ComSet", "connection": "Ligação Modbus", "connectionHintSerial": "O dispositivo está ligado diretamente ao evcc via RS485.", "connectionHintTcpip": "O dispositivo está acessível ao evcc via LAN/WIFI.", "connectionValueSerial": "Serial / USB", "connectionValueTcpip": "Rede Informática", "device": "Nome do dispositivo", "deviceHint": "Exemplo: /dev/ttyUSB0", "host": "Endereço IP ou hostname", "hostHint": "Exemplo: *********", "id": "Modbus ID", "port": "Porta", "protocol": "Protocolo Modbus", "protocolHintRtu": "Ligação através de adaptador RS485 / Ethernet sem tradução de protocolo.", "protocolHintTcp": "O dispositivo tem suporte LAN/WIFI nativo ou está ligado a um adaptador RS485 / Ethernet com tradução de protocolo.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Permit<PERSON> que vários clientes acedam ao mesmo dispositivo Modbus.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Autenticação", "description": "Conecte-se a um broker MQTT para trocar dados com outros sistemas da sua rede.", "descriptionClientId": "Autor das mensagens. Se o `evcc-[rand]` vazio é usado.", "descriptionTopic": "Deixe por preencher para desativar a publicação.", "labelBroker": "Broker", "labelCaCert": "Certificado do servidor (CA)", "labelCheckInsecure": "Permitir certificados auto-assinados", "labelClientCert": "Certificado de Cliente", "labelClientId": "ID do Cliente", "labelClientKey": "<PERSON><PERSON>", "labelInsecure": "Validação de Certificado", "labelPassword": "Palavra-passe", "labelTopic": "Topic", "labelUser": "Nome do utilizador", "publishing": "Publicação", "title": "MQTT"}, "network": {"descriptionHost": "Use sufixo .local para permitir mDNS. Importante para a descoberta do aplicativo móvel e alguns carregadores OCPP.", "descriptionPort": "Port para a interface web e API. É preciso atualizar o URL do navegador se mudar este parâmetro.", "descriptionSchema": "Apenas afeta como as URLs são geradas. Selecionar HTTPS não permitirá encriptação.", "labelHost": "Nome do anfitrião", "labelPort": "Porta", "labelSchema": "Esquema", "title": "Rede"}, "options": {"boolean": {"no": "não", "yes": "sim"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (não encriptado)", "https": "HTTPS (encriptado)"}, "status": {"A": "A (Desligado)", "B": "B (ligado)", "C": "C (<PERSON>)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON>"}, "section": {"additionalMeter": "Contadores adicionais", "general": "G<PERSON>", "grid": "Rede", "integrations": "Integrações", "loadpoints": "Postos de Carregamento", "meter": "Solar e Bateria", "system": "Sistema", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "Insira token", "changeToken": "Alterar token", "description": "O modelo de apoio (sponsoring) ajuda-nos a manter o projeto e a desenvolver, de forma sustentável, novas funcionalidades. Como apoiante (sponsor), terá acesso às integrações de todos os Carregadores.", "descriptionToken": "Você obtém o token de {url}. Também oferecemos um token de avaliação para teste.", "error": "O token de Apoiante (Sponsor) não é válido.", "labelToken": "<PERSON><PERSON> (Sponsor)", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenRequired": "Tem de configurar um token de Patrocínio (Sponsor) antes de poder criar este dispositivo.", "tokenRequiredLearnMore": "<PERSON>s informaçõ<PERSON>.", "trialToken": "Token de teste"}, "system": {"logs": "Registos", "restart": "Reiniciar", "restartRequiredDescription": "<PERSON>r favor reinicie para aplicar as alterações.", "restartRequiredMessage": "Configuração alterada.", "restartingDescription": "<PERSON>spere por favor…", "restartingMessage": "evcc a reiniciar."}, "tariffs": {"description": "<PERSON><PERSON><PERSON> as suas tarifas de energia para calcular os custos das suas sessões de carregamento.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Exibido no menu principal e na barra do navegador.", "label": "Nome", "title": "<PERSON><PERSON>"}, "validation": {"failed": "falhou", "label": "Status", "running": "a validar…", "success": "concluído com sucesso", "unknown": "desconhecido", "validate": "validar"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "chargingSettings": "Parâmetros de carga", "defaultMode": "Modo predefinido", "defaultModeHelp": "Modo de carga ao ligar o veículo.", "delete": "<PERSON><PERSON><PERSON>", "generic": "Outras integrações", "identifiers": "Identificadores RFID", "identifiersHelp": "Lista de registos RFID para identificar o veículo. Uma entrada por linha. O identificador atual pode ser encontrado no respetivo ponto de carga na página de resumo.", "maximumCurrent": "<PERSON><PERSON><PERSON>", "maximumCurrentHelp": "Deve ser superior à corrente mínima.", "maximumPhases": "Máximo de fases", "maximumPhasesHelp": "Com quantas fases pode carregar este veículo? A informação é utilizada para calcular o excedente solar mínimo necessário e a duração do plano.", "minimumCurrent": "<PERSON><PERSON><PERSON>", "minimumCurrentHelp": "Somente ir abaixo de 6A se souber o que está a fazer.", "online": "Veículos com API online", "primary": "Integrações genéricas", "priority": "Prioridade", "priorityHelp": "Uma prioridade mais elevada significa que este veículo tem acesso preferencial ao excedente solar.", "save": "Guardar", "scooter": "<PERSON>ooter", "template": "Fabricante", "titleAdd": "<PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON>", "validateSave": "Validar e guardar"}}, "footer": {"community": {"greenEnergy": "Solar", "greenEnergySub1": "carregado com evcc", "greenEnergySub2": "desde Outubro de 2022", "greenShare": "Parte solar", "greenShareSub1": "energia fornecida por", "greenShareSub2": "solar e bateria doméstica", "power": "Energia de carga", "powerSub1": "{activeClients} de {totalClients} utilizadores", "powerSub2": "a carregar…", "tabTitle": "Comunidade em tempo real"}, "savings": {"co2Saved": "{value} poupado", "co2Title": "Emissões de CO₂", "configurePriceCo2": "Saiba como configurar os dados de preço e CO₂.", "footerLong": "{percent} energia solar", "footerShort": "{percent} Sol", "modalTitle": "Dados da energia carregada", "moneySaved": "{value} poupado", "percentGrid": "{grid} kWh Rede", "percentSelf": "{self} kWh Sol", "percentTitle": "Energia solar", "period": {"30d": "últimos 30 dias", "365d": "últimos 365 dias", "thisYear": "este ano", "total": "total"}, "periodLabel": "Período:", "priceTitle": "Preço da energia", "referenceGrid": "Rede", "referenceLabel": "Dados de referência:", "tabTitle": "Os meus dados"}, "sponsor": {"becomeSponsor": "Torne-se um apoiante (Sponsor)", "becomeSponsorExtended": "Apoie-nos diretamente para receber autocolantes.", "confetti": "Pronto para os confetes?", "confettiPromise": "Receba autocolantes e confetes digitais", "sticker": "… ou autocolantes do evcc?", "supportUs": "A nossa missão é tornar a energia solar a norma. Ajude o evcc e contribua, pagando o que ele vale para si.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! A sua contribuição ajuda a desenvolver ainda mais o evcc.", "titleNoSponsor": "Apoie-nos", "titleSponsor": "Você é um Apoiante", "titleTrial": "<PERSON><PERSON> de teste", "titleVictron": "Patrocinado pela Victron Energy", "trial": "Você está no modo de teste e pode usar todos os recursos. Por favor, considere apoiar o projeto.", "victron": "Você está usando o evcc no hardware Victron Energy e tem acesso a todos os recursos."}, "telemetry": {"optIn": "Quero contribuir com meus dados.", "optInMoreDetails": "<PERSON><PERSON> de<PERSON> {0}.", "optInMoreDetailsLink": "aqui", "optInSponsorship": "<PERSON><PERSON> (Sponsoring)."}, "version": {"availableLong": "nova versão disponível", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "Download", "modalInstalledVersion": "Versão instalada", "modalNoReleaseNotes": "Sem notas da versão disponível. Mais informações sobre a nova versão:", "modalTitle": "Nova versão disponível", "modalUpdate": "Instalar", "modalUpdateNow": "Instalar agora", "modalUpdateStarted": "Iniciando a nova versão do evcc…", "modalUpdateStatusStart": "Instalação iniciada:"}}, "forecast": {"co2": {"average": "Média", "lowestHour": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON>\"", "range": "Intervalo"}, "modalTitle": "Previsões", "price": {"average": "Média", "lowestHour": "<PERSON><PERSON><PERSON><PERSON> mais barato", "range": "Intervalo"}, "solar": {"dayAfterTomorrow": "De<PERSON><PERSON> de aman<PERSON>", "partly": "parcialmente", "remaining": "restante", "today": "Hoje", "tomorrow": "Amanhã"}, "solarAdjust": "Ajustar a previsão solar com base em dados reais de produção{percent}.", "type": {"co2": "CO₂", "price": "Preço", "solar": "Solar"}}, "header": {"about": "Sobre", "blog": "Blog", "docs": "Documentação", "github": "GitHub", "login": "Registo de veículos", "logout": "Sair do sistema", "nativeSettings": "<PERSON><PERSON> o servidor", "needHelp": "Precisa de ajuda?", "sessions": "Sessões de carregamento"}, "help": {"discussionsButton": "Discussões no GitHub", "documentationButton": "Documentação", "issueButton": "Reportar um erro", "issueDescription": "Encontrou um comportamento estranho ou errado?", "logsButton": "Ver registos", "logsDescription": "Verifique os registos para erros.", "modalTitle": "Precisa de ajuda?", "primaryActions": "Alguma coisa não funciona como deveria? Pode procurar ajuda aqui:", "restart": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Sim, reiniciar!", "description": "Normalmente não é necessária a reinicialização. Considere registrar um erro se precisar de reiniciar o evcc regularmente.", "disclaimer": "Nota: o evcc será encerrado e dependerá do sistema operativo para reiniciar o serviço.", "modalTitle": "Tem a certeza que quer reiniciar?"}, "restartButton": "Reiniciar", "restartDescription": "Tentou desligar e ligar de novo?", "secondaryActions": "Ainda não conseguiu resolver o seu problema? Aqui estão algumas sugestões mais complexas."}, "log": {"areaLabel": "Filtrar por área", "areas": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "download": "Des<PERSON><PERSON><PERSON> o registo completo", "levelLabel": "Filtrar por nível de registo", "nAreas": "{count} <PERSON><PERSON><PERSON>", "noResults": "Sem entradas de registo correspondentes.", "search": "Procurar", "selectAll": "selecionar tudo", "showAll": "<PERSON><PERSON> todas as entradas", "title": "Registos", "update": "Atualização automática"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON>", "demoMode": "O login não é suportado no modo de demonstração.", "error": "Login falhou: ", "iframeHint": "Abrir evcc numa nova aba.", "iframeIssue": "A sua Palavra-passe está correta, mas o navegador parece ter perdido o cookie de autenticação. <PERSON><PERSON> pode acontecer se executar evcc num iframe via HTTP.", "invalid": "A Palavra-passe é inválida.", "login": "Entrar no sistema", "password": "Palavra-passe", "reset": "Repor Palavra-passe?", "title": "Autenticação"}, "main": {"chargingPlan": {"active": "Ativo", "addRepeatingPlan": "Adicionar planos iguais", "arrivalTab": "Fim", "day": "<PERSON>a", "departureTab": "<PERSON><PERSON>o", "goal": "Carga pretendida", "modalTitle": "Plano de carga", "none": "<PERSON><PERSON><PERSON>", "planNumber": "Plano {número}", "preconditionDescription": "Carregar {duration} antes da partida para pré-condicionamento da bateria.", "preconditionLong": "Carga tardia", "preconditionOptionAll": "tudo", "preconditionOptionNo": "não", "preconditionShort": "Tarde", "remove": "<PERSON><PERSON><PERSON>", "repeating": "recorrente", "repeatingPlans": "Repetição de planos", "selectAll": "Selecionar tudo", "time": "<PERSON><PERSON>", "title": "Plano", "titleMinSoc": "Carga mínima", "titleTargetCharge": "<PERSON><PERSON>o", "unsavedChanges": "Há alterações não salvas. Aplicar agora?", "update": "Aplicar", "weekdays": "<PERSON><PERSON>"}, "energyflow": {"battery": "Bateria", "batteryCharge": "Carga de Bateria", "batteryDischarge": "Descarga de Bateria", "batteryGridChargeActive": "carga da Rede ativa", "batteryGridChargeLimit": "carga da Rede quando", "batteryHold": "Bateria (suspensa)", "batteryTooltip": "{energy} de {total} ({soc})", "forecastTooltip": "previsão: produção solar restante de hoje", "gridImport": "<PERSON><PERSON><PERSON>e", "homePower": "Consu<PERSON>", "loadpoints": "Carregador | Carregador | {count} Carregadores", "noEnergy": "Sem dados", "pv": "Sistema fotovoltaico", "pvExport": "Injeção na Rede", "pvProduction": "Produção Solar", "selfConsumption": "Autoconsumo"}, "heatingStatus": {"charging": "A aquecer…", "connected": "Standby.", "vehicleLimit": "Limite do aquecedor", "waitForVehicle": "Pronto. À espera do aquecedor…"}, "loadpoint": {"avgPrice": "Preço", "charged": "Carregado", "co2": "CO₂", "duration": "Duração", "fallbackName": "Posto de Carregamento", "finished": "Tempo de finalização", "power": "Potência", "price": "Custo", "remaining": "Tempo restante", "remoteDisabledHard": "{source}: desativado", "remoteDisabledSoft": "{source}:carregamento PV adaptável desativado", "solar": "Solar"}, "loadpointSettings": {"batteryBoost": {"description": "Carga rápida da bateria de casa.", "label": "Boost bateria", "mode": "Disponível apenas nos modos solar e min+solar.", "once": "Boost ativo para esta sessão de carga."}, "batteryUsage": "Bateria de casa", "currents": "Corrente de carga", "default": "predefinido", "disclaimerHint": "Aviso:", "limitSoc": {"description": "limite de partilha que é usado quando este veículo está conectado.", "label": "Limite <PERSON>"}, "maxCurrent": {"label": "Corrente de carga máxima"}, "minCurrent": {"label": "Corrente de carga mínima"}, "minSoc": {"description": "O veículo é carregado “rápido” até {0} no modo solar. A seguir, continua com excedente solar. Útil para garantir um alcance mínimo mesmo em dias com pouco sol.", "label": "Min. de carga %"}, "onlyForSocBasedCharging": "Estas opções só estão disponíveis para veículos com nível de carregamento conhecido.", "phasesConfigured": {"label": "Fases elétricas", "no1p3pSupport": "Como está ligado o seu carregador?", "phases_0": "comutação automática", "phases_1": "1 fase", "phases_1_hint": "({min} a {max})", "phases_3": "3 fases", "phases_3_hint": "({min} a {max})"}, "smartCostCheap": "Carregamento de Rede em tarifa mais económica", "smartCostClean": "Carregamento de Rede \"Verde\"", "title": "Configurações {0}", "vehicle": "Veí<PERSON>lo"}, "mode": {"minpv": "Min+Solar", "now": "<PERSON><PERSON><PERSON><PERSON>", "off": "Off", "pv": "Solar", "smart": "Smart"}, "provider": {"login": "iniciar se<PERSON><PERSON>", "logout": "encer<PERSON> sessão"}, "startConfiguration": "Iniciar a configuração", "targetCharge": {"activate": "Ativar", "co2Limit": "Limite de CO₂ de {co2}", "costLimitIgnore": "O {limit} configurado será ignorado durante este período.", "currentPlan": "Plano ativo", "descriptionEnergy": "Até quando {targetEnergy} deve ser carregado no veículo?", "descriptionSoc": "Quando deve o veículo ser carregado até {targetSoc}?", "goalReached": "Objetivo já atingido", "inactiveLabel": "Objetivo", "nextPlan": "Próximo plano", "notReachableInTime": "O objetivo será atingido {overrun} mais tarde.", "onlyInPvMode": "O plano de carregamento só funciona no modo solar.", "planDuration": "Tempo de carga", "planPeriodLabel": "<PERSON><PERSON><PERSON>", "planPeriodValue": "{start} até {end}", "planUnknown": "desconhecido", "preview": "pré-visualizar", "priceLimit": "limite de preço {preço}", "remove": "<PERSON><PERSON><PERSON>", "setTargetTime": "<PERSON><PERSON><PERSON>", "targetIsAboveLimit": "O limite de carregamento configurado de {limit} será ignorado durante este período.", "targetIsAboveVehicleLimit": "O limite do veículo está abaixo do objetivo de carregamento.", "targetIsInThePast": "<PERSON>sco<PERSON>ha um momento no futuro, <PERSON>.", "targetIsTooFarInTheFuture": "Vamos ajustar o plano assim que soubermos mais sobre o futuro.", "title": "Objetivo", "today": "hoje", "tomorrow": "aman<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "Aprender a configurá-la.", "vehicleCapacityRequired": "A capacidade da bateria do veículo é necessária para estimar o tempo de carregamento."}, "targetChargePlan": {"chargeDuration": "Tempo de carga", "co2Label": "Emissão de CO₂", "priceLabel": "Preço da energia", "timeRange": "{day} {range} h", "unknownPrice": "ainda desconhecido"}, "targetEnergy": {"label": "Limite", "noLimit": "<PERSON><PERSON><PERSON>"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON><PERSON>", "changeVehicle": "<PERSON><PERSON><PERSON>", "detectionActive": "A detectar veículo…", "fallbackName": "Veí<PERSON>lo", "moreActions": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "notReachable": "O veículo não está acessível. Tente reiniciar o evcc.", "targetSoc": "Limite", "temp": "Temperatura", "tempLimit": "<PERSON><PERSON>.", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleSoc": "Carga"}, "vehicleStatus": {"awaitingAuthorization": "À espera de autorização.", "batteryBoost": "Boost da bateria ativo.", "charging": "A carregar…", "cheapEnergyCharging": "Energia barata disponível.", "cheapEnergyNextStart": "Energia barata em {duration}.", "cheapEnergySet": "Limite de preço definido.", "cleanEnergyCharging": "Energia \"Verde\" disponível.", "cleanEnergyNextStart": "Energia \"Verde\" em {duration}.", "cleanEnergySet": "Limite de CO₂ definido.", "climating": "Pré-climatização detectada.", "connected": "Ligado.", "disconnectRequired": "Sessão terminada. Por favor, volte a ligar-se.", "disconnected": "Desligado.", "finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "minCharge": "<PERSON><PERSON><PERSON> mínima até {soc}.", "pvDisable": "Excedente insuficiente. Pausa em breve.", "pvEnable": "Excesso disponível. Carregamento em breve.", "scale1p": "Reduzindo para carregamento monofásico em breve.", "scale3p": "Aumentando para carregamento trifásico em breve.", "targetChargeActive": "Plano de carregamento ativo. Fim estimado em {duration}.", "targetChargePlanned": "O plano de carregamento começa em {duration}.", "targetChargeWaitForVehicle": "Plano de carregamento pronto. A aguardar veículo…", "vehicleLimit": "Limite dos veículos", "vehicleLimitReached": "<PERSON>ite de ve<PERSON><PERSON>lo <PERSON>.", "waitForVehicle": "Pronto. A aguardar veículo…", "welcome": "Carga inicial curta para confirmar a ligação."}, "vehicles": "Estacionamento", "welcome": "Be<PERSON>-vindo a bordo!"}, "notifications": {"dismissAll": "<PERSON><PERSON>", "logs": "Ver registos completos", "modalTitle": "Notificações"}, "offline": {"configurationError": "Erro durante a inicialização. Verifique a sua configuração e reinicie.", "message": "<PERSON>ão conectado a nenhum servidor.", "restart": "Reiniciar", "restartNeeded": "Necessário para aplicar as alterações.", "restarting": "Servidor a reiniciar."}, "passwordModal": {"description": "Defina uma Palavra-passe para proteger as definições de configuração. Continua a ser possível utilizar o menu principal sem iniciar sessão.", "empty": "A Palavra-passe não deve ficar por preencher", "error": "Erro: ", "labelCurrent": "Palavra-passe atual", "labelNew": "Nova Palavra-passe", "labelRepeat": "<PERSON><PERSON>r pala<PERSON>ra-passe", "newPassword": "<PERSON><PERSON>r palavra-passe", "noMatch": "As Palavras-passe não coincidem", "titleNew": "<PERSON><PERSON><PERSON>-passe de administrador", "titleUpdate": "Atualizar a Palavra-passe do administrador", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>e"}, "session": {"cancel": "<PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "finished": "Finalizado", "meter": "<PERSON><PERSON><PERSON>", "meterstart": "Início do Contador", "meterstop": "Contagem final", "odometer": "Quilómetros", "price": "Preço", "started": "Iniciado", "title": "Sessão de carga"}, "sessions": {"avgPower": "Potência", "avgPrice": "Preço", "chargeDuration": "Duração", "chartTitle": {"avgCo2ByGroup": "CO₂ {byGroup}", "avgPriceByGroup": "Preço {byGroup}", "byGroupLoadpoint": "por Posto de Carregamento", "byGroupVehicle": "por Veículo", "energy": "Energia Carregada", "energyGrouped": "Energia Solar vs. Energia de Rede", "energyGroupedByGroup": "Energia Acumulada {byGroup}", "energySubSolar": "{value} solar", "energySubTotal": "{value} total", "groupedCo2ByGroup": "Quantidade de CO₂ {byGroup}", "groupedPriceByGroup": "Custo total {byGroup}", "historyCo2": "Emissões de CO₂", "historyCo2Sub": "{value} total", "historyPrice": "Custos de Carregamento", "historyPriceSub": "{value} total", "solar": "Parte solar durante todo o ano", "solarByGroup": "Parte solar {byGroup}"}, "co2": "CO₂", "csv": {"chargedenergy": "Energia (kWh)", "chargeduration": "Duração", "co2perkwh": "CO₂/kWh", "created": "Hora de início", "finished": "Hora final", "identifier": "Identificador", "loadpoint": "Posto de Carregamento", "meterstart": "Início do Contador (kWh)", "meterstop": "Contagem final (kWh)", "odometer": "Quilometragem (km)", "price": "Preço", "priceperkwh": "Preço/kWh", "solarpercentage": "Solar (%)", "vehicle": "Veí<PERSON>lo"}, "csvPeriod": "Download {period} CSV", "csvTotal": "Download tudo CSV", "date": "Início", "energy": "Carregado", "filter": {"allLoadpoints": "todos os postos de carregamento", "allVehicles": "todos os veículos", "filter": "Filtro"}, "group": {"co2": "Emissões", "grid": "Rede", "price": "Preço", "self": "Solar"}, "groupBy": {"loadpoint": "Posto de Carregamento", "none": "Total", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Posto de Carregamento", "noData": "Nenhuma sessão de carregamento este mês.", "overview": "Vista geral", "period": {"month": "<PERSON><PERSON><PERSON>", "total": "Total", "year": "<PERSON><PERSON>"}, "price": "Custo", "reallyDelete": "Quer mesmo apagar esta sessão?", "showIndividualEntries": "Mostrar sessões individuais", "solar": "Solar", "title": "Sessões de carregamento", "total": "Total", "type": {"co2": "CO₂", "price": "Preço", "solar": "Solar"}, "vehicle": "Veí<PERSON>lo"}, "settings": {"fullscreen": {"enter": "Entrar em tela cheia", "exit": "<PERSON>r da tela cheia", "label": "Tela cheia"}, "hiddenFeatures": {"label": "Experimental", "value": "Mostrar recursos experimentais."}, "language": {"auto": "Automático", "label": "Idioma"}, "sponsorToken": {"expires": "O seu token de Apoiante (Sponsor) expira em {inXDays}. {getNewToken} e atualize-o aqui.", "getNew": "Adquira um novo", "hint": "Nota: Vamos tornar isso automático no futuro."}, "telemetry": {"label": "Telemetria"}, "theme": {"auto": "sistema", "dark": "escuro", "label": "<PERSON><PERSON><PERSON>", "light": "claro"}, "time": {"12h": "12 horas", "24h": "24 horas", "label": "Formato da hora"}, "title": "Interface de utilizador", "unit": {"km": "km", "label": "Unidades", "mi": "milhas"}}, "smartCost": {"activeHours": "{active} de {total}", "activeHoursLabel": "Horas ativas", "applyToAll": "Aplicar em todo o lado?", "batteryDescription": "Carrega a bateria doméstica com energia da Rede.", "cheapTitle": "Carregamento da Rede mais económico", "cleanTitle": "Carregamento de Rede \"Verde\"", "co2Label": "Emissão de CO₂", "co2Limit": "Limite de CO₂", "loadpointDescription": "Permite carregamento rápido temporário no modo solar.", "modalTitle": "Carregamento de Rede Inteligente", "none": "<PERSON><PERSON><PERSON>", "priceLabel": "Preço da energia", "priceLimit": "Preço limite", "resetAction": "Remover limite", "resetWarning": "Não estão configurados o preço dinâmico da rede ou a fonte de CO₂. No entanto, ainda existe um limite de {limite}. Limpar a sua configuração?", "saved": "Guardado."}, "startupError": {"configFile": "Ficheiro de configuração em uso:", "configuration": "Configuração", "description": "Verifique seu ficheiro de configuração. Se a mensagem de erro não ajudar, verifique as {0}.", "discussions": "Discussões no GitHub", "fixAndRestart": "Por favor corrija o problema e reinicie o servidor.", "hint": "Nota: Pode acontecer que tenha um equipamento com defeito (inversor, contador, …). Verifique as suas conexões de rede.", "lineError": "Erro em {0}.", "lineErrorLink": "linha {0}", "restartButton": "Reiniciar", "title": "Erro ao iniciar"}}