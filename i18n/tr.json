{"batterySettings": {"batteryLevel": "Doluluk oranı", "bufferStart": {"above": "{soc} seviyesinin üzerindeyken.", "full": "{soc} seviyesindeyken.", "never": "yalnızca yeterli fazlalık ile."}, "capacity": "{total}'in {energy}'si", "control": "Batarya kontrolü", "discharge": "Hızlı modda ve planlanan doldurmada boşalmayı önle.", "disclaimerHint": "Not:", "disclaimerText": "Bu ayarlar yalnızca güneş enerjisi yöntemini etkiler. Doldurma davranışı buna göre ayarlanır.", "gridChargeTab": "Şebekeden doldurma", "legendBottomName": "<PERSON>v bataryasına öncelik ver", "legendBottomSubline": "{soc} se<PERSON><PERSON><PERSON> ul<PERSON> kadar.", "legendMiddleName": "<PERSON><PERSON>ın doldurulmasına öncelik ver", "legendMiddleSubline": "ev bataryası {soc} seviyesinin üzerindeyse.", "legendTopAutostart": "Otomatik olarak başla", "legendTopName": "Batarya destekli araç doldurma", "legendTopSubline": "ev bataryası {soc} seviyesinin üzerindeyse.", "modalTitle": "<PERSON>v <PERSON>ı", "usageTab": "Batarya k<PERSON>anımı"}, "config": {"aux": {"description": "Tüketimini mevcut fazlalığa göre ayarlayan cihaz (akıllı su ısıtıcıları gibi). evcc, bu cihazın gerektiğinde güç tüketimini azaltmasını bekler.", "titleAdd": "<PERSON><PERSON>üzenleyen Tüketici Ekle", "titleEdit": "<PERSON><PERSON> Düzenleyen Tüketiciyi Düzenle"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "Bataryayı Düzenle"}, "charge": {"titleAdd": "Doldurma sayacı ekle", "titleEdit": "Doldurma sayacını düzenle"}, "charger": {"chargers": "Doldurma cihazları", "generic": "<PERSON>l entegrasyon", "heatingdevices": "Isıtma cihazları", "ocppHelp": "Bu adresi doldurma cihazının yapılandırmasına kopyala.", "ocppLabel": "OCPP sunucusu URL'si", "switchsockets": "“Açılıp kapanabilen prizler”", "template": "Üretici", "titleAdd": {"charging": "Doldurma Cihazı Ekle", "heating": "Isıtıcı Ekle"}, "titleEdit": {"charging": "Doldurma Cihazını Düzenle", "heating": "Isıtıcıyı Düzenle"}, "type": {"custom": {"charging": "Kullanıcı tanımlı doldurma cihazı", "heating": "Kullanıcı tanımlı ısıtıcı"}, "heatpump": "Kullanıcı tanımlı ısı pompası", "sgready": "Kullanıcı tanımlı ısı pompası (sg-ready, tamamı)", "sgready-boost": "Kullanıcı tanımlı ısı pompası (sg-ready, takviye)", "switchsocket": "Kullanıcı tanımlı şalterli priz"}}, "circuits": {"description": "Bir devreye bağlı tüm yük noktalarının toplamının, yapılandırılan güç ve akım limitlerini aşmamasını sağlar. <PERSON><PERSON><PERSON>, bir hiyerarşi oluşturacak şekilde iç içe yerleştirilebilir.", "title": "<PERSON><PERSON><PERSON>"}, "control": {"description": "<PERSON><PERSON><PERSON>lan değerler iyidir. Bunları yalnızca ne yaptığını biliyorsan değiştir.", "descriptionInterval": "Saniye cinsinden denetim çevrimi güncelleme döngüsü. evcc'nin ölçüm verilerini ne sıklıkla okuyacağını, doldurma gücünü ayarlayacağını ve kullanıcı arayüzünü güncelleyeceğini tanımlar. Kısa aralıklar (<30s) dalgalanmalara ve istenmeyen davranışlara yol açabilir.", "descriptionResidualPower": "Denetim çevriminin çalışma noktasını değiştirir. Eğer bir ev bataryan varsa 100 W değerini ayarlaman önerilir. <PERSON>u <PERSON><PERSON><PERSON>, şebeke kullanımına göre hafif bir önceliğe sahip olacaktır.", "labelInterval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelResidualPower": "Artık güç", "title": "<PERSON><PERSON><PERSON>"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON>", "broker": "Aracı", "bucket": "<PERSON><PERSON>", "capacity": "Kapasite", "chargeStatus": "Durum", "chargeStatusA": "bağlı değil", "chargeStatusB": "bağlı", "chargeStatusC": "dolu<PERSON>r", "chargeStatusE": "elektrik yok", "chargeStatusF": "hata", "chargedEnergy": "<PERSON><PERSON><PERSON>", "co2": "Şebeke CO₂", "configured": "Yapılandırıldı", "controllable": "Kontrol edilebilir", "currency": "Para Birimi", "current": "Akım", "currentRange": "Akım", "enabled": "<PERSON><PERSON><PERSON>", "energy": "<PERSON><PERSON><PERSON>", "feedinPrice": "Satış fiyatı", "gridPrice": "<PERSON><PERSON>m <PERSON>yatı", "heaterTempLimit": "Isıtıcı sınırlaması", "hemsType": "Sistem", "identifier": "RFID tanımlayıcısı", "no": "<PERSON>ı<PERSON>", "odometer": "Kilometre Sayacı", "org": "Organizasyon", "phaseCurrents": "Faz Akımı L1, L2, L3", "phasePowers": "Faz Gücü L1, L2, L3", "phaseVoltages": "Faz Voltajı L1, L2, L3", "phases1p3p": "Faz değiştirme", "power": "<PERSON><PERSON><PERSON>", "powerRange": "<PERSON><PERSON><PERSON><PERSON>”", "range": "Menzil", "singlePhase": "“Tek aşamalı”", "soc": "Doluluk", "solarForecast": "<PERSON><PERSON><PERSON><PERSON>", "temp": "<PERSON><PERSON>", "topic": "<PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "“<PERSON><PERSON> sınırı”", "yes": "evet"}, "deviceValueChargeStatus": {"A": "“A (bağlı değil)”", "B": "“B (bağlı)”", "C": "“C (dolduruyor)”"}, "devices": {"auxMeter": "“Akıllı tüketici”", "batteryStorage": "“Batarya”", "solarSystem": "“Güneş Enerji <PERSON>”"}, "editor": {"loading": "YAML düzenleyici yükleniyor…"}, "eebus": {"description": "evcc'nin di<PERSON>er EEBus cihazlarıyla iletişim kurmasını sağlayan yapılandırma.", "title": "EEBus"}, "ext": {"description": "Yük yönetimi veya istatistik amaçları için kullanılabilir.", "titleAdd": "<PERSON><PERSON>", "titleEdit": "Harici <PERSON>le"}, "form": {"danger": "<PERSON><PERSON><PERSON>", "deprecated": "“kull<PERSON><PERSON><PERSON><PERSON> kaldırıldı”", "example": "Örnek", "optional": "iste<PERSON>e bağlı"}, "general": {"cancel": "İptal", "customHelp": "evcc'nin eklenti sistemini kullanarak kullanıcı tanımlı bir cihaz oluştur.", "customOption": "Kullanıcı tanımlı cihaz", "delete": "“Sil”", "docsLink": "<PERSON><PERSON><PERSON> bak.", "experimental": "Den<PERSON>sel", "hideAdvancedSettings": "“Gelişmiş ayarları gizle”", "invalidFileSelected": "Geçersiz dosya seçildi", "noFileSelected": "Seçili dosya yok.", "off": "kapalı", "on": "açık", "password": "Şifre", "readFromFile": "<PERSON><PERSON><PERSON><PERSON> oku", "remove": "Kaldır", "save": "<PERSON><PERSON>", "selectFile": "<PERSON><PERSON><PERSON><PERSON>", "showAdvancedSettings": "“Gelişmiş ayarları göster”", "telemetry": "Uzölçüm", "templateLoading": "Dolduruyorum...", "title": "Başlık", "validateSave": "“Doğrula ve kaydet”"}, "grid": {"title": "Elektrik sayacı", "titleAdd": "Elektrik Sayacı Ekle", "titleEdit": "Elektrik Sayacını Düzenle"}, "hems": {"description": "evcc'yi ba<PERSON>ka bir ev enerji yönetim siste<PERSON> ba<PERSON>.", "title": "HEMS"}, "icon": {"change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "influx": {"description": "Doldurma verilerini ve diğer ölçümleri InfluxDB'ye yazar. Verileri görselleştirmek için Grafana veya başka araçlar kullan.", "descriptionToken": "Nasıl oluşturulacağını öğrenmek için InfluxDB belgelerine göz at. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "<PERSON><PERSON>", "labelCheckInsecure": "<PERSON><PERSON><PERSON><PERSON><PERSON>z bağlantılara izin ver”", "labelDatabase": "Veritabanı", "labelInsecure": "“Sertifika doğrulama”", "labelOrg": "Organizasyon", "labelPassword": "Pa<PERSON><PERSON>", "labelToken": "API Jetonu", "labelUrl": "URL", "labelUser": "Kullanıcı Adı", "title": "InfluxDB", "v1Support": "InfluxDB 1.x için desteğe mi ihtiyacın var?", "v2Support": "InfluxDB 2.x'e geri dön"}, "loadpoint": {"addCharger": {"charging": "“Doldurma cihazı ekle”", "heating": "Isıtıcı ekle"}, "addMeter": "<PERSON>lave enerji <PERSON> e<PERSON>", "cancel": "“İptal”", "chargerError": {"charging": "Doldurma cihazı yapılandırılmalı.", "heating": "Bir ısıtıcının yapılandırılması gerekli."}, "chargerLabel": {"charging": "“Doldurma cihazı”", "heating": "Isıtıcı"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "6 ila 16 A akım aralığını kullanır.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "6 ila 32 A akım aralığını kullanır.", "chargerPowerCustom": "<PERSON><PERSON><PERSON>", "chargerPowerCustomHelp": "Kendine özgü bir akım aralığı tanımla.", "chargerTypeLabel": "“Doldurma gücü”", "chargingTitle": "Davranış", "circuitHelp": "G<PERSON>ç ve akım limitlerinin aşılmaması için yük yönetim ataması.", "circuitLabel": "“Devre”", "circuitUnassigned": "“atanmamış”", "defaultModeHelp": {"charging": "<PERSON>ç bağlandığında doldurma şekli.", "heating": "Sistem başlatıldığında ayarlanır."}, "defaultModeHelpKeep": "Son se<PERSON>ilen modu korur.", "defaultModeLabel": "“Varsayılan şekil”", "delete": "“Sil”", "electricalSubtitle": "<PERSON><PERSON> elektrikçine sor.", "electricalTitle": "“Elektrikli”", "energyMeterHelp": "Doldurma cihazında bütünleşik bir sayaç yoksa ek sayaç.", "energyMeterLabel": "<PERSON><PERSON><PERSON><PERSON>”", "estimateLabel": "“API güncellemeleri arasında doldurma seviyesini hesapla”", "maxCurrentHelp": "Asgari akımdan büyük olmalı.", "maxCurrentLabel": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>”", "minCurrentHelp": "Sadece ne yaptığını biliyorsan 6 A altına in.", "minCurrentLabel": "“<PERSON>gari akım”", "noVehicles": "<PERSON>ç araç ya<PERSON>ılandırılmamış.", "option": {"charging": "Doldurma noktası ekle", "heating": "Isıtma cihazı ekle"}, "phases1p": "“1 aşamalı”", "phases3p": "“3 aşamalı”", "phasesAutomatic": "“Otomatik aşamalar”", "phasesAutomaticHelp": "Doldurma cihazın 1 ve 3 aşamalı doldurma arasında otomatik geçişi destekliyor. Ana görüntüde doldurma sırasında aşama davranışını ayarlayabilirsin.", "phasesHelp": "Bağlı olan aşama sayısı.", "phasesLabel": "“Aşamalar”", "pollIntervalDanger": "Aracın sürekli sorgulanması araç bataryasını boşaltabilir. Bazı araç üreticileri bu durumda doldurma işlemini etkin bir şekilde engelleyebilir. Tavsiye etmiyoruz! Bunu yalnızca risklerin farkındaysan kullan.", "pollIntervalHelp": "Araç API güncellemeleri arasındaki süre. Kısa aralıklar aracın bataryasını tüketebilir.", "pollIntervalLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>”", "pollModeAlways": "“daima”", "pollModeAlwaysHelp": "Daima düzenli aralıklarla durum sorgulaması yap.", "pollModeCharging": "<PERSON><PERSON><PERSON>", "pollModeChargingHelp": "Sadece doldururken araç durum güncellemelerini talep et.", "pollModeConnected": "“bağlıyken”", "pollModeConnectedHelp": "Bağlıyken araç durumunu düzenli aralıklarla güncelle.", "pollModeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>”", "priorityHelp": "Öncelikli olanlar güneş enerjisi fazlasına öncelikli erişir.", "priorityLabel": "“Öncelik”", "save": "<PERSON><PERSON><PERSON>”", "showAllSettings": "“<PERSON><PERSON><PERSON> a<PERSON>ı gö<PERSON>”", "solarBehaviorCustomHelp": "<PERSON><PERSON>, ka<PERSON><PERSON> eşiklerini ve gecikmeleri tanımla.", "solarBehaviorDefaultHelp": "Yet<PERSON><PERSON> fazlalığın {enableDelay} ardından başla. {disableDelay} i<PERSON>in yeterli fazlalık olmadığında dur.", "solarBehaviorLabel": "Güneş enerjisi fazlalığı", "solarModeCustom": "“özel”", "solarModeMaximum": "“sad<PERSON><PERSON>”", "thresholdDisableDelayLabel": "“<PERSON><PERSON><PERSON><PERSON> g<PERSON>”", "thresholdDisableHelpInvalid": "Lütfen pozitif bir de<PERSON> k<PERSON>.", "thresholdDisableHelpPositive": "{delay} süres<PERSON>ce şebekeden {power} değerinden fazla güç kullanıldığında durdur.", "thresholdDisableHelpZero": "{delay} süres<PERSON>ce asgari gerekli güç karşılanamadığında durdur.", "thresholdDisableLabel": "“Şebeke elektriğini kapat”", "thresholdEnableDelayLabel": "“Açma gecikmesi”", "thresholdEnableHelpInvalid": "Lütfen negatif bir <PERSON><PERSON> k<PERSON>.", "thresholdEnableHelpNegative": "{delay} için {surplus} fazlalık varsa doldurmaya başla.", "thresholdEnableHelpZero": "{delay} i<PERSON><PERSON> asgari doldurma gücü fazlası mevcut olduğunda başlat.", "thresholdEnableLabel": "“Şebeke gücünü aç”", "titleAdd": {"charging": "Doldurma Noktası Ekle", "heating": "Isıtma Cihazı Ekle", "unknown": "Doldurma Cihazı veya Isıtıcı Ekle"}, "titleEdit": {"charging": "Doldurma Noktasını Düzenle", "heating": "Isıtma Cihazını Düzenle", "unknown": "Doldurma Cihazını veya Isıtıcıyı Düzenle"}, "titleExample": {"charging": "Garaj, Carport, vb.", "heating": "<PERSON><PERSON> pompası, Isıtıcı, vb."}, "titleLabel": "“Başlık”", "vehicleAutoDetection": "“otomatik algılama”", "vehicleHelpAutoDetection": "Otomatik olarak en makul aracı seçer. Elden değiştirme mümkündür.", "vehicleHelpDefault": "Her zaman bu aracın burada doldurduğunu varsayar. Otomatik algılama devre dışı. <PERSON>den değiştirme mümkün.", "vehicleLabel": "“Varsayılan araç”", "vehiclesTitle": "“Araçlar”"}, "main": {"addAdditional": "Ek <PERSON>ç e<PERSON>", "addGrid": "“Elektrik sayacı ekle”", "addLoadpoint": "Doldurma cihazı veya ısıtıcı ekle", "addPvBattery": "Güneş enerjisi veya enerji deposu ekle", "addTariffs": "“<PERSON><PERSON><PERSON> e<PERSON>”", "addVehicle": "<PERSON><PERSON>", "configured": "yapılandırıldı", "edit": "<PERSON><PERSON><PERSON><PERSON>", "loadpointRequired": "En az bir doldurma noktası yapılandırılmalı.", "name": "“İsim”", "title": "Yapılandırma", "unconfigured": "yapılandırılmadı", "vehicles": "Araçlarım", "yaml": "evcc.yaml dosyasındaki cihazlar düzenlenemez."}, "messaging": {"description": "Dolum oturumları hakkında bildirimler al.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "meter": {"cancel": "İptal", "delete": "Sil", "generic": "<PERSON><PERSON>", "option": {"aux": "<PERSON><PERSON> kendini düzenleyen tüketici ekle", "battery": "<PERSON><PERSON>", "ext": "<PERSON><PERSON>", "pv": "G<PERSON>neş ölçer ekle"}, "save": "<PERSON><PERSON>", "specific": "<PERSON><PERSON><PERSON>", "template": "Üretici", "titleChoice": "Ne Eklemek İstersin?", "validateSave": "Doğrula ve kaydet"}, "modbus": {"baudrate": "<PERSON><PERSON> hızı", "comset": "ComSet", "connection": "Modbus bağlantısı", "connectionHintSerial": "Cihaz RS485 arayüzü ile doğrudan evcc'ye bağlanır.", "connectionHintTcpip": "Cihaz LAN/Wifi üzerinden evcc'den adreslenebilir.", "connectionValueSerial": "Seri / USB", "connectionValueTcpip": "Ağ", "device": "Cihaz adı", "deviceHint": "Örnek: /dev/ttyUSB0", "host": "IP adresi yada hostname", "hostHint": "Örnek: *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus protokolü", "protocolHintRtu": "Protokol çevirisi olmadan RS485 den Ethernet adaptörü üzerinden bağlantı.", "protocolHintTcp": "<PERSON><PERSON><PERSON>, dahili LAN/Wi-Fi desteği ile ya da RS485–Ethernet adaptörü üzerinden protokol çevrimi yapılarak ağa erişir.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Birden fazla istemcinin tek bir Modbus cihazına erişmesine izin verir.", "title": "Modbus vekili"}, "mqtt": {"authentication": "Kimlik Doğrulama", "description": "Ağındaki diğer sistemlerle veri alışverişi yapmak için evcc'yi bir MQTT aracısına bağla.", "descriptionClientId": "İletilerin yazarı. <PERSON><PERSON><PERSON> b<PERSON> `evcc-[rand]` kullanılır.", "descriptionTopic": "Yayınlamayı devre dışı bırakmak için boş bırak.", "labelBroker": "Aracı", "labelCaCert": "“<PERSON><PERSON><PERSON> sert<PERSON>ı (CA)”", "labelCheckInsecure": "<PERSON><PERSON><PERSON><PERSON> olma<PERSON> bağlantılara izin ver", "labelClientCert": "“Müşteri sertifikası”", "labelClientId": "Müşteri kimliği", "labelClientKey": "“Müşteri anahtarı”", "labelInsecure": "Sertifika doğrulama", "labelPassword": "Pa<PERSON><PERSON>", "labelTopic": "<PERSON><PERSON>", "labelUser": "Kullanıcı Adı", "publishing": "<PERSON><PERSON><PERSON><PERSON><PERSON>\"", "title": "MQTT"}, "network": {"descriptionHost": "mDNS'yi et<PERSON>ştirmek için .local sonekini kullan. Mobil uygulamanın ve bazı OCPP doldurma cihazlarının tanınması ile ilgili.", "descriptionPort": "Web arayüzü ve API için bağlantı noktası. <PERSON><PERSON><PERSON> tarayıcının URL'sini güncellemen gerekir.", "descriptionSchema": "Yalnızca URL'lerin oluşturuluşunu etkiler. HTTPS'nin seçilmesi şifrelemeyi etkinleştirmez.", "labelHost": "<PERSON> makine adı", "labelPort": "Port", "labelSchema": "<PERSON><PERSON>", "title": "Ağ"}, "options": {"boolean": {"no": "“hayır”", "yes": "evet"}, "endianness": {"big": "büyük uçlu", "little": "küçük uçlu"}, "operationMode": {"heating": "<PERSON><PERSON><PERSON><PERSON>", "standby": "Bekleme"}, "schema": {"http": "HTTP (şifrelenmemiş)", "https": "HTTPS (şifrelenmiş)"}, "status": {"A": "“A (bağlı değil)”", "B": "“B (bağlı)”", "C": "“C (dolduruyor)”"}}, "pv": {"titleAdd": "GES Sayacı Ekle", "titleEdit": "GES Sayacını Düzenle"}, "section": {"additionalMeter": "Ek sayaçlar", "general": "<PERSON><PERSON>", "grid": "Şebeke", "integrations": "<PERSON><PERSON><PERSON>ü<PERSON><PERSON>ş<PERSON>rmeler", "loadpoints": "Doldurma ve Isıtma", "meter": "GES ve Batarya", "system": "Sistem", "vehicles": "Araçlar"}, "sponsor": {"addToken": "Jetonu gir", "changeToken": "<PERSON><PERSON><PERSON>", "description": "Destek modeli, projeyi sürdürmemize ve sürdürülebilir bir şekilde yeni ve heyecan verici özellikler geliştirmemize yardımcı oluyor. Destekçi olarak tüm doldurma cihazı uygulamalarına erişimin oluyor.", "descriptionToken": "Jetonu {url} adresinden alabilirsiniz. Denemek için bir deneme jetonu da sunuyoruz.", "error": "Destekçi jetonu geçerli değil.", "labelToken": "Destekçi jetonu", "title": "Destekçilik", "tokenRequired": "Bu cihazı oluşturabilmen için önce bir destekçi jetonu yapılandırmalısın.", "tokenRequiredLearnMore": "<PERSON>ha fazla bilgi edin.", "tokenRequiredShort": "Sponsor jetonu yapılandırılmadı.", "trialToken": "<PERSON><PERSON><PERSON> jetonu"}, "system": {"backupRestore": {"backup": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON> indir...", "confirmationButton": "Yedeklemeyi indir", "confirmationText": "Veritabanı dosyasını indirmek için lütfen şifreni gir.", "description": "Verilerini bir dosyaya yedekle. Bu dosya, bir sistem arızası durumunda verilerini geri yüklemek için kullanılabilir.", "title": "<PERSON><PERSON><PERSON>"}, "cancel": "İptal", "description": "Verilerini <PERSON>dek<PERSON>, geri yü<PERSON> ve sıfırla. Verilerini başka bir sisteme taşımak istiyorsan kullanışlıdır.", "note": "Not: <PERSON><PERSON><PERSON><PERSON><PERSON> tüm eylemler yalnızca veritabanı verilerini etkiler. evcc.yaml yapılandırma dosyası değişmeden kalır.", "reset": {"action": "Sıfırla...", "confirmationButton": "Sıfırla ve yeniden başlat", "confirmationText": "<PERSON><PERSON>, se<PERSON><PERSON><PERSON><PERSON> verileri kalıcı olarak siler. Önce bir yedek indirdiğinden emin ol.", "description": "Yapılandırma ile ilgili sorun mu yaşıyorsun ve baştan mı başlamak istiyorsun? Tüm verileri sil ve yeni bir ba<PERSON><PERSON><PERSON><PERSON> yap.", "sessions": "Doldurma oturumları", "sessionsDescription": "Doldurma oturumları geçmişini siler.", "settings": "Yapılandırma ve ayarlar", "settingsDescription": "Yapılandırılmış tüm cihazları, hizmetleri, planları, önbellekleri vb. siler.", "title": "Sıfırla"}, "restore": {"action": "<PERSON><PERSON>...", "confirmationButton": "<PERSON><PERSON> yü<PERSON> ve yeniden ba<PERSON><PERSON>", "confirmationText": "<PERSON><PERSON>, tüm veritabanının üzerine yazar. Önce bir yedek indirdiğinden emin ol.", "description": "Verilerini bir yedekleme dos<PERSON>ından geri yükle. Bu, mevcut tüm verilerinin üzerine yazar.", "labelFile": "Ye<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "title": "Yedekleme ve <PERSON>"}, "logs": "Loglar", "restart": "<PERSON><PERSON><PERSON>", "restartRequiredDescription": "Değişikliklerin yansıması için yeniden başlatma gereklidir.", "restartRequiredMessage": "Yapılandırma ayarları değiştirildi.", "restartingDescription": "<PERSON><PERSON><PERSON><PERSON> bekle…", "restartingMessage": "evcc yeniden başlatılıyor."}, "tariffs": {"description": "Doldurma oturumlarının maliyetlerini hesaplamak için elektrik tarifelerini gir.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Ana ekranda ve tarayıcı sekmesinde görüntülenir.", "label": "Başlık", "title": "Başlığı Düzenle"}, "validation": {"failed": "başarısız", "label": "Durum", "running": "doğrulanıyor…", "success": "başarılı", "unknown": "bilinmiyor", "validate": "<PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "İptal", "chargingSettings": "“ Doldurma ayarları”", "defaultMode": "“Varsayılan ayar”", "defaultModeHelp": "<PERSON><PERSON> bağlanırken doldurma ayarı.", "delete": "Aracı sil", "generic": "<PERSON><PERSON><PERSON>", "identifiers": "“RFID tanımlayıcıları”", "identifiersHelp": "Aracı tanımlamak için RFID dizelerinin listesi. Satır başına bir giriş. Güncel tanımlayıcıyı, genel bakış sayfasındaki ilgili doldurma noktasında bulabilirsin.", "maximumCurrent": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>”", "maximumCurrentHelp": "Asgari akımdan büyük olmalıdır.", "maximumPhases": "<PERSON><PERSON><PERSON><PERSON>”", "maximumPhasesHelp": "Bu araç kaç aşama ile doldurulabilir? Gerekli asgari güneş enerjisi fazlasını ve plân süresini hesaplamak için kullanılır.", "minimumCurrent": "“<PERSON>gari akım”", "minimumCurrentHelp": "Yalnızca ne yaptığını biliyorsan 6A'in altına in.", "online": "Çevrimiçi API'ye sahip araçlar", "primary": "<PERSON><PERSON>", "priority": "“Öncelik”", "priorityHelp": "<PERSON><PERSON> y<PERSON><PERSON><PERSON> öncelik, bu a<PERSON>ç güneş enerjisi fazlasına öncelikli erişim sağlayacak demektir.", "save": "<PERSON><PERSON>", "scooter": "<PERSON>ek<PERSON><PERSON><PERSON>", "template": "Üretici", "titleAdd": "<PERSON><PERSON>", "titleEdit": "Aracı Düzenle", "validateSave": "Doğrula ve kaydet"}}, "footer": {"community": {"greenEnergy": "G<PERSON>neş Enerjisi", "greenEnergySub1": "evcc ile dolduruldu", "greenEnergySub2": "Ekim 2022'den beri", "greenShare": "Güneş enerjisi payı", "greenShareSub1": "güneş enerjisi ve enerji deposu", "greenShareSub2": "tarafından sa<PERSON>lanan enerji", "power": "Doldurma gücü", "powerSub1": "{totalClients} katı<PERSON>ımcıdan {activeClients} katılımcı", "powerSub2": "dolduruyor…", "tabTitle": "Canlı topluluk"}, "savings": {"co2Saved": "{value} CO₂ tasarruf edildi", "co2Title": "CO₂ Emisyonu", "configurePriceCo2": "Fiyat ve CO₂ emisyonlarını yapılandır.", "footerLong": "{percent} g<PERSON><PERSON><PERSON> enerji<PERSON>", "footerShort": "{percent} güneş", "modalTitle": "Doldurma Enerjisi Genel Bakışı", "moneySaved": "{value} tasar<PERSON>f edildi", "percentGrid": "{grid} <PERSON><PERSON> <PERSON><PERSON><PERSON>", "percentSelf": "{self} <PERSON>h g<PERSON>", "percentTitle": "G<PERSON>neş Enerjisi", "period": {"30d": "son 30 gün", "365d": "son 365 gün", "thisYear": "“bu yıl”", "total": "tüm zaman"}, "periodLabel": "Zaman aralığı:", "priceTitle": "Enerji fiyatı", "referenceGrid": "<PERSON><PERSON><PERSON><PERSON>", "referenceLabel": "Referans verileri:", "tabTitle": "Verilerim"}, "sponsor": {"becomeSponsor": "Destekçi ol", "becomeSponsorExtended": "Çıkartma almak için bizi doğrudan des<PERSON>kle.", "confetti": "<PERSON><PERSON><PERSON><PERSON>?", "confettiPromise": "Çıkartmalar ve dijital konfeti de var", "sticker": "… ya da evcc çıkartmaları?", "supportUs": "Hedefimiz gü<PERSON>ş enerjisi ile yakıt ikmalini gelenek haline getirmek. Bize yardım et ve evcc'yi maddi olarak destekle.", "thanks": "Teşekkürler {sponsor}! Katkın evcc'yi daha da geliştirmemize yardımcı oluyor.", "titleNoSponsor": "Bize destek ol", "titleSponsor": "Destekçisin", "titleTrial": "<PERSON><PERSON><PERSON> modu", "titleVictron": "Victron Energy tarafından desteklenmektedir", "trial": "Deneme modundasın ve tüm özellikleri kullanabilirsin. Destekçi olursan seviniriz.", "victron": "Victron Energy donanımı üzerinde evcc kullanıyorsun ve tüm özelliklere erişebiliyorsun."}, "telemetry": {"optIn": "Doldurma verilerimi <PERSON>mak istiyorum.", "optInMoreDetails": "Daha fazla ayrıntı {0}.", "optInMoreDetailsLink": "burada", "optInSponsorship": "Destekçi olman gere<PERSON>."}, "version": {"availableLong": "yeni sürüm mevcut", "modalCancel": "İptal", "modalDownload": "<PERSON><PERSON><PERSON>", "modalInstalledVersion": "<PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON>", "modalNoReleaseNotes": "Sürüm bilgileri mevcut değil. Yeni sürüm hakkında bilgiler:", "modalTitle": "<PERSON><PERSON> sü<PERSON>üm mevcut", "modalUpdate": "<PERSON><PERSON>", "modalUpdateNow": "<PERSON><PERSON><PERSON> kur", "modalUpdateStarted": "evcc'nin yeni sü<PERSON>ü<PERSON> başlatılıyor…", "modalUpdateStatusStart": "<PERSON><PERSON><PERSON> başladı:"}}, "forecast": {"co2": {"average": "“Ortalama”", "lowestHour": "“En temiz saat”", "range": "“Aralık”"}, "modalTitle": "“Öngörü”", "price": {"average": "“Ortalama”", "lowestHour": "“En ucuz saat”", "range": "“Aralık”"}, "solar": {"dayAfterTomorrow": "“<PERSON><PERSON><PERSON><PERSON>n sonra”", "partly": "“kısmen”", "remaining": "“kalan”", "today": "“<PERSON><PERSON><PERSON><PERSON>”", "tomorrow": "“<PERSON><PERSON><PERSON><PERSON>”"}, "solarAdjust": "Güneş enerjisi tahm<PERSON>ini gerçek üretim verilerine göre a<PERSON>{percent}.", "type": {"co2": "CO₂", "price": "“<PERSON><PERSON><PERSON>”", "solar": "“Güneş”"}}, "header": {"about": "evcc hakkında", "blog": "Blog", "docs": "<PERSON><PERSON><PERSON>", "github": "GitHub", "login": "<PERSON><PERSON>", "logout": "Çıkış", "nativeSettings": "<PERSON> makin<PERSON>", "needHelp": "<PERSON>ıma mı ihtiyacın var?", "sessions": "Doldurma Oturumları"}, "help": {"discussionsButton": "GitHub tartışmaları", "documentationButton": "<PERSON><PERSON><PERSON>", "issueButton": "<PERSON><PERSON> bildir", "issueDescription": "<PERSON><PERSON><PERSON> yada yanlış bir durum mu buldun?", "logsButton": "Logları görüntüle", "logsDescription": "Hatalar için logları gözden geçir.", "modalTitle": "<PERSON>ıma mı ihtiyacın var?", "primaryActions": "Bir şeyler çalışması gerektiği gibi çalışmıyor mu? Bunlar yardım almak için iyi yerler.", "restart": {"cancel": "İptal", "confirm": "<PERSON><PERSON>, yeniden ba<PERSON><PERSON>!", "description": "Normal koşullarda yeniden başlatma gerekmemeli. Eğer evcc'yi sü<PERSON>li olarak yeniden başlat<PERSON> gere<PERSON>, bir hata bildiri<PERSON> yap.", "disclaimer": "Not: evcc kendini sonlandıracak ve işletim sistemi tarafindan yeniden başlatılacağına güveniyor.", "modalTitle": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>k istediğine emin misin?"}, "restartButton": "<PERSON><PERSON><PERSON>", "restartDescription": "Cihazı kapatıp tekrar açmayı denedin mi?", "secondaryActions": "Hâlâ bir çözüm bulamadın mı? Burada birkaç seçenek daha var.."}, "log": {"areaLabel": "<PERSON><PERSON> g<PERSON> filtrele", "areas": "<PERSON><PERSON><PERSON>", "download": "Bütün log<PERSON> indir", "levelLabel": "Log sevi<PERSON>ine göre filtrele", "nAreas": "{count} alan<PERSON>", "noResults": "Uygun log kaydı bulunamadı.", "search": "Ara", "selectAll": "<PERSON><PERSON><PERSON>", "showAll": "<PERSON><PERSON><PERSON> kayıtları göster", "title": "Loglar", "update": "Otomatik güncelle"}, "loginModal": {"cancel": "İptal", "demoMode": "Demo modunda oturum açamazsınız.", "error": "G<PERSON>ş başarısız: ", "iframeHint": "evcc'yi yeni bir sekmede aç.", "iframeIssue": "<PERSON><PERSON><PERSON>, ancak tarayıcın kimlik doğrulama çerezini reddetti. Bu, evcc'yi HTTP üzerinden bir iframe içinde çalıştırırsan meydana gelebilir.", "invalid": "Şifre geçersiz.", "login": "<PERSON><PERSON><PERSON> yap", "password": "Yönetici Şifresi", "reset": "<PERSON><PERSON><PERSON>i sıfırla?", "title": "Kimlik Doğrulama"}, "main": {"chargingPlan": {"active": "Aktif", "addRepeatingPlan": "“<PERSON><PERSON>r eden plan ekle”", "arrivalTab": "Varış", "day": "<PERSON><PERSON><PERSON>", "departureTab": "Ayrılış", "goal": "Doldurma hedefi", "modalTitle": "Doldurma Planı", "none": "yok", "planNumber": "Plan {number}", "preconditionDescription": "Batarya ön ısıtması için kalkıştan önce {duration} doldur.", "preconditionLong": "Geç <PERSON>", "preconditionOptionAll": "hepsi", "preconditionOptionNo": "<PERSON>ı<PERSON>", "preconditionShort": "Geç", "remove": "Kaldır", "repeating": "“tekrarlanan”", "repeatingPlans": "“<PERSON><PERSON><PERSON><PERSON><PERSON> planlar”", "selectAll": "“Tümünü seç”", "time": "Zaman", "title": "Plan", "titleMinSoc": "<PERSON><PERSON><PERSON> do<PERSON>", "titleTargetCharge": "Ayrılış", "unsavedChanges": "Kaydedilmemiş değişiklikler var. Şimdi uygulansın mı?", "update": "<PERSON><PERSON><PERSON><PERSON>", "weekdays": "<PERSON><PERSON><PERSON><PERSON>"}, "energyflow": {"battery": "Batarya", "batteryCharge": "<PERSON><PERSON><PERSON> do<PERSON>", "batteryDischarge": "<PERSON><PERSON><PERSON>", "batteryGridChargeActive": "“şebekeden doldurma etkin”", "batteryGridChargeLimit": "“şebekeden doldur şayet”", "batteryHold": "Batarya (kilitli)", "batteryTooltip": "{total} ({soc})'ın {energy}'ı", "forecastTooltip": "“öngörü: bugün kalan güneşden üreti̇m”", "gridImport": "Şebeke kullanımı", "homePower": "<PERSON><PERSON><PERSON><PERSON>", "loadpoints": "Doldurma cihazı| Doldurma cihazı | {count} doldurma cihazları", "noEnergy": "Ölçüm verisi yok", "pv": "“Güneş enerji siste<PERSON>”", "pvExport": "Şebekeye ihracat", "pvProduction": "Üretim", "selfConsumption": "<PERSON><PERSON> tüketim"}, "heatingStatus": {"charging": "Isıtılıyor…", "connected": "Beklemede.", "vehicleLimit": "“Isıtıcı sınırlaması”", "waitForVehicle": "Ha<PERSON><PERSON>r. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bekleniyor…"}, "loadpoint": {"avgPrice": "⌀ Fiyat", "charged": "<PERSON><PERSON><PERSON>", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON><PERSON>", "fallbackName": "Doldurma noktası", "finished": "“Doldurma sonu”", "power": "<PERSON><PERSON><PERSON>", "price": "Maliyet", "remaining": "<PERSON><PERSON>", "remoteDisabledHard": "{source}: kapatıldı", "remoteDisabledSoft": "{source}: uyumlu güneş enerjili doldurma kapatıldı", "solar": "G<PERSON>neş Enerjisi"}, "loadpointSettings": {"batteryBoost": {"description": "<PERSON>v bataryasından hızlı doldur.", "label": "'<PERSON><PERSON><PERSON>”", "mode": "Sadece GES ve Asg.+GES modunda kullanılabilir.", "once": "Bu doldurma oturumu i<PERSON>in tak<PERSON>ye etkin."}, "batteryUsage": "“Ev Bataryası”", "currents": "Doldurma Akımı", "default": "varsay<PERSON>lan", "disclaimerHint": "Not:", "limitSoc": {"description": "<PERSON><PERSON> bağlandığında kullanılan dolum sınırı.", "label": "Varsayılan dolum sınır"}, "maxCurrent": {"label": "<PERSON><PERSON><PERSON>"}, "minCurrent": {"label": "Asgari Akım"}, "minSoc": {"description": "Araç, güneş enerjisi modunda {0} seviyesine hızlı doldurulur. Ardından güneş enerjisi fazlalığıyla devam eder. Karanlık havalarda dahi asgari bir menzil sağlamak için kullanışlıdır.", "label": "<PERSON><PERSON><PERSON> dolum oranı"}, "onlyForSocBasedCharging": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, sadece doluluk seviyesi bilinen araçlar için açık.", "phasesConfigured": {"label": "Fazlar", "no1p3pSupport": "Doldurma cihazınız nasıl bağlı?", "phases_0": "otomatik geçiş", "phases_1": "1 aşamalı", "phases_1_hint": "({min}'dan {max}'a kadar)", "phases_3": "3 aşamalı", "phases_3_hint": "({min}'dan {max}'a kadar)"}, "smartCostCheap": "<PERSON><PERSON>z <PERSON>", "smartCostClean": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> {0}", "vehicle": "<PERSON><PERSON>"}, "mode": {"minpv": "Asg.+GES", "now": "Hızlı", "off": "<PERSON><PERSON><PERSON>", "pv": "GES", "smart": "Akıllı"}, "provider": {"login": "giri<PERSON> yap", "logout": "çıkış yap"}, "startConfiguration": "“Yapılandırmaya başlayalım”", "targetCharge": {"activate": "Etkinleştir", "co2Limit": "{co2} CO₂ sınırı", "costLimitIgnore": "<PERSON>u zaman aralığında yapılandırılan {limit} yoksayılacak.", "currentPlan": "Etkin plan", "descriptionEnergy": "{targetEnergy} ne zamana kadar araca doldurulmalı?", "descriptionSoc": "<PERSON><PERSON> ne zaman {targetSoc} seviyesine doldurulmalı?", "goalReached": "“Doldurma hedefine ulaşıldı bile”", "inactiveLabel": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "nextPlan": "“<PERSON><PERSON><PERSON> plan”", "notReachableInTime": "<PERSON><PERSON><PERSON><PERSON> zamana {overrun} sonra <PERSON>.", "onlyInPvMode": "Doldurma planı sadece güneş enerjisi modunda çalışır.", "planDuration": "Doldurma s<PERSON>i", "planPeriodLabel": "Zaman aralığı", "planPeriodValue": "{start}'dan {end}'a kadar", "planUnknown": "<PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON>zleme", "priceLimit": "{price} fiyat sınırı", "remove": "Kaldır", "setTargetTime": "yok", "targetIsAboveLimit": "Yapılandırılan {limit} seviyesindeki doldurma sınırı bu zaman aralığında yok sayılacaktır.", "targetIsAboveVehicleLimit": "Araç sınırı doldurma hedefinin altında.", "targetIsInThePast": "Gelecekte bir <PERSON>, <PERSON>.", "targetIsTooFarInTheFuture": "Gelecek hakkında daha fazla bilgi edindiğimizde planı uyarlayacağız.", "title": "<PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>", "tomorrow": "yarın", "update": "<PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "Nasıl yapılandırılacağını öğren.", "vehicleCapacityRequired": "Doldurma süresini tahmin etmek için araç batarya kapasitesi gerekli."}, "targetChargePlan": {"chargeDuration": "Doldurma s<PERSON>i", "co2Label": "⌀ CO₂ emisyonu", "priceLabel": "Enerji fiyatı", "timeRange": "{day} {range} saat", "unknownPrice": "<PERSON><PERSON><PERSON><PERSON>"}, "targetEnergy": {"label": "Sınır", "noLimit": "yok"}, "vehicle": {"addVehicle": "<PERSON><PERSON>", "changeVehicle": "Araçı değiştir", "detectionActive": "<PERSON><PERSON>…", "fallbackName": "<PERSON><PERSON>", "moreActions": "Daha Fazla İşlem", "none": "<PERSON><PERSON>", "notReachable": "Araca ulaşılamadı. Evcc'yi yeniden başlatmayı dene.", "targetSoc": "Doldurma Sınırı", "temp": "Sıcaklık.", "tempLimit": "Hedeflenen sıcaklık", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "vehicleSoc": "Doluluk seviyesi"}, "vehicleStatus": {"awaitingAuthorization": "<PERSON><PERSON> bekliyorum.", "batteryBoost": "<PERSON><PERSON>a ta<PERSON> et<PERSON>.", "charging": "doluyor…", "cheapEnergyCharging": "<PERSON><PERSON>z enerji mevcut.", "cheapEnergyNextStart": "{duration} i<PERSON><PERSON><PERSON> u<PERSON>z enerji.", "cheapEnergySet": "Fiyat sınırı belirlendi.", "cleanEnergyCharging": "<PERSON><PERSON><PERSON> enerji mevcut.", "cleanEnergyNextStart": "{duration} i<PERSON><PERSON><PERSON> temiz enerji.", "cleanEnergySet": "CO₂ sınırı belirlendi.", "climating": "Ön iklimlendirme algılandı.", "connected": "Bağlı.", "disconnectRequired": "Oturum sonlandırıldı. Tekrar bağlan.", "disconnected": "Bağlantı kesildi.", "feedinPriorityNextStart": "<PERSON><PERSON>ksek besleme fiyatları {duration} içinde başlar.", "feedinPriorityPausing": "Beslemeyi azamiye çıkarmak için güneşden doldurma duraklatıldı.", "finished": "Tamamlandı.", "minCharge": "{soc} kadar asgari dolum.", "pvDisable": "Yeterli fazlalık yok. Birazdan duraklatılacak.", "pvEnable": "Fazlalık mevcut. Birazdan başlatılacak.", "scale1p": "Birazdan 1 aşamalı doldurmaya düşürülecek.", "scale3p": "Birazdan 3 aşamalı doldurmaya yükseltilecek.", "targetChargeActive": "Doldurma planı yürürlükte. Tahmini bitiş süresi {duration} içerisinde.", "targetChargePlanned": "Doldurma planı {duration} içerisinde başlayacak.", "targetChargeWaitForVehicle": "Doldurma planı hazır. <PERSON><PERSON>…", "vehicleLimit": "<PERSON>ç sınırı", "vehicleLimitReached": "<PERSON><PERSON> sınır<PERSON>na ula<PERSON>ıldı.", "waitForVehicle": "Doldurmaya hazır. <PERSON><PERSON>…", "welcome": "Bağlantıyı onaylamak için kısa ilk dolum."}, "vehicles": "Park", "welcome": "Ho<PERSON> geldin!"}, "notifications": {"dismissAll": "Bildirimleri kaldır", "logs": "<PERSON>ü<PERSON>ün log<PERSON> görü<PERSON>üle", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "offline": {"configurationError": "Başlatma sırasında hata oluştu. Yapılandırmanı gözden geçir ve yeniden başlat.", "message": "<PERSON> makineye bağlantı yok.", "restart": "<PERSON><PERSON><PERSON> b<PERSON>", "restartNeeded": "Değişiklikleri uygulamak için gerekli.", "restarting": "<PERSON><PERSON><PERSON>."}, "passwordModal": {"description": "Yapılandırma ayarlarını korumak için bir şifre belirle. Ana görünüme erişim oturum açmadan da mümkün.", "empty": "<PERSON><PERSON><PERSON> bo<PERSON>", "error": "Hata: ", "labelCurrent": "<PERSON><PERSON><PERSON>", "labelNew": "<PERSON><PERSON>", "labelRepeat": "<PERSON><PERSON> te<PERSON>a", "newPassword": "<PERSON><PERSON><PERSON>", "noMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "titleNew": "Yönetici Şifresi Oluştur", "titleUpdate": "Yönetici Şifresini Güncelle", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>"}, "session": {"cancel": "İptal", "co2": "CO₂", "date": "Zaman aralığı", "delete": "Sil", "finished": "Bitiş zamanı", "meter": "<PERSON><PERSON><PERSON>", "meterstart": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "meterstop": "<PERSON><PERSON><PERSON>", "odometer": "Kilometre", "price": "<PERSON><PERSON><PERSON>", "started": "Başlama zamanı", "title": "Dold<PERSON><PERSON>"}, "sessions": {"avgPower": "⌀ Güç", "avgPrice": "⌀ Fiyat", "chargeDuration": "<PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Fiyat {byGroup}", "byGroupLoadpoint": "“Doldurma Noktasına Göre”", "byGroupVehicle": "“Araca Göre”", "energy": "“Dolduru<PERSON> En<PERSON>ji”", "energyGrouped": "“Güneşe karşı Şebeke Enerjisi”", "energyGroupedByGroup": "Enerji {byGroup}", "energySubSolar": "“{value} güneş”", "energySubTotal": "{value} toplam", "groupedCo2ByGroup": "CO₂-Miktarı {byGroup}", "groupedPriceByGroup": "“Toplam Maliyet {byGroup}\"", "historyCo2": "“CO₂ Salınımları”", "historyCo2Sub": "{value} toplam", "historyPrice": "“Doldurma Maliyetleri”", "historyPriceSub": "{value} toplam", "solar": "“Yıl İçindeki Güneş Payı”", "solarByGroup": "“Güneş Payı {byGroup}\""}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Enerji (kWh)", "chargeduration": "Doldurma s<PERSON>i", "co2perkwh": "CO₂/kWh", "created": "Başlama zamanı", "finished": "Bitiş zamanı", "identifier": "Tanımlayıcı", "loadpoint": "Doldurma Noktası", "meterstart": "<PERSON><PERSON><PERSON><PERSON> (kWh)", "meterstop": "<PERSON><PERSON><PERSON> (kWh)", "odometer": "Kilometre (km)", "price": "<PERSON><PERSON><PERSON>", "priceperkwh": "Fiyat/kWh", "solarpercentage": "<PERSON><PERSON><PERSON><PERSON> (%)", "vehicle": "<PERSON><PERSON>"}, "csvPeriod": "{period} CSV olarak indir", "csvTotal": "CSV'nin tamamını indir", "date": "Başlangıç", "energy": "Doldurulan", "filter": {"allLoadpoints": "tüm doldurma noktaları", "allVehicles": "<PERSON><PERSON><PERSON>", "filter": "Filtrele"}, "group": {"co2": "Salınım", "grid": "“Şebeke”", "price": "<PERSON><PERSON><PERSON>", "self": "<PERSON><PERSON><PERSON>ş"}, "groupBy": {"loadpoint": "Doldurma noktası", "none": "Toplam", "vehicle": "<PERSON><PERSON>"}, "loadpoint": "Doldurma Noktası", "noData": "Bu ay henüz doldurma oturumu yok.", "overview": "“Genel Bakış”", "period": {"month": "Ay", "total": "Toplam", "year": "<PERSON><PERSON><PERSON>"}, "price": "Maliyet", "reallyDelete": "Bu oturumu gerçekten silmek istiyor musun?", "showIndividualEntries": "“Bireysel oturumları göster”", "solar": "G<PERSON>neş Enerjisi", "title": "Doldurma Oturumları", "total": "Toplam", "type": {"co2": "CO₂", "price": "<PERSON><PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>ş"}, "vehicle": "<PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Tam ekrana geç", "exit": "Tam ekrandan çık", "label": "<PERSON>"}, "hiddenFeatures": {"label": "Den<PERSON>sel", "value": "Deneysel kullanıcı arayüzü özelliklerini göster."}, "language": {"auto": "Otomatik", "label": "Dil"}, "sponsorToken": {"expires": "Sponsor jetonun {inXDays} sonra sona erecek. {getNewToken} ve burada güncelle.", "getNew": "<PERSON>ni bir tane al", "hint": "Not: İleride bunu otomatik hale getireceğiz."}, "telemetry": {"label": "Uzölçüm"}, "theme": {"auto": "sistem", "dark": "Karanlık", "label": "G<PERSON>rü<PERSON><PERSON><PERSON>", "light": "Aydınlık"}, "time": {"12h": "12saat", "24h": "24saat", "label": "Saat biçimi"}, "title": "<PERSON><PERSON>", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON>", "mi": "<PERSON>l"}}, "smartCost": {"activeHours": "{total} saat içinde {active}", "activeHoursLabel": "<PERSON><PERSON><PERSON>", "applyToAll": "<PERSON><PERSON><PERSON> uygulan<PERSON>ın mı?", "batteryDescription": "<PERSON>v ener<PERSON> de<PERSON> do<PERSON>.", "cheapTitle": "Ucuz Şebeke Doldurması", "cleanTitle": "<PERSON><PERSON>z <PERSON>ur<PERSON>", "co2Label": "CO₂ emisyonu", "co2Limit": "CO₂ sınırı", "loadpointDescription": "Güneş enerjisi modunda hızlı doldurmayı geçici olarak etkinleştirir.", "modalTitle": "Akıllı Şebeke Doldurması", "none": "yok", "priceLabel": "Enerji fiyatı", "priceLimit": "Fiyat sınırı", "resetAction": "“Sınırlamayı kaldır“", "resetWarning": "Dinamik şebeke fiyatı veya yapılandırılmış CO₂ kaynağı yok. <PERSON><PERSON><PERSON>, hala {limit} sınırlaması var. Yapılandırmayı toparlayayım mı?", "saved": "<PERSON><PERSON><PERSON><PERSON>."}, "smartFeedInPriority": {"activeHoursLabel": "Duraklatılmış saatler", "description": "Kârlı şebeke beslemesine öncelik vermek için yüksek fiyatlar sırasında doldurmayı durdurur.", "priceLabel": "Besleme fiyatı", "priceLimit": "Besleme sınırı", "resetWarning": "Dinamik besleme fiyatı yapılandırılmadı. <PERSON><PERSON><PERSON>, hâlâ {limit} sınırı var. Yap<PERSON>landırmanı temizlemek ister misin?", "title": "Beslemeyi Ö<PERSON>lendir"}, "startupError": {"configFile": "Kullanılan yapılandırma dosyası:", "configuration": "Yapılandırma", "description": "Lütfen yapılandırma dosyanı kontrol et. Hata mesajı yardımcı olmuyorsa, çözüm için {0}'na göz at.", "discussions": "GitHub Tartışmaları", "fixAndRestart": "Lüt<PERSON> sorunu düzelt ve ana makineyi yeniden ba<PERSON>lat.", "hint": "Not: <PERSON><PERSON><PERSON><PERSON> hatalı bir cihaz da (<PERSON><PERSON><PERSON>, say<PERSON><PERSON>, <PERSON>) sebeb olabilir. <PERSON><PERSON> bağlantılarını gözden geçir.", "lineError": "{0} i<PERSON><PERSON><PERSON> hata bulundu.", "lineErrorLink": "{0}. satır", "restartButton": "<PERSON><PERSON><PERSON>", "title": "Başlama Hatası"}}