{"batterySettings": {"batteryLevel": "مستوى البطارية", "bufferStart": {"full": "عندما أوشكت على الامتلاء", "never": "فقط مع فائض كافٍ"}, "legendBottomName": "أولوية المنزل", "legendBottomSubline": "لا تستخدم للشحن", "legendMiddleName": "السيارة أولا", "legendMiddleSubline": "المنزل الثاني", "legendTopAutostart": "يبدأ تلقائيًا", "legendTopName": "بطارية تدعم الشحن", "legendTopSubline": "بدون انقطاع", "modalTitle": "إعدادات البطارية"}, "config": {"form": {"example": "مثال", "optional": "اختياري"}, "main": {"addVehicle": "اض<PERSON>ر<PERSON>ة", "edit": "عدّل", "title": "الإعدادات", "vehicles": "عرباتي"}, "validation": {"failed": "<PERSON>خ<PERSON>ق", "label": "الحالة", "running": "التحقق من صحة...", "success": "ناجح", "unknown": "غير معروف", "validate": "تحقق من الصحة"}, "vehicle": {"cancel": "الغاء", "delete": "ا<PERSON><PERSON><PERSON> عربة"}}, "footer": {"community": {"greenEnergy": "شمسي", "greenEnergySub1": "مشحونة بـ evcc", "greenEnergySub2": "منذ أكتوبر ٢٠٢٢", "greenShare": "حصة الطاقة الشمسية", "greenShareSub1": "القوة المقدمة من", "greenShareSub2": "تخزين الطاقة الشمسية وتخزين البطارية", "power": "قوة الشحن", "powerSub1": "{activeClients} من إجمالي {totalClients} مشاركين", "powerSub2": "الشحن ...", "tabTitle": "مجتمع مباشر"}, "savings": {"footerLong": "{percent} من الطاقة الشمسية", "footerShort": "{percent} شمسي\""}}}