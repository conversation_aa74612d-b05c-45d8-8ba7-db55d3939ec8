{"batterySettings": {"batteryLevel": "Nabití baterie", "bufferStart": {"above": "k<PERSON><PERSON> je nad {soc}.", "full": "k<PERSON><PERSON> je na {soc}.", "never": "pouze když je dost přeb<PERSON>ů."}, "capacity": "{energy} z {total}", "control": "Ovládání baterie", "discharge": "Zabrání vybití baterie FVE v okamžitém režimu a během naplánovaného nabíjení.", "disclaimerHint": "Poznámka:", "disclaimerText": "Tato nastavení jsou platná pouze v solárním režimu. Chování dobíjení se upraví dle potřeby.", "gridChargeTab": "Nabíjení z distribuční sítě", "legendBottomName": "Upřednostnit nabíjení baterie FVE", "legendBottomSubline": "dokud ne<PERSON><PERSON>e hodnoty {soc}.", "legendMiddleName": "Upřednostnit nabíjení vozidla", "legendMiddleSubline": "k<PERSON><PERSON> je baterie domu nad {soc}.", "legendTopAutostart": "Spustí <PERSON>ky", "legendTopName": "Nabíjení vozidla s využitím baterie domu", "legendTopSubline": "k<PERSON><PERSON> je baterie domu nad {soc}.", "modalTitle": "Nastavení baterie", "usageTab": "Využití baterie"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> př<PERSON>působuje svou spotřebu podle dostupného přeb<PERSON>ku (například chytré bojlery). evcc předpokládá, že toto zařízení v případě potřeby sníží svůj odběr.", "titleAdd": "Přidat samoregulační spotř<PERSON>č", "titleEdit": "Upravit nastavení chytrého <PERSON>ř<PERSON>če"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON><PERSON>i"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> na<PERSON>", "titleEdit": "Upravi<PERSON> na<PERSON>"}, "charger": {"chargers": "EV na<PERSON>ky", "generic": "Obecné integrace", "heatingdevices": "Zařízení pro vytápění", "ocppHelp": "Zkopíruj tuto adresu do konfiguračního nástroje nabíječky.", "ocppLabel": "OCPP-Server URL", "switchsockets": "Přepínatelné zásuvky", "template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleAdd": {"charging": "Přidat nabíječku", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)"}, "titleEdit": {"charging": "Upravit nabíječku", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)"}, "type": {"custom": {"charging": "Vlastní konfigurace <PERSON>", "heating": "Vlastn<PERSON> konfigu<PERSON> kotle (topení)"}, "heatpump": "Vlastní konfigurace tepelného čerpadla", "sgready": "Vlastní konfigurace te<PERSON> (SG Ready, All)", "sgready-boost": "Vlastní konfigurace <PERSON> (SGReady, Boost)", "switchsocket": "Vlastní konfigurace ch<PERSON> z<PERSON>"}}, "circuits": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že součet všech nabíjecích bodů připojených k danému okruhu nepřekročí nastavené limity výkonu a proudu. Okruhy lze vnořovat a vytvářet tak hierarchii.", "title": "Řízení zát<PERSON><PERSON>e"}, "control": {"description": "<PERSON><PERSON><PERSON><PERSON> jsou výchozí hodnoty správné. Měňte je jen pokud víte, co děláte.", "descriptionInterval": "Aktualizační cyklus regulační smyčky v sekundách. Určuje, jak často evcc čte data z měřičů, upravuje nabíjecí výkon a aktualizuje uživatelské rozhraní. Krátk<PERSON> interval<PERSON> (< 30s) mohou způsobit oscilace a nežádoucí chování.", "descriptionResidualPower": "Posouvá pracovní bod regulační s<PERSON>. Pokud máte do<PERSON>á<PERSON>í baterii, doporučuje se nastavit hodnotu 100 W. Tím získá baterie mírnou prioritu před spotřebou ze sítě.", "labelInterval": "Interval aktualizace", "labelResidualPower": "Zbytkový výkon", "title": "Chován<PERSON> sys<PERSON>"}, "deviceValue": {"amount": "Množství", "broker": "Broker", "bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (bucket)", "capacity": "<PERSON><PERSON><PERSON><PERSON>", "chargeStatus": "Stav", "chargeStatusA": "nepřip<PERSON>je<PERSON>", "chargeStatusB": "připojeno", "chargeStatusC": "Nabíjení", "chargeStatusE": "žádný výkon", "chargeStatusF": "chyba", "chargedEnergy": "Nabito", "co2": "CO₂ ze sítě", "configured": "Nastaveno", "controllable": "Ovládatelné", "currency": "Měna", "current": "<PERSON><PERSON>", "currentRange": "<PERSON><PERSON>", "enabled": "Zapnuto", "energy": "Energie", "feedinPrice": "Cena za výkup energie", "gridPrice": "Cena ze sítě", "heaterTempLimit": "Limit topen<PERSON>", "hemsType": "System", "identifier": "RFID identifikátor", "no": "ne", "odometer": "Ujetých km", "org": "Organizace", "phaseCurrents": "Proud L1, L2, L3", "phasePowers": "Výkon L1, L2, L3", "phaseVoltages": "Napětí L1, L2, L3", "phases1p3p": "Přepínač fází", "power": "<PERSON>ý<PERSON>", "powerRange": "<PERSON>ý<PERSON>", "range": "<PERSON><PERSON><PERSON><PERSON>", "singlePhase": "Jednofázové", "soc": "Úroveň nabití", "solarForecast": "Solární <PERSON>v<PERSON>", "temp": "<PERSON><PERSON><PERSON><PERSON>", "topic": "<PERSON><PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "<PERSON><PERSON> voz<PERSON>", "yes": "ano"}, "deviceValueChargeStatus": {"A": "A (nepřipojeno)", "B": "B (připojeno)", "C": "C (nabíjení)"}, "devices": {"auxMeter": "Chytrý <PERSON>ř<PERSON>", "batteryStorage": "Bat<PERSON><PERSON><PERSON>", "solarSystem": "Fotovoltaický systém"}, "editor": {"loading": "Načítám editor YAML…"}, "eebus": {"description": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>ňuje evcc komunikovat s dalšími EEBus zařízeními.", "title": "EEBus"}, "ext": {"description": "Lze použít pro správu zatížení nebo pro statistické účely.", "titleAdd": "Přidat externí <PERSON>", "titleEdit": "Upravit extern<PERSON>"}, "form": {"danger": "Nebezpečí", "deprecated": "zastara<PERSON>", "example": "Příklad", "optional": "volitelné"}, "general": {"cancel": "Zrušit", "customHelp": "Vytvořit zařízení definované uživatelem s použitím EVCC plugin rozhraní.", "customOption": "Zařízení nastavené uživatelem", "delete": "<PERSON><PERSON><PERSON><PERSON>", "docsLink": "Další informace jsou uvedeny v dokumentaci.", "experimental": "Experimentální nastavení", "hideAdvancedSettings": "Skrýt pokročilá nastavení", "invalidFileSelected": "Zvolen neplatný soubor", "noFileSelected": "Nebyl zvolen žádný soubor.", "off": "vypnuto", "on": "zapnuto", "password": "He<PERSON><PERSON>", "readFromFile": "Načíst ze souboru", "remove": "<PERSON><PERSON><PERSON><PERSON>", "save": "Uložit", "selectFile": "<PERSON><PERSON><PERSON><PERSON> soubor", "showAdvancedSettings": "Zobrazit pokročilá nastavení", "telemetry": "Telemetrie", "templateLoading": "Načítání...", "title": "<PERSON><PERSON><PERSON><PERSON>", "validateSave": "Ověřit a uložit"}, "grid": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sítě", "titleAdd": "Přida<PERSON> měř<PERSON>č spotřeby ze sítě", "titleEdit": "Editovat mě<PERSON><PERSON>č spotřeby ze sítě"}, "hems": {"description": "Umožňuje propojení evcc s dalším systémem pro správu energie v domácnosti.", "title": "HEMS"}, "icon": {"change": "Změnit"}, "influx": {"description": "Zapisuje údaje o nabíjení a další metriky do InfluxDB. K vizualizaci dat použijte Grafanu nebo jiné nástroje.", "descriptionToken": "Podívejte se do dokumentace InfluxDB, kde se dozvíte, jak ji vytvořit: https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (bucket)", "labelCheckInsecure": "Povolit self-signed certifik<PERSON><PERSON>", "labelDatabase": "<PERSON><PERSON><PERSON><PERSON>", "labelInsecure": "Ověření certifikátu", "labelOrg": "Organizace", "labelPassword": "He<PERSON><PERSON>", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Uživatelské jméno", "title": "InfluxDB", "v1Support": "Potřebujete podporu pro InfluxDB verze 1.x?", "v2Support": "Zpět na InfluxDB verze 2.x"}, "loadpoint": {"addCharger": {"charging": "Přidat nabíječku", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)"}, "addMeter": "Přidat sa<PERSON>atný měřič energie", "cancel": "Zrušit", "chargerError": {"charging": "Konfigurace <PERSON> je povinná.", "heating": "Je třeba nakonfigurovat kotel (topení)."}, "chargerLabel": {"charging": "Nabíječka", "heating": "Kotel (topení)"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Použije se nastavení proudu od 6 do 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Použije se nastavení proudu od 6 do 32 A.", "chargerPowerCustom": "Ostatní", "chargerPowerCustomHelp": "Definujte vlastní r<PERSON> (A) připojeného zařízení.", "chargerTypeLabel": "Typ <PERSON>", "chargingTitle": "Chování", "circuitHelp": "Řízení zátěže zajišťující nepřekročení limitů proudu a výkonu.", "circuitLabel": "Elektrický okruh", "circuitUnassigned": "nepřiřazeno", "defaultModeHelp": {"charging": "<PERSON><PERSON><PERSON> nabíjení po připojení vozidla.", "heating": "Výchozí režim při zapnutí systému."}, "defaultModeHelpKeep": "<PERSON>ová naposledy zvolený režim.", "defaultModeLabel": "Výchozí <PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "electricalSubtitle": "Pokud <PERSON>, konzultujte se svým elektrikářem.", "electricalTitle": "Elektrické", "energyMeterHelp": "Doplňkov<PERSON> mě<PERSON>, pokud nabíječka nemá integrovaný.", "energyMeterLabel": "Elektroměr", "estimateLabel": "Odhadovat úroveň nabití vozu mezi aktualizacemi z API", "maxCurrentHelp": "Mus<PERSON> být větš<PERSON> než nastavený minimální proud.", "maxCurrentLabel": "Maxim<PERSON>ln<PERSON> proud", "minCurrentHelp": "Nastavení nižší než 6 A využijte pouze, pokud víte, co děláte.", "minCurrentLabel": "Minimální proud", "noVehicles": "<PERSON><PERSON><PERSON> v<PERSON>.", "option": {"charging": "Přidat místo pro nabíjení", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)"}, "phases1p": "1 fáze", "phases3p": "3 fáze", "phasesAutomatic": "Automatické p<PERSON>ínání fází", "phasesAutomaticHelp": "Vaše nabíječka podporuje automatické přepínání mezi 1-fázovým a 3-fázovým nabíjením. Na hlavní obrazovce můžete během nabíjení upravit chování fází.", "phasesHelp": "Počet připojených fází.", "phasesLabel": "<PERSON><PERSON><PERSON>", "pollIntervalDanger": "Pravidelné dotazování na stav vozidla může vybíjet jeho baterii. Někteří výrobci vozidel mohou v takovém případě aktivně bránit nabíjení. Nedoporučuje se! Použijte pouze tehdy, pokud si jste vědomi rizik.", "pollIntervalHelp": "Interval mezi aktualizacemi z API vozidla. Krátké intervaly mohou vybíjet baterii vozidla.", "pollIntervalLabel": "Interval aktualizace", "pollModeAlways": "vždy", "pollModeAlwaysHelp": "Vždy požadovat aktualizace stavu v pravidelných intervalech.", "pollModeCharging": "nabíjení", "pollModeChargingHelp": "Požadovat aktualizace stavu vozidla ze serveru pouze během nabíjení.", "pollModeConnected": "připojeno", "pollModeConnectedHelp": "Aktualizovat stav vozidla v pravidelných intervalech, pokud je připojeno.", "pollModeLabel": "Chování aktualizace", "priorityHelp": "Zařízení s vyšší prioritou mají přednostní přístup k solárním přebytkům.", "priorityLabel": "Priorita", "save": "Uložit", "showAllSettings": "Zobrazit všechna nastavení", "solarBehaviorCustomHelp": "Definujte vlastní prahové hodnoty a zpoždění pro zapnutí a vypnutí.", "solarBehaviorDefaultHelp": "Spustí se poté co jsou solární přebytky k dispozici po {enableDelay}. Zastaví se, pokud přebytek není k dispozici po dobu {disableDelay}.", "solarBehaviorLabel": "<PERSON><PERSON><PERSON>", "solarModeCustom": "vlastní", "solarModeMaximum": "maximum solární energie", "thresholdDisableDelayLabel": "Zpoždění vypnutí", "thresholdDisableHelpInvalid": "Použijte prosím kladnou hodnotu.", "thresholdDisableHelpPositive": "<PERSON><PERSON><PERSON><PERSON>, pokud je ze sítě odeb<PERSON><PERSON><PERSON><PERSON> v<PERSON>ce ne<PERSON> {power} po dobu {delay}.", "thresholdDisableHelpZero": "<PERSON><PERSON><PERSON><PERSON>, pokud nelze po dobu {delay} zajistit minimální požadovaný výkon.", "thresholdDisableLabel": "Vypnout napájení ze sítě", "thresholdEnableDelayLabel": "Zpoždění z<PERSON>nut<PERSON>", "thresholdEnableHelpInvalid": "Použijte prosím zápornou hodnotu.", "thresholdEnableHelpNegative": "<PERSON><PERSON><PERSON><PERSON>, pokud je k dispozici přebytek {surplus} po dobu {delay}.", "thresholdEnableHelpZero": "<PERSON><PERSON><PERSON><PERSON>, pokud je k dispozici přebytek odpovídající minimálnímu požadovanému výkonu po dobu {delay}.", "thresholdEnableLabel": "Povolit napájení ze sítě", "titleAdd": {"charging": "Přidat místo pro nabíjení", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)", "unknown": "Přidat nabíječku nebo kotel (topení)"}, "titleEdit": {"charging": "Upravit nabí<PERSON>í bod", "heating": "<PERSON><PERSON><PERSON><PERSON> kotel (topení)", "unknown": "Upravit nabíječku / kotel (topení)"}, "titleExample": {"charging": "G<PERSON><PERSON><PERSON>, Dvorek, apod.", "heating": "<PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON>, apod."}, "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "vehicleAutoDetection": "<PERSON><PERSON><PERSON> dete<PERSON>", "vehicleHelpAutoDetection": "Automaticky vybere nejpravděpodobnější vozidlo. Ruční přepsání je možné.", "vehicleHelpDefault": "<PERSON>ž<PERSON> se předpokládá, že se na tomto místě nabíjí toto vozidlo. Automatická detekce je vypnuta. Vozidlo lze ručně změnit.", "vehicleLabel": "Výchozí vozidlo", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "addGrid": "Přida<PERSON> měř<PERSON>č spotřeby ze sítě", "addLoadpoint": "Přidejte nabíječku nebo kotel (topení)", "addPvBattery": "Přidat solar nebo baterii", "addTariffs": "Přidat tarify", "addVehicle": "<PERSON><PERSON><PERSON><PERSON> vozidlo", "configured": "nastaveno", "edit": "editace", "loadpointRequired": "Musíte nastavit alespoň jeden nabíjecí bod.", "name": "Jméno", "title": "Konfigurace", "unconfigured": "nen<PERSON>", "vehicles": "<PERSON><PERSON> vozidla", "yaml": "Zařízení z evcc.yaml nelze upravovat."}, "messaging": {"description": "Dostávejte notifikace o průběhu nabíjení.", "title": "Oznámení"}, "meter": {"cancel": "Zrušit", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "Obecné integrace", "option": {"aux": "Přidat samoregulační spotř<PERSON>č", "battery": "<PERSON><PERSON><PERSON><PERSON> baterie", "ext": "Přidat externí <PERSON>", "pv": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>č solární energie"}, "save": "Uložit", "specific": "Specifické integrace", "template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleChoice": "Co chcete přidat?", "validateSave": "Zkontrolovat a uložit"}, "modbus": {"baudrate": "Rychlost", "comset": "KomSet", "connection": "Modbus připojení", "connectionHintSerial": "Zařízení je přímo připojeno k evcc přes rozhraní RS485.", "connectionHintTcpip": "Zařízení je adresovatelné z evcc přes LAN/Wifi.", "connectionValueSerial": "Sériový / KABELA", "connectionValueTcpip": "Síť", "device": "Název zařízení", "deviceHint": "Příklad: / dev / ttyUSB0", "host": "IP adresa nebo název hostitele", "hostHint": "Příklad: *********", "id": "Modbus IDY", "port": "Porto", "protocol": "Modbus protokol", "protocolHintRtu": "Připojení přes adaptér RS485 na Ethernet bez překladu protokolu.", "protocolHintTcp": "Zařízení má nativní podporu LAN / Wifi nebo je připojeno přes adaptér RS485 na Ethernet s překladem protokolu.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Povolit více klientům přístup k jednomu Modbus zařízení.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Autentifikace", "description": "Připojte se k MQTT brokeru pro výměnu dat s jinými systémy ve vaší síti.", "descriptionClientId": "Autor zpráv. <PERSON>kud je pole prázdné, použije se evcc-[rand].", "descriptionTopic": "Nechte prázdné pro deaktivaci publikování.", "labelBroker": "Broker", "labelCaCert": "<PERSON>rt<PERSON><PERSON><PERSON><PERSON> (CA)", "labelCheckInsecure": "Povolit self-signed certifik<PERSON><PERSON>", "labelClientCert": "Certifik<PERSON><PERSON>", "labelClientId": "ID klienta", "labelClientKey": "<PERSON><PERSON><PERSON><PERSON>", "labelInsecure": "Ověření certifikátu", "labelPassword": "He<PERSON><PERSON>", "labelTopic": "<PERSON><PERSON><PERSON>", "labelUser": "Uživatelské jméno", "publishing": "Publikování", "title": "MQTT"}, "network": {"descriptionHost": "Použijte příponu .local pro povolení mDNS. Relevance pro detekci mobilní aplikace a některých OCPP nabíječek.", "descriptionPort": "Port pro webové rozhraní a API. Pokud tuto hodnotu změníte, budete muset aktualizovat URL ve vašem prohlížeči.", "descriptionSchema": "Ovlivňuje pouze způsob generování URL adres. Výběr HTTPS nezajistí šifrování.", "labelHost": "Název hostitele", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON><PERSON>", "title": "Síť"}, "options": {"boolean": {"no": "ne", "yes": "ano"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "Vytápění", "standby": "Pohotovostní <PERSON>"}, "schema": {"http": "HTTP (nešifrované)", "https": "HTTPS (šifrované)"}, "status": {"A": "A (nepřipojeno)", "B": "B (připojeno)", "C": "C (nabíjení)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>č solární energie", "titleEdit": "<PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> solární energie"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON>", "general": "Obecné", "grid": "Elektrická síť", "integrations": "Integrace", "loadpoints": "Nabíjení a kotle (topení)", "meter": "Solár a baterie", "system": "Systém", "vehicles": "<PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "changeToken": "Změnit token", "description": "Sponzorský model n<PERSON><PERSON> pomáhá udržovat projekt a udržitelně vyvíjet nové a vzrušující funkce. Jako sponzor získáte přístup ke všem implementacím nabíječek.", "descriptionToken": "Token zís<PERSON>áte na {url}. Nabízíme tak<PERSON> z<PERSON>í token pro testování.", "error": "Sponzorský token nen<PERSON> p<PERSON>.", "labelToken": "Sponzorský token", "title": "Sponzorství", "tokenRequired": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> tohoto zařízení musíte nakonfigurovat sponzorský token.", "tokenRequiredLearnMore": "Zjistit více.", "tokenRequiredShort": "<PERSON><PERSON><PERSON><PERSON><PERSON> sponz<PERSON> token.", "trialToken": "Zkušební token"}, "system": {"backupRestore": {"backup": {"action": "St<PERSON>hnout zálohu...", "confirmationButton": "St<PERSON>hn<PERSON> zálohu", "confirmationText": "Prosím, zadejte své heslo pro stažení databázového souboru.", "description": "Zálohuje vaši konfiguraci do souboru. Tento soubor může být později použit pro obnovu, např. v případě selhání systému.", "title": "Z<PERSON>loha"}, "cancel": "Zrušit", "description": "Umožňuje zálohu a obnovení dat či provedení kompletního smazání a resetu systému. Může se hodit, k<PERSON><PERSON> chcete svá data přesunout do jiného systému.", "note": "Poznámka: Všechny akce uvedené výše ovlivní jen data v databázi. Data v evcc.yaml souboru zůstanou nedotčena.", "reset": {"action": "Resetovat systém...", "confirmationButton": "Resetovat a restartovat", "confirmationText": "Dočasně smaže Vámi vybraná data. Ujistě<PERSON> se, že jste prvně provedli z<PERSON> dat.", "description": "Máte problémy a chcete začít znovu? Můžete smazat data a pustit se znovu do nastavování.", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace", "sessionsDescription": "Smaže veškerou historii nabíjení.", "settings": "Konfigurace a nastavení", "settingsDescription": "Smaže veškerá nastavená zařízení, služby apod.", "title": "Reset"}, "restore": {"action": "Obnovit zálohu...", "confirmationButton": "Obnovit a restartovat", "confirmationText": "Přep<PERSON>še celou vaši databázi. Ujist<PERSON><PERSON> se, že jste prvně <PERSON>li z<PERSON> dat.", "description": "Obnoví vaše data ze zálohy. Vaše současná data budou přepsána.", "labelFile": "<PERSON><PERSON><PERSON>", "title": "Obnovit"}, "title": "Zálohování a obnova"}, "logs": "<PERSON><PERSON>", "restart": "<PERSON><PERSON><PERSON><PERSON>", "restartRequiredDescription": "<PERSON>by se změny projevily, je třeba restartovat sys<PERSON>m.", "restartRequiredMessage": "Konfigurace by<PERSON>.", "restartingDescription": "Prosím <PERSON>…", "restartingMessage": "Restartování evcc."}, "tariffs": {"description": "Definujte své energetické tarify pro výpočet nákladů na nabíjecí relace.", "title": "Tarify"}, "title": {"description": "Zobrazeno na hlavní obrazovce a kartě prohlížeče.", "label": "<PERSON><PERSON><PERSON><PERSON>", "title": "Upravi<PERSON>"}, "validation": {"failed": "sel<PERSON>o", "label": "Stav", "running": "Ověřování…", "success": "úspěch", "unknown": "neznámý", "validate": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "Zrušit", "chargingSettings": "Nastavení nabíjení", "defaultMode": "Výchozí <PERSON>", "defaultModeHelp": "<PERSON><PERSON><PERSON> nabíjení po připojení vozidla.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "Ostatní integrace", "identifiers": "RFID identifikátory", "identifiersHelp": "Seznam RFID řetězců pro identifikaci vozidla. Jeden záznam na řádek. Aktuální identifikátor naleznete na příslušném nabíjecím bodě na stránce přehledu.", "maximumCurrent": "Maxim<PERSON>ln<PERSON> proud", "maximumCurrentHelp": "Musí být vět<PERSON><PERSON> než minimální proud.", "maximumPhases": "Maximální počet fází", "maximumPhasesHelp": "Kolik fází může toto vozidlo využít pro nabíjení? Používá se k výpočtu požadovaného minimálního solárního přebytku a doby plánování.", "minimumCurrent": "Minimální proud", "minimumCurrentHelp": "Pod 6 A jděte pouze tehdy, pokud v<PERSON><PERSON>, co <PERSON><PERSON><PERSON><PERSON>.", "online": "Vozidlo s online API", "primary": "Obecné integrace", "priority": "Priorita", "priorityHelp": "Vyšší priorita znamená, že toto vozidlo má přednostní přístup k solárnímu přebytku.", "save": "Uložit", "scooter": "Skůtr", "template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleAdd": "<PERSON><PERSON><PERSON><PERSON> vozidlo", "titleEdit": "Upravit vozidlo", "validateSave": "Ověřit a uložit"}}, "footer": {"community": {"greenEnergy": "<PERSON><PERSON><PERSON>", "greenEnergySub1": "nabito s evcc", "greenEnergySub2": "od října 2022", "greenShare": "Solární sdílení", "greenShareSub1": "energie poskytnuta", "greenShareSub2": "so<PERSON><PERSON><PERSON><PERSON>, a bateriové úložiště", "power": "Dobíjecí energie", "powerSub1": "{activeClients} z {totalClients} účastníků", "powerSub2": "p<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "tabTitle": "Komunita Live"}, "savings": {"co2Saved": "{value} ul<PERSON><PERSON><PERSON>", "co2Title": "CO₂ emise", "configurePriceCo2": "Zjistit jak nastavit cenu a data CO₂.", "footerLong": "{percent} solární energie", "footerShort": "{percent} sol<PERSON>r", "modalTitle": "Přehled energie", "moneySaved": "{value} ul<PERSON><PERSON><PERSON>", "percentGrid": "{grid} kWh síť", "percentSelf": "{self} kWh solár", "percentTitle": "Solární energie", "period": {"30d": "posledních 30 dní", "365d": "posledních 365 dní", "thisYear": "tento rok", "total": "celkem"}, "periodLabel": "Opakování:", "priceTitle": "<PERSON><PERSON> ener<PERSON>", "referenceGrid": "síť", "referenceLabel": "Referenční data:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "Staň se sponzorem", "becomeSponsorExtended": "Podpořte nás přímo a získejte samolepky.", "confetti": "Připraven na konfety?", "confettiPromise": "Budou nálepky a digitální konfety", "sticker": "... nebo evcc n<PERSON>?", "supportUs": "Naším cílem je udělat ze solárního nabíjení standard. Podpořte evcc částkou, která odpovídá jeho hodnotě pro vás.", "thanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {sponsor}! Tvůj příspěvěk pomáhá vyvíjet evcc i nadále.", "titleNoSponsor": "Podpořte nás", "titleSponsor": "Jste podporovatel", "titleTrial": "Zkušeb<PERSON><PERSON>", "titleVictron": "Sponzorováno společností Victron Energy", "trial": "Jste v zkušebním režimu a můžete používat všechny funkce. Prosím, zvažte podporu projektu.", "victron": "Používáte evcc na hardwaru Victron Energy a máte přístup ke všem funkcím."}, "telemetry": {"optIn": "Chci přispět svými daty.", "optInMoreDetails": "<PERSON><PERSON><PERSON> informací {0}.", "optInMoreDetailsLink": "zde", "optInSponsorship": "Vyžadován sponzorský token."}, "version": {"availableLong": "nová verze je k dispozici", "modalCancel": "Zrušit", "modalDownload": "<PERSON><PERSON><PERSON><PERSON>", "modalInstalledVersion": "Instalovaná verze", "modalNoReleaseNotes": "Žádné bližší informace k vydání nejsou k dispozici. Více informací o nové verzi:", "modalTitle": "Nová verze nalezena", "modalUpdate": "Instalovat", "modalUpdateNow": "Instalovat nyní", "modalUpdateStarted": "Spouštění nové verze EVCC…", "modalUpdateStatusStart": "Instalace spuštěna:"}}, "forecast": {"co2": {"average": "Pr<PERSON><PERSON>ě<PERSON>", "lowestHour": "Nejč<PERSON><PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON><PERSON>"}, "modalTitle": "Předpověď", "price": {"average": "Pr<PERSON><PERSON>ě<PERSON>", "lowestHour": "Nejlevnějš<PERSON> hodina", "range": "<PERSON><PERSON><PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "Pozítří", "partly": "Částečně", "remaining": "zbývající", "today": "Dnes", "tomorrow": "<PERSON>í<PERSON>"}, "solarAdjust": "Upravit solární předpověď na základě skutečných výrobních dat {percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}}, "header": {"about": "O", "authProviders": {"confirmLogout": "Opravdu chcete odpojit {title}?", "title": "Stav autorizace"}, "blog": "Blog", "docs": "Dokumentace", "github": "GitHub", "login": "Přihlašovací údaje k vozidlu", "logout": "Odhlásit se", "nativeSettings": "<PERSON><PERSON><PERSON><PERSON>", "needHelp": "Potřebujete poradit?", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace"}, "help": {"discussionsButton": "GitHub diskuse", "documentationButton": "Dokumentace", "issueButton": "Nahlásit bug", "issueDescription": "<PERSON><PERSON><PERSON> jste podivné nebo nesprávné ch<PERSON>ní?", "logsButton": "Zobrazit protokoly", "logsDescription": "Zkontrolujte protokoly na chyby.", "modalTitle": "Protřebujete pomoc?", "primaryActions": "<PERSON><PERSON><PERSON>, jak má? <PERSON><PERSON>e jsou do<PERSON>r<PERSON> m<PERSON>, kde získat pomoc.", "restart": {"cancel": "Zrušit", "confirm": "Ano, restartovat!", "description": "<PERSON>a normálních okolností by restartování ne<PERSON> být nutn<PERSON>. Pokud musíte pravidelně restartovat evcc, zvažte prosím nahlášení chyby.", "disclaimer": "Poznámka: evcc se ukončí a spoléhá na to, že operační systém restartuje službu evcc automaticky.", "modalTitle": "Opravdu chcete restartovat?"}, "restartButton": "<PERSON><PERSON><PERSON><PERSON>", "restartDescription": "<PERSON><PERSON><PERSON> jste to restartovat?", "secondaryActions": "<PERSON><PERSON><PERSON> se vám nedaří vyřešit váš problém? Zde jsou některé další možnosti."}, "log": {"areaLabel": "Filtruj podle oblasti", "areas": "Všechny oblasti", "download": "Stáhnout kompletní protokol", "levelLabel": "Filtruj podle úrovně protokolu", "nAreas": "{count} oblastí", "noResults": "Žádné odpovídající záznamy v protokolu.", "search": "Hledat", "selectAll": "v<PERSON><PERSON>t vše", "showAll": "Zobrazit všechny záznamy", "title": "Protokoly", "update": "Automatická aktualizace"}, "loginModal": {"cancel": "Zrušit", "demoMode": "Přihlášení není dostupné v DEMO režimu.", "error": "Přihlášení neúspěšné: ", "iframeHint": "Otevřít evcc v novém panelu.", "iframeIssue": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>lo je správn<PERSON>, ale zdá se, že v<PERSON><PERSON>č ztratil autentifikační cookie. K tomu mů<PERSON>, pokud spouštíte evcc v iframe přes HTTP.", "invalid": "<PERSON><PERSON><PERSON> je nep<PERSON>.", "login": "Přihlásit", "password": "Administrá<PERSON><PERSON><PERSON>", "reset": "Obnovit hes<PERSON>?", "title": "Přihlášení"}, "main": {"chargingPlan": {"active": "Aktivní", "addRepeatingPlan": "Přidat opakující se plán", "arrivalTab": "Příchod", "day": "Den", "departureTab": "Odchod", "goal": "Cílový stav nabíjení", "modalTitle": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON>", "planNumber": "<PERSON><PERSON><PERSON> {number}", "preconditionDescription": "Nabíjení bude dokončeno {duration} před časem odjezdu. Vhodné v zimě pro zahřátí baterie či její šetření při vyšším stavu nabití.", "preconditionLong": "Odložené nabíjení", "preconditionOptionAll": "všechen", "preconditionOptionNo": "ne", "preconditionShort": "Odložené nabíjení", "remove": "<PERSON><PERSON><PERSON><PERSON>", "repeating": "opakující se", "repeatingPlans": "Opakující se plány", "selectAll": "Vybrat vše", "time": "Čas", "title": "Plán", "titleMinSoc": "<PERSON><PERSON>", "titleTargetCharge": "Odjezd", "unsavedChanges": "Jsou zde neuložené změny. Použít nyní?", "update": "<PERSON><PERSON><PERSON><PERSON>", "weekdays": "Dny"}, "energyflow": {"battery": "Baterie domu", "batteryCharge": "Nabíjení baterie", "batteryDischarge": "Vybíjení baterie", "batteryGridChargeActive": "nabíjení domácí baterie ze sítě je aktivní", "batteryGridChargeLimit": "nabíjení ze sítě, když", "batteryHold": "Baterie (udržování stavu nabití)", "batteryTooltip": "{energy} z {total} ({soc})", "forecastTooltip": "předpověď: zbývající solární výroba dnes", "gridImport": "<PERSON>d<PERSON><PERSON><PERSON> ze sítě", "homePower": "Spotřeba domu", "loadpoints": "Nabíječka | Nabíječka | {count} nabí<PERSON>ček", "noEnergy": "Žádná data z měřiče", "pv": "FVE", "pvExport": "Export do sítě", "pvProduction": "V<PERSON><PERSON>ba", "selfConsumption": "Vlastní výroba"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "connected": "Pohotovostní <PERSON>.", "vehicleLimit": "Limit topen<PERSON>", "waitForVehicle": "Připraven. Čekám na odpověď topidla…"}, "loadpoint": {"avgPrice": "⌀ Cena", "charged": "Nabito", "co2": "⌀ CO₂", "duration": "Doba trvání", "fallbackName": "Nabíjecí bod", "finished": "Čas dokončení", "power": "<PERSON>ý<PERSON>", "price": "<PERSON><PERSON>", "remaining": "Zbývající", "remoteDisabledHard": "{source}: vypnut", "remoteDisabledSoft": "{source}: adaptivní nabíjení ze slunce vypnuto", "solar": "<PERSON><PERSON><PERSON>"}, "loadpointSettings": {"batteryBoost": {"description": "Povolí využití energie uložené v baterii domu pro nabíjení.", "label": "R<PERSON><PERSON> dobití", "mode": "Dostupné pouze v solárním režimu a režimu min+solar.", "once": "Rychlé dobití je aktivováno pro tuto nabíjecí relaci."}, "batteryUsage": "Domácí baterie", "currents": "Nabí<PERSON><PERSON><PERSON>", "default": "výchozí", "disclaimerHint": "Poznámka:", "limitSoc": {"description": "Nab<PERSON><PERSON><PERSON><PERSON> <PERSON>, k<PERSON><PERSON> je pou<PERSON>, k<PERSON><PERSON> je připo<PERSON>no toto vozidlo.", "label": "Výchozí limit nabití"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Vozidlo bude rychle nabito na {0} v solárním režimu. Poté bude pokračovat nabíjení z přebytku solární energie. Užitečné pro zajištění minimálního dojezdu i během zatažených dnů.", "label": "Minimální úroveň nabití"}, "onlyForSocBasedCharging": "Tyto volby jsou povolené pouze pro vozidla se známou nabíjecí úrovní.", "phasesConfigured": {"label": "Připojení fází", "no1p3pSupport": "Jak je vaše nabíječka připojena?", "phases_0": "<PERSON><PERSON><PERSON>", "phases_1": "jednofázov<PERSON>", "phases_1_hint": "({min} do {max})", "phases_3": "třífázové", "phases_3_hint": "({min} do {max})"}, "smartCostCheap": "Nabíjení levnou energií ze sítě", "smartCostClean": "Nabíje<PERSON><PERSON> energií ze sítě", "title": "Nastavení {0}", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+Solar", "now": "<PERSON><PERSON><PERSON>", "off": "Vypnuto", "pv": "<PERSON><PERSON><PERSON>", "smart": "<PERSON><PERSON><PERSON><PERSON>"}, "provider": {"login": "přihlásit", "logout": "odhlásit"}, "startConfiguration": "Začněme s konfigurací", "targetCharge": {"activate": "Aktivovat", "co2Limit": "CO₂ limit z {co2}", "costLimitIgnore": "Nastavený {limit} nebude během tohoto nabíjení brán v potaz.", "currentPlan": "Aktivní plán", "descriptionEnergy": "Dokud nebude {targetEnergy} nabito do vozidla?", "descriptionSoc": "<PERSON><PERSON> by m<PERSON><PERSON> b<PERSON>t vozidlo nabito na {targetSoc}?", "goalReached": "<PERSON><PERSON><PERSON>", "inactiveLabel": "Cílový čas", "nextPlan": "<PERSON><PERSON><PERSON> p<PERSON>", "notReachableInTime": "Požadovaná hodnota nabití bude dosažena o {overrun} později.", "onlyInPvMode": "Nabíjecí plány fungují pouze v solárním režimu.", "planDuration": "Nabíjecí čas", "planPeriodLabel": "Opakování", "planPeriodValue": "{start} do {end}", "planUnknown": "doposud není <PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "priceLimit": "<PERSON>eno<PERSON><PERSON> strop z {price}", "remove": "<PERSON><PERSON><PERSON><PERSON>", "setTargetTime": "<PERSON><PERSON><PERSON><PERSON>", "targetIsAboveLimit": "Nastavení obecného limitu {limit} bude během této nab<PERSON><PERSON><PERSON><PERSON> relace ignorov<PERSON>o.", "targetIsAboveVehicleLimit": "Limit vozidla je nižší než cíl nabíjení.", "targetIsInThePast": "Vyber čas v budoucnu, Márty.", "targetIsTooFarInTheFuture": "<PERSON><PERSON><PERSON> bude <PERSON><PERSON>, jak<PERSON> bude k dispozici predikce.", "title": "Cílový čas", "today": "dnes", "tomorrow": "<PERSON>í<PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON><PERSON>, jak to nastavit.", "vehicleCapacityRequired": "Pro zjištění nabíjecího času je požadována kapacita baterie vozidla."}, "targetChargePlan": {"chargeDuration": "Čas nabíjení", "co2Label": "CO₂ emise ⌀", "priceLabel": "Cena energie", "timeRange": "{day} {range} h", "unknownPrice": "s<PERSON><PERSON><PERSON>"}, "targetEnergy": {"label": "<PERSON>it do<PERSON>", "noLimit": "<PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON><PERSON> vozidlo", "changeVehicle": "Změnit vozidlo", "detectionActive": "Probíhá detekce vozidla…", "fallbackName": "<PERSON><PERSON><PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "notReachable": "K vozidlu se nedalo připojit. Zkuste restarovat evcc.", "targetSoc": "Maximální úroveň nabití", "temp": "<PERSON><PERSON><PERSON><PERSON>", "tempLimit": "Cílová teplota", "unknown": "Vozidlo hosta", "vehicleSoc": "Aktuální stav baterie"}, "vehicleStatus": {"awaitingAuthorization": "Čeká se na autorizaci.", "batteryBoost": "Rychlé dobití je aktivní.", "charging": "Nabíjen<PERSON>…", "cheapEnergyCharging": "Levná energie je k dispozici.", "cheapEnergyNextStart": "Levná energie za {duration}.", "cheapEnergySet": "Cenový limit nastaven.", "cleanEnergyCharging": "Čistá energie je k dispozici.", "cleanEnergyNextStart": "Čistá energie za {duration}.", "cleanEnergySet": "Limit CO₂ nastaven.", "climating": "Detekováno předehřívání.", "connected": "Připojeno.", "disconnectRequired": "Relace byla <PERSON>. Prosím, připojte se znovu.", "disconnected": "Odpojeno.", "feedinPriorityNextStart": "Vysoká cena výkupu začne za {duration}.", "feedinPriorityPausing": "Nabíjení z FVE pozastaveno pro maximální dodávku do distribuční sítě.", "finished": "Dokončeno.", "minCharge": "Okamžité nabíjení na {soc}.", "pvDisable": "Nedostatečný přebytek. Nabíjení bude brzy pozastaveno.", "pvEnable": "Přebytky jsou k dispozici. Nabíjení začne brzy.", "scale1p": "Brzy dojde ke snížení na 1fázové nabíjení.", "scale3p": "Brzy dojde k přechodu na třífázové nabíjení.", "targetChargeActive": "Plán nabíjení je aktivní. Odhadovaný čas dokončení: {duration}.", "targetChargePlanned": "Plán nabíjení začne za {duration}.", "targetChargeWaitForVehicle": "Plán nabíjení připraven. Čeká se na vozidlo…", "vehicleLimit": "<PERSON><PERSON> voz<PERSON>", "vehicleLimitReached": "<PERSON>it voz<PERSON><PERSON>.", "waitForVehicle": "Připraveno. Čekám na vozidlo…", "welcome": "Krátké počáteční nabíjení pro potvrzení připojení."}, "vehicles": "Parkoviště", "welcome": "Vítejte na palubě!"}, "notifications": {"dismissAll": "Zrušit vše", "logs": "Zobrazit úplné protokoly", "modalTitle": "Oznámení"}, "offline": {"configurationError": "Chyba při spuštění. Zkontrolujte konfiguraci a restartujte.", "message": "Nepřipojeno na server.", "restart": "<PERSON><PERSON>", "restartNeeded": "Požadováno pro aplikaci změn.", "restarting": "Server bude brzy opět k dispozici."}, "passwordModal": {"description": "Nastavte heslo pro zabezpečení konfiguračního menu. Obrazovka přehledu bude stále dostupná i bez ověření heslem.", "empty": "<PERSON><PERSON><PERSON> b<PERSON> p<PERSON>", "error": "Chyba: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelNew": "<PERSON><PERSON>", "labelRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newPassword": "Vytvoř<PERSON> he<PERSON>", "noMatch": "<PERSON><PERSON> se neshoduj<PERSON>", "titleNew": "Nastavit administr<PERSON><PERSON><PERSON><PERSON>", "titleUpdate": "Aktualizovat administrátorsk<PERSON>", "updatePassword": "Aktualizovat he<PERSON>lo"}, "session": {"cancel": "Zrušit", "co2": "CO₂", "date": "Opakování", "delete": "<PERSON><PERSON><PERSON><PERSON>", "finished": "Ukončeno", "meter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meterstart": "<PERSON><PERSON><PERSON><PERSON><PERSON>čá<PERSON>k", "meterstop": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "odometer": "<PERSON><PERSON><PERSON><PERSON> kilometrů", "price": "<PERSON><PERSON>", "started": "Zahájeno", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace"}, "sessions": {"avgPower": "⌀ Výkon", "avgPrice": "⌀ Cena", "chargeDuration": "Trvání", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Cena {byGroup}", "byGroupLoadpoint": "podle nabí<PERSON><PERSON><PERSON><PERSON> bodu", "byGroupVehicle": "pod<PERSON> voz<PERSON>", "energy": "Nabité energie", "energyGrouped": "Solární energie vs. Energie ze sítě", "energyGroupedByGroup": "Energie {byGroup}", "energySubSolar": "{value} solár", "energySubTotal": "{value} celkem", "groupedCo2ByGroup": "Množství CO₂ {byGroup}", "groupedPriceByGroup": "<PERSON><PERSON><PERSON><PERSON> náklady {byGroup}", "historyCo2": "Emise CO₂", "historyCo2Sub": "{value} celkem", "historyPrice": "Náklady na nabíjení", "historyPriceSub": "{value} celkem", "solar": "Podíl solární energie za rok", "solarByGroup": "Podíl solární energie {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energie (kWh)", "chargeduration": "Trvání", "co2perkwh": "CO₂/kWh", "created": "Vytvořeno", "finished": "Ukončeno", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Nabíjecí bod", "meterstart": "Počáteční stav měřiče (kWh)", "meterstop": "Konečný stav měři<PERSON> (kWh)", "odometer": "Tachometr (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Cena/kWh", "solarpercentage": "Solární energie (%)", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "csvPeriod": "Stažení {period} CSV", "csvTotal": "Stažení celkového CSV", "date": "Start", "energy": "Nabito", "filter": {"allLoadpoints": "všechny nabíjecí body", "allVehicles": "všechny vozidla", "filter": "Filtr"}, "group": {"co2": "<PERSON><PERSON>", "grid": "Elektrická síť", "price": "<PERSON><PERSON>", "self": "<PERSON><PERSON><PERSON>"}, "groupBy": {"loadpoint": "Nabíjecí bod", "none": "<PERSON><PERSON><PERSON>", "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Nabíjecí bod", "noData": "<PERSON><PERSON> m<PERSON><PERSON><PERSON><PERSON> nep<PERSON><PERSON><PERSON><PERSON> žádné nabíjení.", "overview": "<PERSON><PERSON><PERSON><PERSON>", "period": {"month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "year": "Rok"}, "price": "<PERSON><PERSON>", "reallyDelete": "Opravdu chcete smazat tuto relaci?", "showIndividualEntries": "Zobrazit j<PERSON><PERSON><PERSON><PERSON><PERSON> relace", "solar": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace", "total": "<PERSON><PERSON><PERSON>", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}, "vehicle": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Přepnout do režimu celé obrazovky", "exit": "Ukončit režim cel<PERSON> o<PERSON>", "label": "<PERSON><PERSON><PERSON> o<PERSON>zo<PERSON>"}, "hiddenFeatures": {"label": "Experimentální nastavení", "value": "Zobrazit experimentální nastavení."}, "language": {"auto": "<PERSON>ky", "label": "Jazyk"}, "sponsorToken": {"expires": "<PERSON><PERSON><PERSON> spo<PERSON> token vypr<PERSON><PERSON> za {inXDays}. {getNewToken} a aktualizujte ho zde.", "getNew": "<PERSON><PERSON><PERSON>", "hint": "Poznámka: Toto v budoucnu zautomatizujeme."}, "telemetry": {"label": "Telemetrie"}, "theme": {"auto": "systém", "dark": "tmavý", "label": "Design", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "time": {"12h": "12h", "24h": "24h", "label": "<PERSON><PERSON><PERSON>"}, "title": "Nastavení rozhraní", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON>", "mi": "<PERSON><PERSON><PERSON>"}}, "smartCost": {"activeHours": "{active} z {total}", "activeHoursLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applyToAll": "Aplikovat všude?", "batteryDescription": "Nabíjí baterii FVE ze distribuční sítě.", "cheapTitle": "<PERSON><PERSON>é nabíjení ze sítě", "cleanTitle": "Čisté nabíjení ze sítě", "co2Label": "CO₂ emise", "co2Limit": "Limit CO₂", "loadpointDescription": "Umožňuje dočasné okamžité nabíjení i v solárním režimu.", "modalTitle": "Smart Grid Nabí<PERSON>ní", "none": "<PERSON><PERSON><PERSON><PERSON>", "priceLabel": "Cena energie", "priceLimit": "Cenový strop", "resetAction": "Odebrat limit", "resetWarning": "<PERSON><PERSON>í nakonfigurována dynamická cena za energii ze sítě nebo zdroj CO₂. I přesto je zde stále limit {limit}. Chcete upravit konfiguraci?", "saved": "Uloženo."}, "smartFeedInPriority": {"activeHoursLabel": "Pozastaveno", "description": "Pozastaví nabíjení pro maximalizaci výdělečné dodávky do distribuční sítě.", "priceLabel": "Cena výkupu energie", "priceLimit": "Cenový strop pro výkup energie", "resetWarning": "<PERSON><PERSON><PERSON>vili jste žádný dynamický tarif výkupu energie. Přesto je nastavení limit {limit}. Zkontrolujte konfiguraci.", "title": "Priorita výkupu energie"}, "startupError": {"configFile": "Konfigurační soubor užív<PERSON>:", "configuration": "Konfigurace", "description": "Zkontrolujte prosím konfigurační soubor. Pokud chybová hláška nepomáhá, zkontroluj {0}.", "discussions": "GitHub diskuze", "fixAndRestart": "Opravte prosím problém a restartujte server.", "hint": "Poznámka: <PERSON><PERSON><PERSON><PERSON> to být také způsobeno vadným zařízením (invertor, měř<PERSON>č, ...). Zkontrolujte síťové připojení.", "lineError": "Chyba v {0}.", "lineErrorLink": "<PERSON><PERSON><PERSON> {0}", "restartButton": "<PERSON><PERSON>", "title": "Chyba při s<PERSON>štění"}}