{"batterySettings": {"batteryLevel": "Batterinivå", "bufferStart": {"above": "när den är över {soc}.", "full": "när det är {soc}.", "never": "bara med tillr<PERSON><PERSON><PERSON>gt sol-överskott."}, "capacity": "{energy} av {total}", "control": "Batteriinställningar", "discharge": "Förhindra urladdning i snabbt läge och vid schemalagd laddning.", "disclaimerHint": "OBS:", "disclaimerText": "Inställningarna gäller enbart sol-läge. Laddning justeras därefter.", "gridChargeTab": "Laddning från n<PERSON>et", "legendBottomName": "Prioritera laddning av hemmabatteri", "legendBottomSubline": "tills den når {soc}.", "legendMiddleName": "Prioritera laddning av fordon", "legendMiddleSubline": "när hemmabatteriet är över {soc}.", "legendTopAutostart": "Startar automatiskt", "legendTopName": "Fordonsladdning med batteristöd", "legendTopSubline": "när hemmabatteriet är över {soc}.", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usageTab": "Batterianvändning"}, "config": {"aux": {"description": "Enhet som anpassar sin förbrukning baserat på tillgängligt överskott (ex. smarta varmvattenberedare). Evcc förväntar sig att denna enhet reducerar sin förbrukning om det är nödvändigt.", "titleAdd": "Lägg till enhet som själv anpassar förbrukning", "titleEdit": "<PERSON>ndra enhet som själv anpassar förbrukning"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON> till batteri", "titleEdit": "<PERSON><PERSON>"}, "charge": {"titleAdd": "Lägg till energimätare", "titleEdit": "<PERSON><PERSON>"}, "charger": {"chargers": "Elbilsladdare", "generic": "Generisk integration", "heatingdevices": "Värmeenheter", "ocppHelp": "<PERSON><PERSON><PERSON> in denna adress i din laddkonfiguration.", "ocppLabel": "OCPP-Server URL", "switchsockets": "<PERSON>t kontakt", "template": "Tillverkare", "titleAdd": {"charging": "Lägg till laddare", "heating": "Lägg till värmare"}, "titleEdit": {"charging": "<PERSON><PERSON>", "heating": "<PERSON>ndra värmare"}, "type": {"custom": {"charging": "Egendefinierad laddare", "heating": "Egendefinierad värmare"}, "heatpump": "Egendefinierad värmepump", "sgready": "Egendefinierad värmepump (sg-ready, alla)", "sgready-boost": "Egendefinierad värmepump (sg-ready, boost)", "switchsocket": "Egendefinierad <PERSON>"}}, "circuits": {"description": "<PERSON><PERSON><PERSON><PERSON>, att summan av laddpunkter kopplad till en krets inte överskrider den konfigurerade effektgränsen. Kretsar kan nästlas för att bygga en hierarki.", "title": "Belastningshantering"}, "control": {"description": "Standardvärdena är vanligtsvis ok. Ändra bara om du vet vad du gör.", "descriptionInterval": "Uppdateringsintervall i sekunder. Definierar hur ofta evcc läser in data, ändrar laddeffekt och uppdaterar UI. Korta intervall (<30s) kan orsaka självsvängning och andra felaktiga resultat.", "descriptionResidualPower": "Flyttar fokuspunkt på kontrolloopen. Om du har ett hemmabatteri så rekommenderas att ha ett värde på minst 100 W. Då får batteriet lite högre prioritet jämfört med nätanvändning.", "labelInterval": "Uppdateringsintervall", "labelResidualPower": "<PERSON><PERSON><PERSON><PERSON><PERSON> effekt", "title": "Kontrollera beteende"}, "deviceValue": {"amount": "Belopp", "broker": "Broker", "bucket": "Bucket", "capacity": "Kapacitet", "chargeStatus": "Status", "chargeStatusA": "ej an<PERSON><PERSON>en", "chargeStatusB": "ansluten", "chargeStatusC": "laddar", "chargeStatusE": "ingen ström", "chargeStatusF": "fel", "chargedEnergy": "<PERSON><PERSON><PERSON>", "co2": "Elnät CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "Kontrollerbar", "currency": "Valuta", "current": "Ström", "currentRange": "Ström", "enabled": "Aktiverad", "energy": "Energi", "feedinPrice": "Inmatningspris", "gridPrice": "Nätpris", "heaterTempLimit": "Värmarbegränsning", "hemsType": "System", "identifier": "RFID-identifierare", "no": "nej", "odometer": "Mätarställning", "org": "Organisation", "phaseCurrents": "Ström L1, L2, L3", "phasePowers": "Effekt L1, L2, L3", "phaseVoltages": "<PERSON><PERSON><PERSON><PERSON> L1, L2, L3", "phases1p3p": "Fasväxlare", "power": "Effekt", "powerRange": "Kraft", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singlePhase": "En-fas", "soc": "Laddnivå", "solarForecast": "Solprognos", "temp": "Temperatur", "topic": "Ämne", "url": "URL", "vehicleLimitSoc": "Fordonsbegränsning", "yes": "ja"}, "deviceValueChargeStatus": {"A": "A (ej ansluten)", "B": "B (ansluten)", "C": "C (laddar)"}, "devices": {"auxMeter": "Smart förbrukare", "batteryStorage": "Batterilager", "solarSystem": "Solanläggning"}, "editor": {"loading": "<PERSON><PERSON><PERSON>-editorn…"}, "eebus": {"description": "Inställning som tillåter evcc att kommunicera med andra EEBus-enheter.", "title": "EEBus"}, "ext": {"description": "Kan anv<PERSON><PERSON> för belastningsstyrning eller statistik.", "titleAdd": "Lägg till extern mätare", "titleEdit": "Ändra extern mätare"}, "form": {"danger": "Fara", "deprecated": "utfasad", "example": "Exempel", "optional": "val<PERSON>ri"}, "general": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customHelp": "Skapa en egen enhet med hjälp av evcc's plugin-system.", "customOption": "Egen-definierad enhet", "delete": "<PERSON><PERSON><PERSON>", "docsLink": "Se dokumentation.", "experimental": "Experimentell", "hideAdvancedSettings": "D<PERSON><PERSON>j avancerade inställningar", "invalidFileSelected": "Ogiltig fil vald", "noFileSelected": "Ingen fil vald.", "off": "av", "on": "på", "password": "L<PERSON>senord", "readFromFile": "<PERSON><PERSON><PERSON> fil", "remove": "<PERSON> bort", "save": "Spara", "selectFile": "Bläddra", "showAdvancedSettings": "Visa avancerade inställningar", "telemetry": "Telemetri", "templateLoading": "Laddar...", "title": "Titel", "validateSave": "Validera & spara"}, "grid": {"title": "Elmätare", "titleAdd": "Lägg till elnätsmätare", "titleEdit": "<PERSON><PERSON>"}, "hems": {"description": "Anslut evcc till annat energistyrningssystem i hemmet.", "title": "HEMS"}, "icon": {"change": "<PERSON><PERSON>"}, "influx": {"description": "Skriver laddata och andra mät<PERSON>ar till InfluxDB. Använd Grafana eller andra verktyg för att visualisera data.", "descriptionToken": "Se dokumentationen för InfluxDB. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "<PERSON><PERSON>t egensignerade certifikat", "labelDatabase": "Databas", "labelInsecure": "Certifikatvalidering", "labelOrg": "Organisation", "labelPassword": "L<PERSON>senord", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Användarnamn", "title": "InfluxDB", "v1Support": "Behöver du support till InfluxDB 1.x?", "v2Support": "Tillbaka till InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Lägg till laddare", "heating": "Lägg till värmare"}, "addMeter": "Lägg till dedikerad laddmätare", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargerError": {"charging": "Konfigurering av laddare krävs.", "heating": "En värmare måste konfigureras."}, "chargerLabel": {"charging": "<PERSON><PERSON><PERSON>", "heating": "Värma<PERSON>"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "<PERSON><PERSON><PERSON><PERSON>llan 6 och 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "<PERSON><PERSON><PERSON><PERSON> 6 till 32 A.", "chargerPowerCustom": "annan", "chargerPowerCustomHelp": "<PERSON><PERSON>.", "chargerTypeLabel": "Typ av laddare", "chargingTitle": "<PERSON><PERSON><PERSON>", "circuitHelp": "Belastningsstyrning som säkrar att effekt och strömgränser ej överskrids.", "circuitLabel": "Krets", "circuitUnassigned": "ej vald", "defaultModeHelp": {"charging": "Laddningsläge vid anslutning till fordon.", "heating": "St<PERSON>lls in vid systemstart."}, "defaultModeHelpKeep": "<PERSON><PERSON><PERSON><PERSON> senast valda lad<PERSON>lä<PERSON>.", "defaultModeLabel": "Standardinställning", "delete": "<PERSON><PERSON><PERSON>", "electricalSubtitle": "<PERSON><PERSON> du <PERSON>, kontakta elektriker.", "electricalTitle": "Elektrisk", "energyMeterHelp": "Extern mätare (Om laddaren inte har en integrerad mätare).", "energyMeterLabel": "Energimätare", "estimateLabel": "Interpolera laddningsnivå mellan API uppdateringar", "maxCurrentHelp": "Måste vara högre än minimi strömstyrka.", "maxCurrentLabel": "Maximal strömstyrka", "minCurrentHelp": "Ange endast under 6 A om du vet vad du gör.", "minCurrentLabel": "Minimum strömstyrka", "noVehicles": "Inga fordon är konfigurerade.", "option": {"charging": "Lägg till laddplats", "heating": "Lägg till värmare"}, "phases1p": "1-fas", "phases3p": "3-fas", "phasesAutomatic": "Automatisk fasväljare", "phasesAutomaticHelp": "<PERSON> laddare kan automatiskt byta mellan 1- och 3-fasladdning. På huvudskärmen kan du ändra fasinställningarna under laddning.", "phasesHelp": "Antalet faser som är anslutna till laddaren.", "phasesLabel": "<PERSON><PERSON><PERSON>", "pollIntervalDanger": "Att anropa fordonet ofta kan tömma batteriet. En del fordonstillverkare kan aktivt förhindra att fordonet laddas i så fall. Rekommenderas inte! Använd enbart om du är medveten om risken.", "pollIntervalHelp": "Tid mellan fordons API uppdateringar. Korta intervaller kan minska fordonets batterinivå.", "pollIntervalLabel": "Uppdateringsintervall", "pollModeAlways": "alltid", "pollModeAlwaysHelp": "Hämta alltid statusuppdateringar med jämna mellanrum.", "pollModeCharging": "laddar", "pollModeChargingHelp": "Hämta endast fordonets statusuppdateringar under laddning.", "pollModeConnected": "ansluten", "pollModeConnectedHelp": "Uppdatera fordonsstatus med jämna mellanrum vid anslutning till laddaren.", "pollModeLabel": "Uppdatera tillvägagångsätt", "priorityHelp": "Högre prioriterade laddare får snabbare tillgång till solenergiöverskott.", "priorityLabel": "Prioritet", "save": "Spara", "showAllSettings": "Visa alla inställningar", "solarBehaviorCustomHelp": "Definera dina egna aktiverings- och deaktiverings-tröskelvärden och fördröjningar.", "solarBehaviorDefaultHelp": "Ladda endast med solenergiöverskott. Starta efter {enableDelay} av överskott. Stanna när överskottet går under {disableDelay}.", "solarBehaviorLabel": "Solenergi inställningar", "solarModeCustom": "egen", "solarModeMaximum": "maximal solenergi", "thresholdDisableDelayLabel": "Stäng av fördröjning", "thresholdDisableHelpInvalid": "Ange ett positivt värde.", "thresholdDisableHelpPositive": "Sluta ladda när mer än {power} under {delay} används från el<PERSON>.", "thresholdDisableHelpZero": "Sluta när minimum laddningseffekt inte kan uppfyllas under {delay}.", "thresholdDisableLabel": "<PERSON><PERSON><PERSON> bort elnät<PERSON>", "thresholdEnableDelayLabel": "Slå på fördr<PERSON>jning", "thresholdEnableHelpInvalid": "Ange ett negativt värde.", "thresholdEnableHelpNegative": "<PERSON><PERSON><PERSON><PERSON> ladda när {surplus} överskott finns i {delay}.", "thresholdEnableHelpZero": "Starta när minimum överskott av laddningseffekt finns i {delay}.", "thresholdEnableLabel": "<PERSON><PERSON><PERSON>n<PERSON>", "titleAdd": {"charging": "Lägg till laddplats", "heating": "Lägg till värmare", "unknown": "<PERSON><PERSON><PERSON> till laddare eller värmare"}, "titleEdit": {"charging": "<PERSON><PERSON><PERSON> laddare", "heating": "Ändra värmarenhet", "unknown": "<PERSON><PERSON> laddare eller värmare"}, "titleExample": {"charging": "Garage, Carport, etc.", "heating": "Värmepump, värmare etc."}, "titleLabel": "Titel", "vehicleAutoDetection": "automatisk detektering", "vehicleHelpAutoDetection": "Väljer automatiskt det mest troliga fordonet. Manuell inställning är möjlig.", "vehicleHelpDefault": "Anta att detta fordon alltid laddar här. Automatisk detektering är avstängt. Manuell inställning är möjlig.", "vehicleLabel": "Standard fordon", "vehiclesTitle": "<PERSON><PERSON>"}, "main": {"addAdditional": "Lägg till ytterligare mätare", "addGrid": "Lägg till elmätare", "addLoadpoint": "Lägg till laddbox", "addPvBattery": "<PERSON><PERSON><PERSON> till solceller eller batteri", "addTariffs": "Lägg till tariffer", "addVehicle": "<PERSON><PERSON><PERSON> till fordon", "configured": "konfigu<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "loadpointRequired": "Minst en laddpunkt måste konfigureras.", "name": "<PERSON><PERSON>", "title": "Inställningar", "unconfigured": "ej kon<PERSON><PERSON><PERSON>d", "vehicles": "<PERSON> fordon", "yaml": "Konfigurera i evcc.yaml. Kan inte ändras i UI."}, "messaging": {"description": "Få notiser om dina laddningar.", "title": "Notiser"}, "meter": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "generic": "Generisk integration", "option": {"aux": "Lägg till enhet som själv anpassar förbrukning", "battery": "Lägg till batterimätare", "ext": "Lägg till ytterligare mätare", "pv": "Lägg till solenergimätare"}, "save": "Spara", "specific": "Specifik integration", "template": "Tillverkare", "titleChoice": "Vad vill du lägga till?", "validateSave": "Validera & spara"}, "modbus": {"baudrate": "Baudrate", "comset": "ComSet", "connection": "Modbus förbindelse", "connectionHintSerial": "Enheten är direkt förbunden till evcc via RS485 interface.", "connectionHintTcpip": "Enheten kan adresseras via LAN/Wifi.", "connectionValueSerial": "Seriell / USB", "connectionValueTcpip": "Nätverk", "device": "Enhetsnamn", "deviceHint": "Exempel: /dev/ttyUSB0", "host": "IP adress eller värdnamn", "hostHint": "Exempel: *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus protokoll", "protocolHintRtu": "Förbindelse via RS485 till Ethernet-adapter utan protokollöversättning.", "protocolHintTcp": "Enheten har inbyggt LAN/Wifi-stöd eller är förbunden via RS485 till Ethernet-adapter med protokollöversättning.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "<PERSON><PERSON><PERSON> flera klienter till en Modbus-enhet.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Autentisering", "description": "Anslut till en MQTT-broker för att utbyta data med andra system på ditt nätverk.", "descriptionClientId": "Avsändare av notis. Om tomt används `evcc-[rand]` .", "descriptionTopic": "<PERSON><PERSON><PERSON>na tomt för att deaktivera publicering.", "labelBroker": "Broker", "labelCaCert": "Server certifikat (CA)", "labelCheckInsecure": "<PERSON><PERSON>t egensignerade certifikat", "labelClientCert": "Klient certifikat", "labelClientId": "Klient ID", "labelClientKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelInsecure": "Certifikatkontroll", "labelPassword": "L<PERSON>senord", "labelTopic": "Ämne", "labelUser": "Användarnamn", "publishing": "Publicerar", "title": "MQTT"}, "network": {"descriptionHost": "Använd .local suffix för att aktivera mDNS. Relevant för upptäckt av mobilappen och vissa OCPP-laddare.", "descriptionPort": "Port för webinterface och API. Uppdatera webläsarens URL om du ändrar detta.", "descriptionSchema": "Påverkar endast hur URLer skapas. Val av HTTPS kommer ej att aktivera kryptering.", "labelHost": "Värdnamn", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Nätverk"}, "options": {"boolean": {"no": "nej", "yes": "ja"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "<PERSON><PERSON><PERSON><PERSON>", "standby": "Standby"}, "schema": {"http": "HTTP (okrypterad)", "https": "HTTPS (krypterad)"}, "status": {"A": "A (ej ansluten)", "B": "B (ansluten)", "C": "C (laddar)"}}, "pv": {"titleAdd": "Lägg till solcellsmätare", "titleEdit": "<PERSON><PERSON>"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON><PERSON> mätare", "general": "Allmänna", "grid": "Elnät", "integrations": "<PERSON><PERSON>", "loadpoints": "Laddpunkter", "meter": "Sol & batteri", "system": "System", "vehicles": "<PERSON><PERSON>"}, "sponsor": {"addToken": "Ange sponsor-token", "changeToken": "Ändra sponsor-token", "description": "Sponsormodellen hjälper oss att underhålla projektet och hållbart bygga nya och spännande funktioner. Som sponsor får du tillgång till alla laddningsmöjligheter.", "descriptionToken": "Du får token från {url}. Vi erbjuder också en prov-token för testning.", "error": "Din sponsor-token är inte giltig.", "labelToken": "Sponsor-token", "title": "Sponsor", "tokenRequired": "Du måste ange en sponsortoken innan du kan lägga till detta fordon.", "tokenRequiredLearnMore": "<PERSON><PERSON><PERSON> mer.", "tokenRequiredShort": "Ingen sponsor-token konfigurerad.", "trialToken": "Testtoken"}, "system": {"backupRestore": {"backup": {"action": "Download backup...", "confirmationButton": "Download backup", "confirmationText": "<PERSON><PERSON> ditt lösenord för att ladda hem databasfilen.", "description": "Säkerhetskopiera dina data. Denna fil används för att återställa data i händelse av en systemkrasch.", "title": "Säkerhetskopia"}, "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmWithPassword": "Bekräfta", "description": "Säkerhetskopiera, återställ och nollställ dina data. Användbart om vill flytta till ett annat system.", "note": "Anmärkning: Alla ovanstående ändringar ändrar endast databasens data. Konfigurationsfilen evcc.yaml ändras inte.", "reset": {"action": "Nollställ...", "confirmationButton": "Nollställ & starta om", "confirmationText": "<PERSON><PERSON> raderar dina data permanent. Säkerställ att du har sparat en backup först.", "description": "Har du problem med konfigurationen och vill börja om? Radera all data och starta om.", "sessions": "Laddningar", "sessionsDescription": "Ra<PERSON><PERSON> din laddningshistorik.", "settings": "Konfiguration & inställningar", "settingsDescription": "Raderar alla konfigurerade enheter, t<PERSON><PERSON><PERSON><PERSON>, abonnemang etc.", "title": "Reset"}, "restore": {"action": "Återställ...", "confirmationButton": "Återställ & starta om", "confirmationText": "<PERSON><PERSON> kommer att radera hela databasen. Se till att säkerhetskopiera först.", "description": "Återställ data från en backup-fil. <PERSON><PERSON> raderar dina nuvarande data.", "labelFile": "Säkerhetskopia", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "title": "Säkerhetskopiera & återställ"}, "logs": "Loggar", "restart": "Omstart", "restartRequiredDescription": "Starta om för att de nya inställningarna ska börja gälla.", "restartRequiredMessage": "Inställningar ändrade.", "restartingDescription": "Vänligen vänta…", "restartingMessage": "Startar om evcc."}, "tariffs": {"description": "<PERSON><PERSON><PERSON> in din energitariff för att beräkna kostnaden för dina laddningar.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Visas på huvudskärm och tabbar.", "label": "Titel", "title": "<PERSON><PERSON> titel"}, "validation": {"failed": "miss<PERSON><PERSON><PERSON>", "label": "Status", "running": "validerar…", "success": "lyckades", "unknown": "okänd", "validate": "validerar"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargingSettings": "Laddinställningar", "defaultMode": "Standardläge", "defaultModeHelp": "Laddläge när fordonet ansluts.", "delete": "<PERSON><PERSON><PERSON>", "generic": "<PERSON>ra integrationer", "identifiers": "RFID-identifiering", "identifiersHelp": "Lista på RFID-strängar för att identifiera fordonet. Ett inlägg per rad. Aktuell identifierare hittas på respektive laddare på översiktssidan.", "maximumCurrent": "Maximal ström", "maximumCurrentHelp": "<PERSON><PERSON><PERSON> vara högre än minimi-ström.", "maximumPhases": "<PERSON><PERSON><PERSON> antal faser", "maximumPhasesHelp": "<PERSON>r många faser kan fordonet laddas med? Används för att räkna ut minsta solöverskott och tidsåtgång.", "minimumCurrent": "<PERSON><PERSON>", "minimumCurrentHelp": "<PERSON><PERSON><PERSON> enbart under 6A om du är medveten om konsekvenserna.", "online": "Fordon med online API", "primary": "Generisk integration", "priority": "Prioritet", "priorityHelp": "Högre prioritet betyder att detta fordon får förtur till solöverskott.", "save": "Spara", "scooter": "<PERSON>ooter", "template": "Tillverkare", "titleAdd": "<PERSON><PERSON><PERSON> till fordon", "titleEdit": "<PERSON><PERSON>don", "validateSave": "Bekräfta & spara"}}, "footer": {"community": {"greenEnergy": "Solenergi", "greenEnergySub1": "laddat med evcc", "greenEnergySub2": "sedan oktober 2022", "greenShare": "Solenergiandel", "greenShareSub1": "andel som levereras av", "greenShareSub2": "sol och batteri", "power": "Laddeffekt", "powerSub1": "{activeClients} av {totalClients} deltagare", "powerSub2": "laddar…", "tabTitle": "Live community"}, "savings": {"co2Saved": "{value} sparad", "co2Title": "CO₂ utsläpp", "configurePriceCo2": "Konfigurera pris och CO₂-utsläpp.", "footerLong": "{percent} solenergi", "footerShort": "{percent} sol", "modalTitle": "Översikt laddning", "moneySaved": "{value} sparad", "percentGrid": "{grid} kWh nät", "percentSelf": "{self} kWh sol", "percentTitle": "Solenergi", "period": {"30d": "senaste 30 dagar", "365d": "senaste 365 dagarna", "thisYear": "<PERSON><PERSON>", "total": "totalt"}, "periodLabel": "Period:", "priceTitle": "Energi<PERSON><PERSON>", "referenceGrid": "nät", "referenceLabel": "Referensdata:", "tabTitle": "<PERSON> uppgifter"}, "sponsor": {"becomeSponsor": "Bli sponsor", "becomeSponsorExtended": "Stöd oss direkt för att få klistermärken.", "confetti": "Är du redo för konfetti?", "confettiPromise": "<PERSON> får klistermärken och digital konfetti", "sticker": "... eller evcc-klistermärken?", "supportUs": "Vårt uppdrag är att göra solenergi till norm. Hjälp evcc genom att betala vad det är värt för dig.", "thanks": "Tack, {sponsor}! <PERSON><PERSON> bidrag hjälper oss att utveckla evcc ytterligare.", "titleNoSponsor": "<PERSON><PERSON><PERSON> oss", "titleSponsor": "<PERSON> är en supporter", "titleTrial": "Testläge", "titleVictron": "Sponsras av Victron Energy", "trial": "Du är i testläge och kan använda alla funktioner. Överväg att stödja projektet.", "victron": "<PERSON> använder evcc på Victron Energy-hårdvara och har tillgång till alla funktioner."}, "telemetry": {"optIn": "Jag vill bidra med min data.", "optInMoreDetails": "<PERSON><PERSON> de<PERSON> {0}.", "optInMoreDetailsLink": "<PERSON>är", "optInSponsorship": "Sponsring krävs."}, "version": {"availableLong": "ny version tillgänglig", "modalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modalDownload": "Ned<PERSON><PERSON><PERSON>", "modalInstalledVersion": "Installerad version", "modalNoReleaseNotes": "Det finns inga versionsanvisningar tillgängliga. Mer information om den nya versionen:", "modalTitle": "Ny version finns tillgänglig", "modalUpdate": "Installera", "modalUpdateNow": "Installera nu", "modalUpdateStarted": "Starta den nya versionen av evcc…", "modalUpdateStatusStart": "Installationen har bö<PERSON><PERSON>:"}}, "forecast": {"co2": {"average": "Genomsnitt", "lowestHour": "Timme med lägst CO₂", "range": "Intervall"}, "modalTitle": "<PERSON><PERSON><PERSON>", "price": {"average": "Genomsnitt", "lowestHour": "Billigaste timmen", "range": "Intervall"}, "solar": {"dayAfterTomorrow": "I övermorgon", "partly": "delvis", "remaining": "återstående", "today": "<PERSON><PERSON>", "tomorrow": "I morgon"}, "solarAdjust": "Justera solprognosen baserat på verkliga produktionsvärden{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Sol"}}, "header": {"about": "Om", "authProviders": {"confirmLogout": "<PERSON>r du säker på att du vill bryta anslutningen {title}?", "title": "Auktoriseringsstatus"}, "blog": "Blogg", "docs": "Dokumentation", "github": "GitHub", "login": "Fordons inloggningar", "logout": "Logga ut", "nativeSettings": "Byt server", "needHelp": "Behöver du hj<PERSON>lp?", "sessions": "Laddningar"}, "help": {"discussionsButton": "GitHub diskussioner", "documentationButton": "Dokumentation", "issueButton": "Rapportera fel", "issueDescription": "Hittat ett konstigt eller felaktigt beteende?", "logsButton": "Se loggar", "logsDescription": "Felsök loggar.", "modalTitle": "Behöver du hj<PERSON>lp?", "primaryActions": "<PERSON>gerar det inte som tänkt? Här finns mycket hjälp att hitta.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "Ja, starta om!", "description": "Normalt behövs inte en omstart. Rapportera ett fel om du behöver starta om evcc frekvent.", "disclaimer": "Notera: evcc kommer stängas av och be operativsystemet starta om tjänsten.", "modalTitle": "Är du säker på att du vill starta om?"}, "restartButton": "Omstart", "restartDescription": "Har du testat att starta om?", "secondaryActions": "<PERSON>yckas du inte lösa problemet? Här finns mer hjälp."}, "log": {"areaLabel": "Filtrera per område", "areas": "Alla om<PERSON>", "download": "Ladda hem alla loggar", "levelLabel": "Filtrera på loggnivå", "nAreas": "{count} områden", "noResults": "Inga matchande loggposter.", "search": "<PERSON>ö<PERSON>", "selectAll": "v<PERSON><PERSON>j alla", "showAll": "Visa allt", "title": "Loggar", "update": "Autouppdatera"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "demoMode": "Login fungerar inte i demo-läge.", "error": "Inloggning misslyckades: ", "iframeHint": "Öppna evcc på en ny flik.", "iframeIssue": "<PERSON><PERSON> l<PERSON> är kor<PERSON>t, men din webbläsare verkar ha tagit bort autentiseringscookien. <PERSON>ta kan hända om du kör evcc i en iframe via HTTP.", "invalid": "Ogiltigt lösenord.", "login": "Logga in", "password": "L<PERSON>senord", "reset": "Återställ lösenord?", "title": "Autentisering"}, "main": {"chargingPlan": {"active": "Aktiv", "addRepeatingPlan": "Lägg till en återkommande laddplan", "arrivalTab": "Ankomst", "day": "<PERSON><PERSON>", "departureTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goal": "Laddmål", "modalTitle": "Laddplan", "none": "ingen", "planNumber": "Plan {number}", "preconditionDescription": "Ladda {duration} före avfärd för batteriuppvärm<PERSON>.", "preconditionLong": "Sen laddning", "preconditionOptionAll": "allt", "preconditionOptionNo": "nej", "preconditionShort": "<PERSON>", "remove": "<PERSON> bort", "repeating": "återkommande", "repeatingPlans": "Återkommande planer", "selectAll": "<PERSON><PERSON><PERSON><PERSON> alla", "time": "Tid", "title": "Plan", "titleMinSoc": "<PERSON>. laddning", "titleTargetCharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsavedChanges": "Det finns ej sparade ändringar. Spara nu?", "update": "Verkställ", "weekdays": "Dagar"}, "energyflow": {"battery": "<PERSON><PERSON><PERSON>", "batteryCharge": "<PERSON><PERSON><PERSON> laddas", "batteryDischarge": "<PERSON><PERSON>i laddas ur", "batteryGridChargeActive": "nätladdning aktiv", "batteryGridChargeLimit": "nätladdning när", "batteryHold": "<PERSON><PERSON><PERSON> (låst)", "batteryTooltip": "{energy} av {total} ({soc})", "forecastTooltip": "Prognos: återstående solproduktion idag", "gridImport": "Import fr<PERSON>n <PERSON>", "homePower": "Konsumtion", "loadpoints": "Laddare | Laddare | {count} laddare", "noEnergy": "Inga mätaruppgifter", "pv": "Solanläggning", "pvExport": "Export till elnät", "pvProduction": "Produktion", "selfConsumption": "Egenförbrukning"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON>…", "connected": "Standby.", "vehicleLimit": "Värmarbegränsning", "waitForVehicle": "Redo, väntar på värmare…"}, "loadpoint": {"avgPrice": "⌀ pris", "charged": "Laddat", "co2": "⌀ CO₂", "duration": "Laddtid", "fallbackName": "Laddplats", "finished": "S<PERSON><PERSON><PERSON>", "power": "Effekt", "price": "Kostnad", "remaining": "<PERSON><PERSON>tående tid", "remoteDisabledHard": "{source}: avstängd", "remoteDisabledSoft": "{source}: Adaptiv solladdning avstängd", "solar": "Solenergi"}, "loadpointSettings": {"batteryBoost": {"description": "Snabbladdning från hembatteri.", "label": "<PERSON><PERSON><PERSON>", "mode": "Endast tillgänglig vid 'Sol' och 'Min+Sol' läge.", "once": "Boost är aktiverat för denna laddning."}, "batteryUsage": "<PERSON><PERSON><PERSON><PERSON>", "currents": "Laddström", "default": "<PERSON><PERSON><PERSON><PERSON>", "disclaimerHint": "Anmärkning:", "limitSoc": {"description": "Laddbegränsning när detta fordon är anslutet.", "label": "<PERSON><PERSON><PERSON><PERSON>gr<PERSON>"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Fordonet laddas i sol-läge „snabbt” till {0} och sedan med endast solel. Användbart för att alltid ha en minsta räckvidd tillgänglig .", "label": "Min. laddning %"}, "onlyForSocBasedCharging": "Dessa inställningar är endast tillgängliga för fordon med känd laddnivå.", "phasesConfigured": {"label": "<PERSON><PERSON><PERSON>", "no1p3pSupport": "Hur är din laddbox ansluten?", "phases_0": "automatisk växling", "phases_1": "1-fas", "phases_1_hint": "({min} till {max})", "phases_3": "3-fas", "phases_3_hint": "({min} till {max})"}, "smartCostCheap": "<PERSON><PERSON> laddning från n<PERSON>et", "smartCostClean": "Laddning med grön el", "title": "Inställningar {0}", "vehicle": "<PERSON><PERSON>"}, "mode": {"minpv": "Min+Sol", "now": "Snabbt", "off": "Av", "pv": "Sol", "smart": "Smart"}, "provider": {"login": "logga in", "logout": "logga ut"}, "startConfiguration": "Starta konfigurationen", "targetCharge": {"activate": "Aktivera", "co2Limit": "CO₂ gräns av {co2}", "costLimitIgnore": "Den inställda {limit} kommer att ignoreras den här perioden.", "currentPlan": "Aktiv plan", "descriptionEnergy": "<PERSON><PERSON><PERSON> ska fordonet vara laddat till {targetEnergy}?", "descriptionSoc": "<PERSON><PERSON><PERSON> ska fordonet vara laddat till {targetSoc}?", "goalReached": "Målet är uppnått", "inactiveLabel": "<PERSON><PERSON><PERSON><PERSON>t<PERSON>", "nextPlan": "Nästa plan", "notReachableInTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> kommer att nås {endTime} senare.", "onlyInPvMode": "Laddplan fungerar enbart i sol-läge.", "planDuration": "Laddtid", "planPeriodLabel": "Period", "planPeriodValue": "{start} till {end}", "planUnknown": "ännu inte känt", "preview": "Förhandsvisning", "priceLimit": "pris<PERSON><PERSON><PERSON><PERSON><PERSON> {price}", "remove": "<PERSON> bort", "setTargetTime": "ingen", "targetIsAboveLimit": "Kon<PERSON>gu<PERSON>t ladd<PERSON> {limit} kommer ignoreras denna tidsperiod.", "targetIsAboveVehicleLimit": "Fordonets laddgräns är under <PERSON><PERSON><PERSON>.", "targetIsInThePast": "<PERSON><PERSON><PERSON><PERSON> en tid i framtiden, <PERSON>.", "targetIsTooFarInTheFuture": "Vi kommer att justera planen så snart vi vet mer om framtiden.", "title": "<PERSON><PERSON><PERSON><PERSON>t<PERSON>", "today": "idag", "tomorrow": "i morgon", "update": "Uppdatera", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON> dig hur du konfigurerar.", "vehicleCapacityRequired": "Fordonets batterikapacitet behövs för att beräkna laddtid."}, "targetChargePlan": {"chargeDuration": "Laddtid", "co2Label": "CO₂-utsläpp ⌀", "priceLabel": "Energi<PERSON><PERSON>", "timeRange": "{day} {range} t", "unknownPrice": "<PERSON><PERSON><PERSON><PERSON> ok<PERSON>"}, "targetEnergy": {"label": "<PERSON><PERSON><PERSON><PERSON>", "noLimit": "ingen"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON> till fordon", "changeVehicle": "<PERSON>t fordon", "detectionActive": "Detekterar fordon…", "fallbackName": "<PERSON><PERSON>", "moreActions": "Fler åtgärder", "none": "<PERSON><PERSON> fordon", "notReachable": "Fordonet kunde inte kontaktas. Prova att starta om evcc.", "targetSoc": "<PERSON><PERSON><PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Temp<PERSON>g<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleSoc": "Laddning"}, "vehicleStatus": {"awaitingAuthorization": "Väntar på godkännande.", "batteryBoost": "Batteriboost är aktiv.", "charging": "<PERSON><PERSON><PERSON>…", "cheapEnergyCharging": "<PERSON><PERSON> energi är tillgäng<PERSON>g.", "cheapEnergyNextStart": "Billig energi om {duration}.", "cheapEnergySet": "Prisgr<PERSON><PERSON> spa<PERSON>.", "cleanEnergyCharging": "<PERSON><PERSON><PERSON><PERSON> energi <PERSON>.", "cleanEnergyNextStart": "<PERSON><PERSON><PERSON><PERSON> energi om {duration}.", "cleanEnergySet": "CO₂ gräns sparad.", "climating": "Förvärmning identifierad.", "connected": "Inkopplad.", "disconnectRequired": "Session avbruten. Prova att återansluta.", "disconnected": "Frånkopplad.", "feedinPriorityNextStart": "Snabb inmatning börjar om {duration}.", "feedinPriorityPausing": "Solladdning har pausats för att maximera inmatning.", "finished": "Färdig.", "minCharge": "Minimiladdning till {soc}.", "pvDisable": "Inte tillräckligt med överskott. Pausar strax.", "pvEnable": "Överskott tillgängligt. Börjar strax.", "scale1p": "Minskar till 1-fas-laddning strax.", "scale3p": "Ökar till 3-fas-laddning strax.", "targetChargeActive": "Laddplan aktiv. Beräknas vara klar om {duration}.", "targetChargePlanned": "Laddplan startar om {duration}.", "targetChargeWaitForVehicle": "Laddare beredd. Väntar på fordon…", "vehicleLimit": "<PERSON>onsgr<PERSON><PERSON>", "vehicleLimitReached": "Fordonets gräns n<PERSON>dd.", "waitForVehicle": "Redo. Väntar på fordon…", "welcome": "<PERSON>rt inledande laddning för att kontrollera förbindelsen."}, "vehicles": "<PERSON><PERSON>", "welcome": "Hej!"}, "notifications": {"dismissAll": "Avvisa alla", "logs": "Se kompletta loggar", "modalTitle": "Meddelanden"}, "offline": {"configurationError": "Fel under uppstart. Kontrollera din konfiguration och starta om.", "message": "Inte ansluten till en server.", "restart": "Omstart", "restartNeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> för att spara ändringar.", "restarting": "<PERSON><PERSON> är strax till<PERSON>ka."}, "passwordModal": {"description": "Använd lösenord för att skydda inställningarna. Huvudskärmen kan användas utan att logga in.", "empty": "Lösenordet får inte vara tomt", "error": "Fel: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelNew": "Nytt lösenord", "labelRepeat": "Upprepa lösenord", "newPassword": "Skapa lö<PERSON>ord", "noMatch": "Lösenorden är olika", "titleNew": "Ange administratörslösenord", "titleUpdate": "<PERSON>ndra administratörslösenord", "updatePassword": "<PERSON><PERSON>"}, "session": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "Period", "delete": "<PERSON><PERSON><PERSON>", "finished": "<PERSON><PERSON><PERSON><PERSON>", "meter": "Mätarställning", "meterstart": "Mätare start", "meterstop": "Mätare stopp", "odometer": "Mätarställning", "price": "<PERSON><PERSON>", "started": "Start-tid", "title": "Laddning"}, "sessions": {"avgPower": "⌀-effekt", "avgPrice": "⌀-pris", "chargeDuration": "Laddtid", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Pris {byGroup}", "byGroupLoadpoint": "per laddare", "byGroupVehicle": "per fordon", "energy": "<PERSON><PERSON><PERSON> energi", "energyGrouped": "Sol vs. nätenergi", "energyGroupedByGroup": "Energi {byGroup}", "energySubSolar": "{value} sol", "energySubTotal": "{value} total", "groupedCo2ByGroup": "CO₂-mängd {byGroup}", "groupedPriceByGroup": "Total kostnad {byGroup}", "historyCo2": "CO₂-Emissioner", "historyCo2Sub": "{value} total", "historyPrice": "Laddkostnad", "historyPriceSub": "{value} total", "solar": "Solandel över året", "solarByGroup": "<PERSON><PERSON><PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energi (kWh)", "chargeduration": "Laddtid", "co2perkwh": "CO₂/kWh", "created": "Starttid", "finished": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "Identifierare", "loadpoint": "Laddplats", "meterstart": "Mätare start (kWh)", "meterstop": "Mätare slut (kWh)", "odometer": "Mätarställning (km)", "price": "Kostnad", "priceperkwh": "Pris/kWh", "solarpercentage": "Sol (%)", "vehicle": "<PERSON><PERSON>"}, "csvPeriod": "Ladda hem {period} CSV", "csvTotal": "Ladda hem total CSV", "date": "Start", "energy": "Laddat", "filter": {"allLoadpoints": "alla laddplatser", "allVehicles": "alla fordon", "filter": "Filter"}, "group": {"co2": "Emissioner", "grid": "Nät", "price": "Kostnad", "self": "Sol"}, "groupBy": {"loadpoint": "<PERSON><PERSON><PERSON>", "none": "Total", "vehicle": "<PERSON><PERSON>"}, "loadpoint": "Laddplats", "noData": "<PERSON>ga laddningar denna m<PERSON>.", "overview": "Överblick", "period": {"month": "Månad", "total": "Total", "year": "<PERSON><PERSON>"}, "price": "Kostnad", "reallyDelete": "Vill du verkligen radera den här sessionen?", "showIndividualEntries": "<PERSON> enskilda sessioner", "solar": "Sol", "title": "Laddningar", "total": "Total", "type": {"co2": "CO₂", "price": "Kostnad", "solar": "Sol"}, "vehicle": "<PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Till helskärmsläge", "exit": "Lämna helskärmsläge", "label": "Helskärmsläge"}, "hiddenFeatures": {"label": "Experimentella", "value": "Visa experimentella UI-funktioner."}, "language": {"auto": "Automatisk", "label": "Språk"}, "sponsorToken": {"expires": "<PERSON> sponsortoken löper ut om {inXDays}. {getNewToken} och uppdatera här.", "getNew": "Hämta en ny", "hint": "Observera: <PERSON><PERSON> kommer att automatisera detta i framtiden."}, "telemetry": {"label": "Telemetri"}, "theme": {"auto": "system", "dark": "m<PERSON>rk", "label": "Design", "light": "ljus"}, "time": {"12h": "12h", "24h": "24h", "label": "Tidsformat"}, "title": "Användargränssnitt", "unit": {"km": "km", "label": "Enheter", "mi": "miles"}}, "smartCost": {"activeHours": "{active} av {total}", "activeHoursLabel": "Aktiva timmar", "applyToAll": "Tillämpa överallt?", "batteryDescription": "Laddar batteriet med el från nätet.", "cheapTitle": "<PERSON><PERSON> laddning från n<PERSON>et", "cleanTitle": "Laddar grön el från nätet", "co2Label": "CO₂ utsläpp", "co2Limit": "CO₂ gräns", "loadpointDescription": "Aktiverar tillfällig snabbladdning i sol-läge.", "modalTitle": "Smartladdning elnät", "none": "ingen", "priceLabel": "Energi<PERSON><PERSON>", "priceLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetAction": "Ta bort begr<PERSON>", "resetWarning": "Varken dynamiskt elpris eller CO₂-avtryck är konfigurerat. Emellertid finns fortfarande en begränsning på {limit}. Din konfiguration kanske behöver kontrolleras?", "saved": "Sparad."}, "smartFeedInPriority": {"activeHoursLabel": "<PERSON><PERSON><PERSON> timmar", "description": "Pausar ladning vid höga elpriser för att prioritera lönsam elnätsinmatning.", "priceLabel": "Inmatningshastighet", "priceLimit": "Inmatningsbegränsning", "resetWarning": "Det finns ingen konfigurerad dynamisk inmatningstariff. Däremot finns det en begränsning på {limit}. Städa i konfigurationen?", "title": "Inmatningsprioritet"}, "startupError": {"configFile": "Konfigurationsfil som används:", "configuration": "Konfiguration", "description": "Kontrollera din konfigurationsfil. Om felmeddelandet inte hjälper, kontrollera {0}.", "discussions": "GitHub-diskussioner", "fixAndRestart": "Vänligen åtgärda problemet och starta om servern.", "hint": "Observera: <PERSON> kan också vara så att du har en felaktig enhet (växelriktare, mätare, ...). Kontrollera dina nätverksanslutningar.", "lineError": "Fel i {0}.", "lineErrorLink": "linje {0}", "restartButton": "Starta om", "title": "Startfel"}}