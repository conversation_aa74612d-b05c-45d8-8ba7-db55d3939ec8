{"batterySettings": {"batteryLevel": "Ladenivå", "bufferStart": {"full": "n<PERSON>r det er nesten fullt", "never": "bare med nok PV-overskudd"}, "capacity": "{energy} av {total}", "disclaimerHint": "Merk:", "disclaimerText": "Kun relevant i PV-modus. Ladeadferden justeres deretter.", "legendBottomName": "Hjemmelading har prioritet", "legendBottomSubline": "ikke bruk til lading", "legendMiddleName": "kjøretøy først", "legendMiddleSubline": "hje<PERSON> etterpå", "legendTopAutostart": "starter automatisk", "legendTopName": "batteriassistert lading", "legendTopSubline": "uten avbrudd", "modalTitle": "Batteriinnstillinger"}, "config": {"form": {"example": "Eksempel", "optional": "v<PERSON><PERSON><PERSON><PERSON>t"}, "main": {"addVehicle": "Legg til kjøretøy", "edit": "rediger", "title": "<PERSON><PERSON><PERSON>", "vehicles": "Mine kjøretøy"}, "validation": {"failed": "mislykket", "label": "Status", "running": "bek<PERSON><PERSON> …", "success": "vellykket", "unknown": "ukjent", "validate": "bekreft"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Slett kjøretøy", "generic": "<PERSON>", "online": "Kjøretøy med nettbasert API", "save": "Lagre", "scooter": "<PERSON>ooter", "template": "Fabrikat", "titleAdd": "Legg til kjøretøy", "titleEdit": "<PERSON><PERSON> k<PERSON>ø<PERSON>ø<PERSON>", "validateSave": "Bekreft og lagre"}}, "footer": {"community": {"greenEnergy": "Solenergi", "greenEnergySub1": "ladet med evcc", "greenEnergySub2": "siden oktober 2022", "greenShare": "Solenergiandel", "greenShareSub1": "kraft levert av", "greenShareSub2": "solenergi, og batterilagring", "power": "Ladeeffekt", "powerSub1": "{activeClients} av {totalClients} deltagere", "powerSub2": "lader...", "tabTitle": "Interessefellesskap"}, "savings": {"footerLong": "{percent} solcelleenergi", "footerShort": "{percent} solenergi", "modalTitle": "Oversikt over lad<PERSON><PERSON>gi", "percentGrid": "{self} kWh lysnett", "percentSelf": "{self} kWt sol", "percentTitle": "Solenergi", "priceTitle": "<PERSON><PERSON><PERSON><PERSON>", "tabTitle": "Mine data"}, "sponsor": {"becomeSponsor": "Bli sponsor", "confetti": "Klar for konfetti?", "confettiPromise": "Du får klistremerker og digital konfetti", "sticker": "... eller evcc-klistremerker?", "supportUs": "<PERSON><PERSON>rt oppdrag: gjø<PERSON> solener<PERSON> til normen. Hjelp oss ved å støtte evcc økonomisk.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! <PERSON>tt bidrag bidrar til å utvikle evcc videre.", "titleNoSponsor": "<PERSON><PERSON><PERSON> oss", "titleSponsor": "Du er en støttes<PERSON>ller"}, "telemetry": {"optIn": "Jeg ønsker å bidra med mine data.", "optInMoreDetails": "<PERSON><PERSON><PERSON> {0}.", "optInMoreDetailsLink": "her", "optInSponsorship": "Sponsing kreves."}, "version": {"availableLong": "<PERSON><PERSON> vers<PERSON>", "modalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modalDownload": "Last ned", "modalInstalledVersion": "Installert versjon", "modalNoReleaseNotes": "Ingen versjonsmerknader tilgjengelig. Mer info om den nye versjonen:", "modalTitle": "<PERSON><PERSON> vers<PERSON>", "modalUpdate": "Installer", "modalUpdateNow": "Installer nå", "modalUpdateStarted": "Starter den nye versjonen av evcc...", "modalUpdateStatusStart": "Installasjonen startet:"}}, "header": {"about": "Om", "blog": "Blogg", "docs": "Dokumentasjon", "github": "GitHub", "login": "Kjøretøysinnlogginger", "needHelp": "<PERSON><PERSON>ger du h<PERSON>?", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "help": {"discussionsButton": "GitHub-diskusjoner", "documentationButton": "Dokumentasjon", "issueButton": "Rapporter feil", "issueDescription": "Funnet noe merkelig eller feil?", "modalTitle": "<PERSON><PERSON>ger du h<PERSON>?", "primaryActions": "Er det noe som ikke virker som det skal? Dette er gode ressurser å starte med.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON>, start på ny.", "description": "I normale fall bør ikke omstart være nødvendig. Overvei å sende en feilrapport hvis du må starte evcc på ny ofte.", "disclaimer": "Merk: evcc vil avsluttes og be operativsystemet om å starte enheten på ny.", "modalTitle": "Er du sikker på at du vil utføre omstart?"}, "restartButton": "Start på ny", "restartDescription": "Prøvd å slå av og på igjen?", "secondaryActions": "<PERSON><PERSON><PERSON><PERSON> problemet? Her har du mer drastiske tiltak."}, "main": {"chargingPlan": {"arrivalTab": "Ankomst", "departureTab": "Avgang", "modalTitle": "Ladeplan", "none": "ingen", "title": "Plan", "titleMinSoc": "Min.-lading", "titleTargetCharge": "Avgang"}, "energyflow": {"battery": "<PERSON><PERSON><PERSON>", "batteryCharge": "Batterilading", "batteryDischarge": "Batteri utlading", "batteryTooltip": "{energy} av {total} ({soc})", "gridImport": "Lysnettimport", "homePower": "Forbruk", "loadpoints": "Lader| Lader | {count} ladere", "noEnergy": "Ingen telleverksdata", "pvExport": "Lysnetteksport", "pvProduction": "Produksjon", "selfConsumption": "<PERSON><PERSON> forbruk"}, "loadpoint": {"avgPrice": "⌀-pris", "charged": "<PERSON><PERSON><PERSON><PERSON>", "co2": "⌀-CO₂", "duration": "<PERSON><PERSON><PERSON><PERSON>", "fallbackName": "Ladepunkt", "power": "Effekt", "price": "Σ-pris", "solar": "Solenergi"}, "loadpointSettings": {"currents": "Ladestrøm", "default": "<PERSON><PERSON><PERSON>", "disclaimerHint": "Merknad:", "maxCurrent": {"label": "Maks<PERSON> lad<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "For nødstilfeller. Kjøretøyet blir „raskt” ladet til {0} fra all tilgjengelig solenergi, og fortsetter deretter med bare solenergioverskuddet.", "label": "Min. lading%"}, "phasesConfigured": {"label": "<PERSON><PERSON><PERSON>", "phases_0": "automatisk veksling", "phases_1": "1-fase", "phases_1_hint": "({min} til {max})", "phases_3": "3-fase", "phases_3_hint": "({min} til {max})"}, "title": "<PERSON><PERSON><PERSON><PERSON> for {0}", "vehicle": "Kjøretøy"}, "mode": {"minpv": "Min+Sol-e.", "now": "Raskt", "off": "Stopp", "pv": "Sol"}, "provider": {"login": "logg inn", "logout": "logg ut"}, "targetCharge": {"activate": "Aktiver", "co2Limit": "CO₂-gren<PERSON> p<PERSON> {co2}", "costLimitIgnore": "Ser bort fra oppsatt grense på {limit} i løpet av denne perioden.", "descriptionEnergy": "<PERSON><PERSON><PERSON> skal {targetEnergy} være på kjøretøyet?", "descriptionSoc": "<PERSON><PERSON><PERSON> skal kjøretøyet lades til {targetSoc}?", "inactiveLabel": "Stopptidspunkt", "onlyInPvMode": "Ladeplan fungerer kun i solcellemodus.", "planDuration": "<PERSON><PERSON><PERSON>", "planPeriodLabel": "Periode", "planPeriodValue": "{start} til {end}", "planUnknown": "ikke kjent enda", "priceLimit": "prisgrense på {price}", "setTargetTime": "ingen", "targetIsInThePast": "<PERSON><PERSON>g et tidspunkt i fremtiden, Marty.", "targetIsTooFarInTheFuture": "Planen vil bli justert så snart mer info tilkommer.", "title": "<PERSON><PERSON><PERSON><PERSON>t<PERSON>", "today": "i dag", "tomorrow": "i morgen", "update": "<PERSON><PERSON><PERSON><PERSON>"}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON>", "co2Label": "CO₂-utslipp ⌀", "priceLabel": "Energi<PERSON><PERSON>", "timeRange": "{day} {range} t", "unknownPrice": "frem<PERSON>es ukjent"}, "targetEnergy": {"label": "<PERSON><PERSON><PERSON>", "noLimit": "ingen"}, "vehicle": {"addVehicle": "Legg til kjøretøy", "changeVehicle": "<PERSON><PERSON>", "detectionActive": "Oppda<PERSON> k<PERSON>ø<PERSON>ø<PERSON> ...", "fallbackName": "Kjøretøy", "moreActions": "Flere handlinger", "none": "Ingen kjøretøy", "targetSoc": "<PERSON><PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Temp.-grense", "unknown": "Gjestekjøretøy", "vehicleSoc": "Lade"}, "vehicleStatus": {"charging": "Lading...", "cheapEnergyCharging": "<PERSON><PERSON> energi tilgjengelig. Lader …", "cleanEnergyCharging": "Ren energi tilgjengelig. Lader …", "climating": "Klimaanlegg på forhånd oppdaget.", "connected": "Tilkoblet.", "disconnected": "Frakoblet.", "minCharge": "Minimumslading til {soc}.", "pvDisable": "Ikke nok overskudd. Pause i {remaining}...", "pvEnable": "Overskudd tilgjengelig. Starter om {remaining}...", "scale1p": "Reduserer til 1-fase strøm i {remaining}...", "scale3p": "Øker til 3-fase strøm i {remaining}...", "targetChargeActive": "Målladning aktiv ...", "targetChargePlanned": "Mållading starter p<PERSON> {time}.", "targetChargeWaitForVehicle": "Mållading klar. Venter på kjøretøy...", "vehicleLimitReached": "Kjøretøygrense på {soc} nådd.", "waitForVehicle": "<PERSON><PERSON>. Venter på kjøretøy..."}, "vehicles": "<PERSON><PERSON>"}, "notifications": {"dismissAll": "Forkast alle", "modalTitle": "Merknader"}, "offline": {"message": "<PERSON><PERSON><PERSON> koblet til en server."}, "session": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "Tidsrom", "delete": "<PERSON><PERSON>", "finished": "<PERSON><PERSON><PERSON><PERSON>", "meter": "Måleravlesning", "meterstart": "Start-målerstand", "meterstop": "Stopp-målerstand", "odometer": "Kilometerstand", "price": "<PERSON><PERSON>", "started": "Startet", "title": "Ladeøkt"}, "sessions": {"avgPower": "⌀-effekt", "avgPrice": "⌀-pris", "chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "co2": "⌀-CO₂", "csv": {"chargedenergy": "Energi (kWh)", "created": "Opprettet", "finished": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Ladepunkt", "meterstart": "Start-målerstand (kWh)", "meterstop": "Stopp-målerstand (kWh)", "odometer": "Kilometerstand", "vehicle": "Kjøretøy"}, "csvPeriod": "Last ned (måned) CSV", "csvTotal": "Last ned total CSV", "date": "Start", "energy": "<PERSON><PERSON><PERSON><PERSON>", "filter": {"allLoadpoints": "alle ladepunkter", "allVehicles": "alle kjøretøy", "filter": "Filter"}, "loadpoint": "Ladepunkt", "noData": "Ingen ladeøkter denne måneden.", "price": "Σ-pris", "reallyDelete": "<PERSON><PERSON> denne økten?", "solar": "Solenergi", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Totalt", "vehicle": "Kjøretøy"}, "settings": {"hiddenFeatures": {"label": "Eksperimentelt", "value": "Vis eksperimentelle grensesnittsfunksjoner."}, "language": {"auto": "Automatisk", "label": "Språk"}, "sponsorToken": {"expires": "Sponsorsymbolet ditt utløper om {inXDays}. {getNewToken} og oppdater oppsettsfilen din.", "getNew": "<PERSON><PERSON> en ny token", "hint": "Merk: Dette vil bli automatisert i fremtiden."}, "telemetry": {"label": "Telemetri"}, "theme": {"auto": "system", "dark": "<PERSON>ørk", "label": "Design", "light": "lys"}, "title": "Innstillinger", "unit": {"km": "km", "label": "Enheter", "mi": "mil"}}, "smartCost": {"activeHours": "{active} av {total}", "activeHoursLabel": "Aktive timer", "co2Label": "CO₂-utslipp", "co2Limit": "CO₂-grense", "loadpointDescription": "Slår på midlertidig hurtiglading i solcellemodus.", "modalTitle": "Smart lysnettlading", "none": "ingen", "priceLabel": "Energi<PERSON><PERSON>", "priceLimit": "Prisgren<PERSON>"}, "startupError": {"configFile": "Brukt oppsettsfil:", "configuration": "<PERSON><PERSON><PERSON>", "description": "Vennligst sjekk konfigurasjonsfilen din. Hvis feilmeldingen ikke h<PERSON>, kan du sjekke ut {0}.", "discussions": "GitHub-diskusjoner", "fixAndRestart": "Vennligst fiks problemet og start serveren på nytt.", "hint": "Merk: Det kan også være at du har en defekt enhet (omformer, må<PERSON>, ...). Sjekk nettverkstilkoblingene dine.", "lineError": "Feil i {0}.", "lineErrorLink": "linje {0}", "restartButton": "Omstart", "title": "Oppstartsfeil"}}