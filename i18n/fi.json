{"batterySettings": {"batteryLevel": "<PERSON><PERSON><PERSON>", "bufferStart": {"above": "kun ylittää {soc}.", "full": "kun {soc}.", "never": "ainoastaan kun riittävästi ylijäämää."}, "capacity": "{energy} / {total}", "control": "<PERSON><PERSON><PERSON> hall<PERSON>a", "discharge": "Vältä purkamista nopeassa tilassa ja suunnitellussa lata<PERSON>.", "disclaimerHint": "<PERSON><PERSON><PERSON>:", "disclaimerText": "Nämä asetukset vaikuttavat vain aurinkot<PERSON>. Latauskäyttäytymistä säädetään vast<PERSON>vasti.", "gridChargeTab": "La<PERSON><PERSON> verkosta", "legendBottomName": "Priorisoi kodin akun lataus", "legendBottomSubline": "kunnes saavute<PERSON>an {soc}.", "legendMiddleName": "Priorisoi ajoneuvon lataus", "legendMiddleSubline": "kun kodin akku on yli {soc}.", "legendTopAutostart": "Aloita automaattisesti", "legendTopName": "<PERSON><PERSON><PERSON> tuettu a<PERSON> lataus", "legendTopSubline": "kun kodin akku on yli {soc}.", "modalTitle": "<PERSON><PERSON> a<PERSON>ku", "usageTab": "<PERSON><PERSON><PERSON> k<PERSON>"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka kulutusta voi säädellä ylijäämätuotannon mukaan (kuten älykäs lämminvesivaraaja). EVCC olettaa, että tämä laite pystyy tarvittaessa pienentämään sähkönkulutustaan.", "titleAdd": "Lisää kulutustaan itsesäätelevä sähkölaite", "titleEdit": "Muokkaa kulutustaan itsesäätelevää sähkölaitetta"}, "battery": {"titleAdd": "Lisää akku", "titleEdit": "Muokkaa akkua"}, "charge": {"titleAdd": "Lisää latausmittari", "titleEdit": "Muokkaa latausmittaria"}, "charger": {"chargers": "Sähköauton la<PERSON>", "generic": "<PERSON><PERSON><PERSON>t integraatiot", "heatingdevices": "Lämmityslaitteet", "ocppHelp": "Ko<PERSON>i tämä osoite laturien määrityksiin.", "ocppLabel": "OCPP-palvelimen URL", "switchsockets": "Kytkettävät pistorasiat", "template": "<PERSON><PERSON><PERSON>", "titleAdd": {"charging": "Lisää laturi", "heating": "Lisää lämmitin"}, "titleEdit": {"charging": "Muokkaa laturia", "heating": "Muokkaa lämmitintä"}, "type": {"custom": {"charging": "Itse määritelty laturi", "heating": "Itse määritelty lämmitin"}, "heatpump": "Itse määritelty lämpöpumppu", "sgready": "Itse määritelty lämpöpumppu (älyverk<PERSON><PERSON>ius, kaik<PERSON>)", "sgready-boost": "Itse määritelty lämpöpumppu (älyverk<PERSON><PERSON>, tehostus)", "switchsocket": "Itse määritelty relepistorasia"}}, "circuits": {"description": "Varmistaa, että kaikkien piiriin kytkettyjen kuormituspisteiden summa ei ylitä määritettyjä teho- ja virtarajoja. Piirejä voi rakentaa sisäkkäin.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "control": {"description": "Yleensä oletusarvot ovat sopivat. Muuta vain jos tiedät mitä olet tekemässä.", "descriptionInterval": "Ohjaussilmukan päivitysjakso sekunneissa. <PERSON><PERSON><PERSON><PERSON><PERSON>ää, kuinka usein evcc lukee mittaritietoja, säätää lataustehoa ja päivittää käyttöliittymän. Lyhyet välit alle (30 s) voivat aiheuttaa oskillaatiota ja ei-toivottua toimintaa.", "descriptionResidualPower": "Siirtää ohjaussilmukan toimintapistettä. <PERSON><PERSON> on kodissa akku on suositeltavaa asettaa arvo 100 W. Näin akku on hieman etusijalla sähköverkon käyttöön nähden.", "labelInterval": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelResidualPower": "Jäännösteho", "title": "Määrittele toimintaa"}, "deviceValue": {"amount": "Määrä", "broker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Broker)", "bucket": "Bucket", "capacity": "<PERSON><PERSON><PERSON><PERSON>", "chargeStatus": "Tila", "chargeStatusA": "ei <PERSON><PERSON>", "chargeStatusB": "<PERSON><PERSON><PERSON><PERSON>", "chargeStatusC": "lataa", "chargeStatusE": "ei virtaa", "chargeStatusF": "virhe", "chargedEnergy": "Ladattu", "co2": "Verkon CO₂", "configured": "Määritetty", "controllable": "Hallittava", "currency": "<PERSON><PERSON><PERSON><PERSON>", "current": "Virta", "currentRange": "Virta", "enabled": "Käytössä", "energy": "Energia", "feedinPrice": "Sähköverkkoon myynninhinta", "gridPrice": "Verkos<PERSON> o<PERSON>", "heaterTempLimit": "Lämmityksen raja", "hemsType": "Järjestelmä", "identifier": "RFID-tunniste", "no": "ei", "odometer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "org": "Organisaatio", "phaseCurrents": "Virta L1, L2, L3", "phasePowers": "Teho L1, L2, L3", "phaseVoltages": "Jännite L1, L2, L3", "phases1p3p": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "power": "Teho", "powerRange": "Teho", "range": "To<PERSON>intasäde", "singlePhase": "Yksivaiheinen", "soc": "<PERSON><PERSON><PERSON>", "solarForecast": "Aurinkosääennuste", "temp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "topic": "<PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yes": "kyllä"}, "deviceValueChargeStatus": {"A": "A (ei <PERSON><PERSON>)", "B": "B (<PERSON><PERSON><PERSON><PERSON>)", "C": "C (lataa)"}, "devices": {"auxMeter": "Älykkään virranhallinnan laite", "batteryStorage": "Akkujärjestelmä", "solarSystem": "Aurinkosähköjärjestelmä"}, "editor": {"loading": "Ladataan YAML-tiedostoeditoria…"}, "eebus": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka avulla evcc voi kommunikoida muiden EEBus-laitteiden kanssa.", "title": "EEBus"}, "ext": {"description": "Voidaan käyttää kuormanhallintaan tai tilastointiin.", "titleAdd": "Lisää ulkoinen mittari", "titleEdit": "Muokkaa ulkoista mittaria"}, "form": {"danger": "<PERSON><PERSON><PERSON>", "deprecated": "<PERSON><PERSON><PERSON><PERSON>", "example": "Esimerkiksi", "optional": "<PERSON><PERSON><PERSON><PERSON>"}, "general": {"cancel": "Peruuta", "customHelp": "Luo itsemääritelty laite käyttäen EVCC:n lisäosaa.", "customOption": "Itsemääritelty laite", "delete": "Poista", "docsLink": "<PERSON><PERSON> do<PERSON>.", "experimental": "<PERSON><PERSON><PERSON><PERSON>", "hideAdvancedSettings": "<PERSON><PERSON><PERSON> edist<PERSON> as<PERSON>uk<PERSON>", "off": "pois", "on": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "readFromFile": "<PERSON><PERSON>", "remove": "Poista", "save": "<PERSON><PERSON><PERSON>", "showAdvancedSettings": "Näytä edistyneet asetukset", "telemetry": "Telemetria", "templateLoading": "Ladataan...", "title": "<PERSON><PERSON><PERSON><PERSON>", "validateSave": "Vahvista ja tallenna"}, "grid": {"title": "Sähköverkonmittari", "titleAdd": "Lisää sähköverkonmittari", "titleEdit": "Muokkaa sähköverkonmittaria"}, "hems": {"description": "Yhdistä evcc toiseen kodin energianhallintajärjestelmään.", "title": "HEMS"}, "icon": {"change": "v<PERSON><PERSON><PERSON>"}, "influx": {"description": "Kirjoittaa kulutustiedot ja muut mittaukset InfluxDB:hen. <PERSON><PERSON><PERSON><PERSON> tai muita työkaluja tietojen visualisointiin.", "descriptionToken": "Tarkista InfluxDB-dokumentaatiosta, kuinka voit luoda sellaisen. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Salli itse allekirjoitetut varmenteet", "labelDatabase": "Tietokanta", "labelInsecure": "Varmenteen vahvistaminen", "labelOrg": "Organisaatio", "labelPassword": "<PERSON><PERSON><PERSON>", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "K<PERSON>yttäjät<PERSON>nus", "title": "InfluxDB", "v1Support": "Tarvitsetko tukea InfluxDB 1.x:lle?", "v2Support": "Takaisin InfluxDB 2.x:<PERSON><PERSON>n"}, "loadpoint": {"addCharger": {"charging": "Lisää laturi", "heating": "Lisää lämmitin"}, "addMeter": "Lisää erillinen kulutusmittari", "cancel": "Peruuta", "chargerError": {"charging": "Vaaditaan k<PERSON>yttöönottoasetusten määrittely laturille.", "heating": "Vaaditaan lämmittimen asetuksien määrittely."}, "chargerLabel": {"charging": "<PERSON><PERSON><PERSON>", "heating": "Lämmitin"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Käyttää 6-16 A:n virta-aluetta.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Käyttää 6-32 A:n virta-aluetta.", "chargerPowerCustom": "muu", "chargerPowerCustomHelp": "Mukauta käytettävä virta-alue.", "chargerTypeLabel": "<PERSON><PERSON><PERSON> t<PERSON>", "chargingTitle": "<PERSON><PERSON><PERSON><PERSON>", "circuitHelp": "Kuormanhallinnan mä<PERSON> varmistaa, että teho ja virtarajoitteita ei ylitetä.", "circuitLabel": "Piiri", "circuitUnassigned": "määräämätön", "defaultModeHelp": {"charging": "Lataustila ajoneuvoa kytkettäessä.", "heating": "Asetetaan päälle järjestelmän käynnistyessä."}, "defaultModeHelpKeep": "Säilyttää viimeksi valitun lataus/k<PERSON><PERSON><PERSON><PERSON><PERSON>.", "defaultModeLabel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Poista", "electricalSubtitle": "<PERSON><PERSON>, k<PERSON><PERSON> s<PERSON>nta<PERSON>.", "electricalTitle": "Sähköinen", "energyMeterHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jos laturissa ei ole integroitua energiamittaria.", "energyMeterLabel": "Energiamittari", "estimateLabel": "Arvioi lataustaso API-päivitysten välillä", "maxCurrentHelp": "On oltava suurempi kuin minimivirta.", "maxCurrentLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minCurrentHelp": "Valitse alle 6 A vain, jos <PERSON><PERSON> mitä olet tekemässä.", "minCurrentLabel": "Minimivirta", "noVehicles": "Ajoneuvoja ei ole mää<PERSON>tty.", "option": {"charging": "Lisää latauspiste", "heating": "Lisää lämmityslaite"}, "phases1p": "1-v<PERSON><PERSON>", "phases3p": "3-v<PERSON><PERSON>", "phasesAutomatic": "Automaattinen", "phasesAutomaticHelp": "Laturisi tukee automaattista vaihtoa 1- ja 3-vaiheisen latauksen välillä. Päänäytössä voit säätää vaihekäyttäytymistä latauksen aikana.", "phasesHelp": "Kytkettyjen vaiheiden lukumäärä.", "phasesLabel": "Vaiheet", "pollIntervalDanger": "Ajoneuvon akku voi tyhjentyä tiheiden säännöllisten tietopyyntöjen seurauk<PERSON>a. Joidenkin autovalmistajien ajoneuvot voivat estää tällöin ajoneuvon lataamisen. Ei suositella! Käytä ainoastaan tiedostaen riskit.", "pollIntervalHelp": "Ajoneuvon API-päivitysten välinen aika. Lyhyet välit voivat tyhjentää ajoneuvon akun.", "pollIntervalLabel": "Päivitysväli", "pollModeAlways": "aina", "pollModeAlwaysHelp": "Pyydä aina tilapäivityksiä säännöllisin väliajoin.", "pollModeCharging": "lataa", "pollModeChargingHelp": "Pyydä ajoneuvon tilapäivityksiä vain latauksen aikana.", "pollModeConnected": "<PERSON><PERSON><PERSON><PERSON>", "pollModeConnectedHelp": "Päivitä ajoneuvon tila säännöllisin väliajoin, kun yhteys on muodostettu.", "pollModeLabel": "<PERSON>ä<PERSON><PERSON>", "priorityHelp": "Korkeamman prioriteetin laitteet saavat ensisijaisen pääsyn aurinkoenergian ylijäämään.", "priorityLabel": "Prior<PERSON>etti", "save": "<PERSON><PERSON><PERSON>", "showAllSettings": "Näytä kaikki as<PERSON>", "solarBehaviorCustomHelp": "Määritä omat pä<PERSON>- ja poiskytkentä sekä viiveet.", "solarBehaviorDefaultHelp": "<PERSON><PERSON><PERSON> {enableDelay} jälk<PERSON>. <PERSON><PERSON><PERSON>, kun ylijäämää ei ole tarpeeksi {disableDelay}:lle.", "solarBehaviorLabel": "Aurinkoenergia", "solarModeCustom": "muo<PERSON><PERSON>", "solarModeMaximum": "maksimi P<PERSON>", "thresholdDisableDelayLabel": "Poista viive käytöstä", "thresholdDisableHelpInvalid": "Käytä positiivista arvoa.", "thresholdDisableHelpPositive": "<PERSON><PERSON><PERSON>, kun verkosta käytetään enemmän kuin {power}, {delay} ajaksi.", "thresholdDisableHelpZero": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kun vähimm<PERSON>istehoa ei voida täyttää {delay} ajaksi.", "thresholdDisableLabel": "Poista verkkoenergia käytöstä", "thresholdEnableDelayLabel": "Ota viive käyttöön", "thresholdEnableHelpInvalid": "Käytä negatiivista arvoa.", "thresholdEnableHelpNegative": "<PERSON><PERSON><PERSON>, kun {surplus} ylijäämää on käytettävissä {delay}.", "thresholdEnableHelpZero": "<PERSON><PERSON><PERSON>, kun vä<PERSON>m<PERSON>isteh<PERSON> on käytettävissä {delay}.", "thresholdEnableLabel": "Ota verkkoenergia käyttöön", "titleAdd": {"charging": "Lisää latauspiste", "heating": "Lisää lämmityslaite", "unknown": "Lisää latauslaite tai lämmitin"}, "titleEdit": {"charging": "Muokkaa latauspistettä", "heating": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "unknown": "Muokkaa latauslai<PERSON>tta tai lämmitintä"}, "titleExample": {"charging": "Autotalli, autokatos, yms.", "heating": "Lämpö<PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON><PERSON>, yms."}, "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "vehicleAutoDetection": "automaattinen tunnistus", "vehicleHelpAutoDetection": "Valitsee automaattisesti todennäköisimmän ajoneuvon. Manuaalinen ohitus on mahdollista.", "vehicleHelpDefault": "<PERSON><PERSON><PERSON> aina, että tämä ajoneuvo latautuu täällä. Automaattinen tunnistus poistettu käytöstä. Manuaalinen ohitus on mah<PERSON>llista.", "vehicleLabel": "Ole<PERSON><PERSON><PERSON>u<PERSON>", "vehiclesTitle": "Ajoneuvot"}, "main": {"addAdditional": "Lisää lisämittari", "addGrid": "Lisää verkkomittari", "addLoadpoint": "Lisää latauslaite tai lämmitin", "addPvBattery": "Lisää aurinkovoimala tai akusto", "addTariffs": "Lisää tariffit", "addVehicle": "Lisää ajoneuvo", "configured": "asetettu", "edit": "muokkaa", "loadpointRequired": "Täytyy määrittää vähintään yksi lata<PERSON>.", "name": "<PERSON><PERSON>", "title": "Määritykset", "unconfigured": "ei m<PERSON><PERSON>", "vehicles": "<PERSON><PERSON>", "yaml": "Laitteita jotka ovat mää<PERSON>tty evcc.yaml, ei voi muokata."}, "messaging": {"description": "Vastaanota viestejä koskien lataustapahtumiasi.", "title": "Ilmoitukset"}, "meter": {"cancel": "Peruuta", "delete": "Poista", "generic": "<PERSON><PERSON><PERSON>t integraatiot", "option": {"aux": "Lisää kulutustaan itsesäätelevä laite", "battery": "Lisää akun tilan mittari", "ext": "Lisää ulkopuolinen mittari", "pv": "Lisää aurinkovoimalan mittari"}, "save": "<PERSON><PERSON><PERSON>", "specific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template": "<PERSON><PERSON><PERSON>", "titleChoice": "Mitä haluat lisätä?", "validateSave": "Vahvista ja tallenna"}, "modbus": {"baudrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comset": "ComSet", "connection": "Modbus-yhteys", "connectionHintSerial": "Laite on <PERSON><PERSON><PERSON><PERSON> suoraan EVCC:n RS485-<PERSON><PERSON><PERSON><PERSON><PERSON>.", "connectionHintTcpip": "Laite on saavutettavissa LAN/WiFi-yhteydellä EVCC:stä.", "connectionValueSerial": "Sarjaportti / USB", "connectionValueTcpip": "Verkko", "device": "<PERSON><PERSON><PERSON><PERSON>", "deviceHint": "Esim. /dev/ttyUSB0", "host": "IP-osoite tai verkkotunnus", "hostHint": "Esim. *********", "id": "Modbus ID", "port": "<PERSON><PERSON>", "protocol": "Modbus-protokolla", "protocolHintRtu": "RS485-<PERSON><PERSON><PERSON><PERSON>-sovittimen kautta ilman protokoll<PERSON>.", "protocolHintTcp": "Laitteessa on sisäänrakennettu LAN/WiFi-tuki tai RS485-yhteydessä Ethernet-sovittimen kautta ilman protokollamuunnosta.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Salli useiden asiakkaiden käyttää yhtä Modbus-laitetta.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "To<PERSON><PERSON>", "description": "Yhdistä MQTT-välittäjään vaihtaaksesi tietoja muiden verkossasi olevien järjestelmien kanssa.", "descriptionClientId": "Viestien kirjoittaja. <PERSON><PERSON> `evcc-[rand]` k<PERSON>ytetään.", "descriptionTopic": "<PERSON><PERSON><PERSON>, jos haluat poistaa julkaisut k<PERSON>ytöstä.", "labelBroker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Broker)", "labelCaCert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CA)", "labelCheckInsecure": "Salli itse-allekirjoitetut varmenteet", "labelClientCert": "Asiakasvarmenne", "labelClientId": "Asiakas ID", "labelClientKey": "Asiakasavain", "labelInsecure": "Sertifikaatin todentaminen", "labelPassword": "<PERSON><PERSON><PERSON>", "labelTopic": "<PERSON><PERSON>", "labelUser": "K<PERSON>yttäjät<PERSON>nus", "publishing": "Julkai<PERSON>", "title": "MQTT"}, "network": {"descriptionHost": "Ota mDNS käyttöön käyttämällä .local-liitettä. Olennaista mobiilisovelluksen ja joidenkin OCPP-laturien löytämisen kannalta.", "descriptionPort": "Portti verkkokäyttöliittymälle ja API:lle. <PERSON> on päivitettävä selaimesi URL-osoite, jos muutat tätä.", "descriptionSchema": "Vaikuttaa vain URL-osoitteiden luomiseen. HTTPS:n valitseminen ei ota salausta k<PERSON>yttöön.", "labelHost": "Isäntänimi", "labelPort": "<PERSON><PERSON>", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Verkko"}, "options": {"boolean": {"no": "ei", "yes": "kyllä"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standby": "Valmiustila"}, "schema": {"http": "HTTP (salaamaton)", "https": "HTTPS (salattu)"}, "status": {"A": "A (ei <PERSON><PERSON>)", "B": "B (<PERSON><PERSON><PERSON><PERSON>)", "C": "C (lataa)"}}, "pv": {"titleAdd": "Lisää aurinkovoimalan mittari", "titleEdit": "Muokkaa aurinkovoimalan mittaria"}, "section": {"additionalMeter": "Ylimääräiset mittarit", "general": "<PERSON><PERSON><PERSON>", "grid": "Sähköverkko", "integrations": "Integraatiot", "loadpoints": "La<PERSON>us- ja lä<PERSON>t", "meter": "Aurinko & Akku", "system": "Järjestelmä", "vehicles": "Ajoneuvot"}, "sponsor": {"addToken": "<PERSON> tun<PERSON>", "changeToken": "<PERSON><PERSON><PERSON><PERSON> tunnus", "description": "Sponsorointi auttaa meitä ylläpitämään projektia ja rakentamaan kestävästi uusia ja jännittäviä ominaisuuksia. Sponsorina saat käyttöösi kaikki latureiden toteutukset.", "descriptionToken": "Saat tunnuksen osoitteesta {url}. Tarjoamme my<PERSON> kokeilutunnuksen testausta varten.", "error": "Sponsoritunnus ei kelpaa.", "labelToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenRequired": "Sinun on määritettävä sponsoritunnus ennen kuin voit luoda tämän la<PERSON>en.", "tokenRequiredLearnMore": "Lisätietoja.", "trialToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "system": {"logs": "<PERSON><PERSON>", "restart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "restartRequiredDescription": "Ole hyvä ja käynn<PERSON>, jotta nä<PERSON> mu<PERSON>.", "restartRequiredMessage": "Määritykset muutettu.", "restartingDescription": "Ole hyvä ja odota…", "restartingMessage": "Käynnistetään uudelleen evcc."}, "tariffs": {"description": "Määrittele energiatariffisi, jotta latausistunt<PERSON><PERSON><PERSON> kustann<PERSON> lasket<PERSON>.", "title": "Tariffit"}, "title": {"description": "Näytetään pääikkunassa ja selaimen välilehdessä.", "label": "<PERSON><PERSON><PERSON><PERSON>", "title": "Muokkaa otsik<PERSON>a"}, "validation": {"failed": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Tila", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "success": "<PERSON><PERSON><PERSON>", "unknown": "tunt<PERSON><PERSON>", "validate": "vahvista"}, "vehicle": {"cancel": "Peruuta", "chargingSettings": "<PERSON><PERSON><PERSON>", "defaultMode": "<PERSON><PERSON><PERSON><PERSON>", "defaultModeHelp": "Lataustila ajoneuvoa kytkettäessä.", "delete": "Poista", "generic": "<PERSON><PERSON> integra<PERSON>", "identifiers": "RFID-tunnisteet", "identifiersHelp": "Lista RFID-tunnisteista, jotka yksilöivät ajoneuvon. Yksi tunniste/rivi. Nykyinen tunniste löytyy kyseisen latausaseman yleisnäkymästä.", "maximumCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maximumCurrentHelp": "Täytyy olla minimivirtaa suurempi.", "maximumPhases": "Vaiheiden maksimimäärä", "maximumPhasesHelp": "<PERSON><PERSON><PERSON> monella vaiheella ajoneuvo voi vastaanottaa latausta? Tietoa käytetään minimiaurikosähkön ylituotannon laskemiseen ja latauskeston aikataulusuunnitteluun.", "minimumCurrent": "Minimivirta", "minimumCurrentHelp": "Aseta arvoksi alle 6A ainoastaan jos tiedät mitä olet tekemässä.", "online": "<PERSON><PERSON><PERSON><PERSON><PERSON>, joissa <PERSON>-käyttöliittymä", "primary": "<PERSON><PERSON><PERSON><PERSON> intergraatiot", "priority": "Prior<PERSON>etti", "priorityHelp": "Korkeampi prioriteetti merkitsee, että ajoneuvo saa etuoikeuden aurinkosähkön ylituottoon.", "save": "<PERSON><PERSON><PERSON>", "scooter": "<PERSON><PERSON><PERSON><PERSON>", "template": "<PERSON><PERSON><PERSON>", "titleAdd": "Lisää ajoneuvo", "titleEdit": "Muokkaa ajoneuvoa", "validateSave": "Vahvista ja tallenna"}}, "footer": {"community": {"greenEnergy": "Aurinkoenergiaa", "greenEnergySub1": "ladattu evcc:llä", "greenEnergySub2": "lokakuusta 2022 lähtien", "greenShare": "Aurinkoenergian osuus", "greenShareSub1": "on peräisin", "greenShareSub2": "aurinkoenergiasta ja akkuvarastoinnista", "power": "Latausteho", "powerSub1": "{activeClients} / {totalClients} osallistujaa", "powerSub2": "lataa…", "tabTitle": "Yhteisö"}, "savings": {"co2Saved": "{value} s<PERSON><PERSON><PERSON><PERSON>", "co2Title": "CO₂ Päästöt", "configurePriceCo2": "Opi kuinka voit muokata hinta- ja CO₂ arvoja.", "footerLong": "{percent} aurinkoenergiaa", "footerShort": "{percent} aurinkoenergiaa", "modalTitle": "Latausenergian yhteenveto", "moneySaved": "{value} s<PERSON><PERSON><PERSON><PERSON>", "percentGrid": "{grid} kWh verkosta", "percentSelf": "{self} kWh auringosta", "percentTitle": "Aurinkoenergia", "period": {"30d": "viimeiset 30 päivää", "365d": "viimeiset 365 päivää", "thisYear": "tämä vuosi", "total": "kaikki"}, "periodLabel": "Jakso:", "priceTitle": "Energian hinta", "referenceGrid": "sähköverkko", "referenceLabel": "Vertailutieto:", "tabTitle": "<PERSON><PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "Ryhdy sponsoriksi", "becomeSponsorExtended": "<PERSON>e meitä suoraan saadaksesi tarroja.", "confetti": "<PERSON><PERSON><PERSON> k<PERSON>?", "confettiPromise": "Saat tarroja ja myö<PERSON> digitaali konfetteja", "sticker": "… vai evcc tarroja?", "supportUs": "Missiomme on tehdä aurinkoenergialla lataamisesta normi. Auta evcc:tä maksamalla sen verran minkä arvoinen se on sinulle.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Panoksesi auttaa kehittämään evcc:tä my<PERSON> jat<PERSON>.", "titleNoSponsor": "<PERSON><PERSON> meit<PERSON>", "titleSponsor": "<PERSON><PERSON>", "titleTrial": "Kokeil<PERSON>la", "titleVictron": "Victron Energyn sponsoroima", "trial": "<PERSON><PERSON>, voit käyttää kaikkia ominaisuuksia. Ole hyvä ja harkitse projektin tukemista.", "victron": "Käytät evcc:tä Victron Energy -laitteistossa ja sinulla on pääsy kaikkiin ominaisuuksiin."}, "telemetry": {"optIn": "<PERSON><PERSON> j<PERSON>a <PERSON>.", "optInMoreDetails": "Lisätietoja {0}.", "optInMoreDetailsLink": "t<PERSON>ällä", "optInSponsorship": "S<PERSON>nsorointi vaaditaan."}, "version": {"availableLong": "uusi versio sa<PERSON>", "modalCancel": "Peruuta", "modalDownload": "Lataa", "modalInstalledVersion": "Nykyinen versio", "modalNoReleaseNotes": "Julkaisutietoja ei saatavilla. Lisätietoa uudesta versiosta:", "modalTitle": "Uusi versio sa<PERSON>", "modalUpdate": "<PERSON><PERSON><PERSON>", "modalUpdateNow": "<PERSON><PERSON><PERSON> nyt", "modalUpdateStarted": "Käynnistetään evcc:n uusin versio…", "modalUpdateStatusStart": "<PERSON><PERSON><PERSON> al<PERSON>:"}}, "forecast": {"co2": {"average": "Keskimäärin", "lowestHour": "<PERSON><PERSON><PERSON> tunti", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "modalTitle": "Ennus<PERSON>", "price": {"average": "Keskimääräinen", "lowestHour": "<PERSON><PERSON><PERSON><PERSON> tunti", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partly": "osi<PERSON>in", "remaining": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Tänää<PERSON>", "tomorrow": "<PERSON><PERSON><PERSON>"}, "solarAdjust": "<PERSON><PERSON>uta au<PERSON>ääennustettu todellisten tuotantotietojen perust<PERSON>{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}}, "header": {"about": "<PERSON><PERSON><PERSON>", "blog": "Blogi", "docs": "Dokumentaatio", "github": "GitHub", "login": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeSettings": "<PERSON><PERSON><PERSON><PERSON> palvelin", "needHelp": "Tarvitsetko apua?", "sessions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "help": {"discussionsButton": "GitHub keskustelut", "documentationButton": "Dokumentaatio", "issueButton": "<PERSON><PERSON><PERSON>", "issueDescription": "Löysitkö outoa tai vääränlaista käytöstä?", "logsButton": "Näytä logit", "logsDescription": "Tarkasta loki virheiden varalta.", "modalTitle": "Tarvitsetko apua?", "primaryActions": "Jokin ei toimi niin kuin sen pitäisi toimia? Nämä ovat hyviä paikkoja saada apua.", "restart": {"cancel": "Peruuta", "confirm": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>ist<PERSON> uudelleen!", "description": "Normaaleissa olosuhteissa uudelleenkäynnistyksen ei pitäisi olla välttämätöntä. Harkitse virheilmoituksen tekemistä, jos sinun on käynnistettävä evcc uudelleen säännöllisesti.", "disclaimer": "Huomaa: evcc lopettaa toimintansa ja odottaa, että käyttöjärjestelmä käynnistäisi sen uudelleen.", "modalTitle": "<PERSON><PERSON><PERSON> varma että haluat käynnistää uudelleen?"}, "restartButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "restartDescription": "Yrititk<PERSON> sammuttaa ja käynnistää uudelleen?", "secondaryActions": "Etkö vieläkään pysty ratkai<PERSON>maan ongelmaasi? Tässä on joitain raskaampia vaihtoehtoja."}, "log": {"areaLabel": "<PERSON><PERSON>", "areas": "<PERSON><PERSON><PERSON>", "download": "Lataa täydellinen logi", "levelLabel": "<PERSON>a logi tasolla", "nAreas": "{count} al<PERSON>tta", "noResults": "<PERSON>i vast<PERSON> lo<PERSON>kintöjä.", "search": "Etsi", "selectAll": "valitse kaikki", "showAll": "Näytä kaikki", "title": "<PERSON><PERSON>", "update": "Päivitä automaattisesti"}, "loginModal": {"cancel": "Peruuta", "demoMode": "Sisäänkirjautumista demo-tilassa ei tueta.", "error": "Kirjautuminen epäonnistui: ", "iframeHint": "Avaa evcc uudessa välilehdessä.", "iframeIssue": "Salasa<PERSON><PERSON> on o<PERSON>a, mutta selaimesi näyttää pudonneen todennusev<PERSON>. Näin voi käydä, jos suoritat evcc:n iframe-kehyksessä HTTP:n kautta.", "invalid": "Salasana ei kelpaa.", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "reset": "Määritä salasana uudelleen?", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"chargingPlan": {"active": "Aktiivinen", "addRepeatingPlan": "Lisää to<PERSON>uva suunnitelma", "arrivalTab": "<PERSON><PERSON><PERSON><PERSON>", "day": "Päivä", "departureTab": "Lähtö", "goal": "Lataus tavoite", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "ei mit<PERSON>än", "planNumber": "Suunnitelma {number}", "preconditionDescription": "Ladataan {duration} ennen lähtöä akunesilämmitystä varten.", "preconditionLong": "Viimehetken lataus", "preconditionOptionAll": "koko latausmäärä", "preconditionOptionNo": "ei", "preconditionShort": "<PERSON><PERSON>me<PERSON><PERSON>kellinen", "remove": "Poista", "repeating": "<PERSON><PERSON><PERSON><PERSON>", "repeatingPlans": "<PERSON><PERSON><PERSON><PERSON> suunni<PERSON>", "selectAll": "<PERSON><PERSON><PERSON> kaikki", "time": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleMinSoc": "<PERSON><PERSON>", "titleTargetCharge": "Lähtö", "unsavedChanges": "Tallentamattomia muutoksia. Asetetaanko nyt?", "update": "<PERSON><PERSON>", "weekdays": "Päivät"}, "energyflow": {"battery": "Akku", "batteryCharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batteryDischarge": "Akku purkautuu", "batteryGridChargeActive": "lataaminen verkkosta aktiivinen", "batteryGridChargeLimit": "milloin ladataan verkosta", "batteryHold": "Akku (lukittu)", "batteryTooltip": "{energy} / {total} ({soc})", "forecastTooltip": "ennuste: j<PERSON><PERSON><PERSON><PERSON><PERSON> oleva aurinkotuotanto tän<PERSON>n", "gridImport": "<PERSON><PERSON><PERSON>", "homePower": "<PERSON><PERSON>kö<PERSON>", "loadpoints": "Latauslaite| Latauslaite | {count} latauslaitetta", "noEnergy": "<PERSON>i mittarin tie<PERSON>ja", "pv": "Aurinkosähköjärjestelmä", "pvExport": "Sähköverkkoon vienti", "pvProduction": "<PERSON><PERSON><PERSON>", "selfConsumption": "<PERSON><PERSON><PERSON><PERSON> kulutus"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "connected": "Valmiustila.", "vehicleLimit": "Lämmittimen rajoitin", "waitForVehicle": "Valmiina. Odottaa lämmitintä…"}, "loadpoint": {"avgPrice": "⌀ Hinta", "charged": "Ladattu", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON>", "fallbackName": "La<PERSON><PERSON><PERSON><PERSON>", "finished": "Valmistumisaika", "power": "Teho", "price": "<PERSON><PERSON><PERSON>", "remaining": "J<PERSON>ljellä", "remoteDisabledHard": "{source}: sammui", "remoteDisabledSoft": "{source}: sammutti adaptiivisen aurinkolatauksen", "solar": "Aurinkoenergia"}, "loadpointSettings": {"batteryBoost": {"description": "<PERSON>a lataus kodin a<PERSON>.", "label": "<PERSON><PERSON>n te<PERSON>n", "mode": "Saatavilla vain aurinkosähkö(pv)- ja min + pv-tilassa.", "once": "<PERSON><PERSON><PERSON> on aktiivinen tälle latausistunnolle."}, "batteryUsage": "<PERSON><PERSON> a<PERSON>ku", "currents": "Lata<PERSON><PERSON><PERSON>", "default": "o<PERSON>us", "disclaimerHint": "Huomautus:", "limitSoc": {"description": "Latausraja mitä käytetään kun ajoneuvo yhdistetty.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "maxCurrent": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "minCurrent": {"label": "Minimivirta"}, "minSoc": {"description": "Ajoneuvo ladataan nopeasti {0} asti huolimatta aurinkoenergian ylijäämästä, tämän jälkeen vain ylijäämäenergialla. Tämä on hyödyllinen takaamaan minimi toimintamatka myös pimeinä päivinä.", "label": "<PERSON><PERSON>"}, "onlyForSocBasedCharging": "Nämä vaihtoehdot ovat käytettävissä vain ajoneuvoille, joid<PERSON> la<PERSON> on tied<PERSON>.", "phasesConfigured": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "no1p3pSupport": "<PERSON><PERSON><PERSON> la<PERSON> on kyt<PERSON><PERSON>?", "phases_0": "automaattinen", "phases_1": "1-v<PERSON><PERSON>", "phases_1_hint": "({min} - {max})", "phases_3": "3-v<PERSON><PERSON>", "phases_3_hint": "({min} - {max})"}, "smartCostCheap": "<PERSON><PERSON><PERSON> verkosta lataus", "smartCostClean": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verkosta la<PERSON>", "title": "Asetukset {0}", "vehicle": "<PERSON>joneu<PERSON>"}, "mode": {"minpv": "Minimi+PV", "now": "<PERSON>a", "off": "<PERSON><PERSON>", "pv": "PV", "smart": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "provider": {"login": "kir<PERSON><PERSON><PERSON>", "logout": "k<PERSON><PERSON><PERSON><PERSON> ulos"}, "startConfiguration": "<PERSON><PERSON><PERSON>", "targetCharge": {"activate": "Aktivoi", "co2Limit": "Raja CO₂ / {co2}", "costLimitIgnore": "Määritetty {limit} ohitetaan tänä aikana.", "currentPlan": "Aktiivinen suunnitelma", "descriptionEnergy": "<PERSON><PERSON> {targetEnergy} tulisi ladata autoon?", "descriptionSoc": "<PERSON><PERSON><PERSON> a<PERSON> tulee olla ladattu {targetSoc}?", "goalReached": "Tavoite jo saavutettu", "inactiveLabel": "Tavoiteaika", "nextPlan": "<PERSON><PERSON><PERSON> suun<PERSON>", "notReachableInTime": "Tavoite saavutetaan {overrun} myöhemmin.", "onlyInPvMode": "La<PERSON>ussu<PERSON><PERSON>tel<PERSON> toimii ain<PERSON>aan aurinkoenergiatilassa.", "planDuration": "<PERSON><PERSON><PERSON><PERSON>", "planPeriodLabel": "<PERSON><PERSON><PERSON>", "planPeriodValue": "{start} - {end}", "planUnknown": "ei viel<PERSON> tiedossa", "preview": "Esikatsele suunnitelmaa", "priceLimit": "{price} hintaraja", "remove": "Poista", "setTargetTime": "ei mit<PERSON>än", "targetIsAboveLimit": "Asetettu lata<PERSON>a {limit} ohitetaan tänä aikana.", "targetIsAboveVehicleLimit": "<PERSON><PERSON><PERSON><PERSON> on alle lataustavoitteen.", "targetIsInThePast": "Valitse aika tulevaisuudessa.", "targetIsTooFarInTheFuture": "Muok<PERSON><PERSON>me suunnitel<PERSON>a heti kun tiedämme enemmän tuleva<PERSON>udes<PERSON>.", "title": "Tavoiteaika", "today": "tä<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "huomenna", "update": "Päivittää", "vehicleCapacityDocs": "Opi kuinka voit muokata sitä.", "vehicleCapacityRequired": "<PERSON><PERSON><PERSON><PERSON> akun ka<PERSON> tarvitaan latausajan ar<PERSON> varten."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "co2Label": "CO₂ päästö ⌀", "priceLabel": "Energian hinta", "timeRange": "{day} {range} t", "unknownPrice": "ei viel<PERSON> tiedossa"}, "targetEnergy": {"label": "<PERSON>", "noLimit": "ei mit<PERSON>än"}, "vehicle": {"addVehicle": "Lisää ajoneuvo", "changeVehicle": "<PERSON>ai<PERSON><PERSON>eu<PERSON>", "detectionActive": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "fallbackName": "<PERSON>joneu<PERSON>", "moreActions": "Lisää toim<PERSON>", "none": "<PERSON><PERSON> ajoneuvo<PERSON>", "notReachable": "Ajoneuvo ei ollut saatavilla. Kokeile käynnistää evcc uudelleen.", "targetSoc": "<PERSON>", "temp": "Lämp<PERSON><PERSON><PERSON>.", "tempLimit": "Lämpötila raja-arvo", "unknown": "Vieras ajoneuvo", "vehicleSoc": "<PERSON><PERSON><PERSON>"}, "vehicleStatus": {"awaitingAuthorization": "Odotetaan valtuutusta.", "batteryBoost": "<PERSON><PERSON><PERSON> te<PERSON> a<PERSON>.", "charging": "Lataa…", "cheapEnergyCharging": "Edullista energiaa saatavilla.", "cheapEnergyNextStart": "Energia on edullista {duration} ajan.", "cheapEnergySet": "<PERSON><PERSON><PERSON><PERSON>.", "cleanEnergyCharging": "Puhdasta energiaa saatavilla.", "cleanEnergyNextStart": "<PERSON><PERSON><PERSON><PERSON> energiaa {duration} kuluessa.", "cleanEnergySet": "CO₂-raja asetettu.", "climating": "Esilämmitys/-viilennys havaittu.", "connected": "<PERSON><PERSON><PERSON><PERSON>.", "disconnectRequired": "Istunto lo<PERSON>ettu. Yhdistä uudelleen.", "disconnected": "Irroitettu.", "feedinPriorityNextStart": "<PERSON><PERSON><PERSON> tuotannon verk<PERSON>on myynti<PERSON>ta alkaa {duration}.", "feedinPriorityPausing": "Aurinkoenergia<PERSON><PERSON> ta<PERSON>, jotta s<PERSON><PERSON><PERSON> my<PERSON>ti verk<PERSON>on voidaan maksimoida.", "finished": "Valmis.", "minCharge": "Ladataan minimissään {soc}.", "pvDisable": "Ei tarpeeksi ylijäämää. Lataus keskeytetään pian.", "pvEnable": "Ylijäämää saatavilla. Lataus alkaa pian.", "scale1p": "Siirrytään pian 1-v<PERSON><PERSON> la<PERSON>uk<PERSON>n.", "scale3p": "Si<PERSON>rytään pian 3-v<PERSON><PERSON> la<PERSON>n.", "targetChargeActive": "Lataussuunnitelma aktiivinen. Arvioitu valmistuminen {duration} kuluttua.", "targetChargePlanned": "Lataussuunnitelma alkaa {duration} kuluttua.", "targetChargeWaitForVehicle": "Lataussuunnitelma valmis. Odotetaan ajoneuvoa…", "vehicleLimit": "<PERSON><PERSON><PERSON><PERSON>", "vehicleLimitReached": "<PERSON><PERSON><PERSON><PERSON> raja saavutettu.", "waitForVehicle": "Valmiina. Odotetaan ajoneuvoa…", "welcome": "Lyhyt aloitus lataus yht<PERSON> vahvistami<PERSON>ksi."}, "vehicles": "Pysäköinti", "welcome": "Hei kyytiin!"}, "notifications": {"dismissAll": "Hylkää kaikki", "logs": "Näytä täydellinen logi", "modalTitle": "Ilmoitukset"}, "offline": {"configurationError": "<PERSON><PERSON><PERSON> käynnistyksessä. Tarkista määritykset ja käynnistä uudelleen.", "message": "Ei yhtey<PERSON>ä palvelimeen.", "restart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "restartNeeded": "Tarvitaan muutosten k<PERSON>yttöönottamiseksi.", "restarting": "Palvelin tulee takaisin hetken kuluttua."}, "passwordModal": {"description": "Aseta salasana suojellaksesi määritys asetuksia. Pääikkunan käyttö on silti mahdollista ilman kirjautumista.", "empty": "Salasana ei tulisi olla tyhjä", "error": "Virhe: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON>", "labelNew": "<PERSON><PERSON><PERSON>", "labelRepeat": "<PERSON><PERSON> sa<PERSON>", "newPassword": "<PERSON><PERSON> sa<PERSON>", "noMatch": "Salasanat eivät täsmää", "titleNew": "Aseta Pääkäyttäjän salasana", "titleUpdate": "Päivitä Pääkäyttäjän salasana", "updatePassword": "Päivitä sa<PERSON>"}, "session": {"cancel": "Peruuta", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Poista", "finished": "Val<PERSON>", "meter": "<PERSON><PERSON><PERSON>", "meterstart": "<PERSON><PERSON><PERSON>", "meterstop": "<PERSON><PERSON><PERSON>", "odometer": "Ajokilometrit", "price": "<PERSON><PERSON>", "started": "Alkoi", "title": "Latausistunto"}, "sessions": {"avgPower": "⌀ <PERSON>ho", "avgPrice": "⌀ Hinta", "chargeDuration": "<PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Hinta {byGroup}", "byGroupLoadpoint": "lataus<PERSON><PERSON> mukaan", "byGroupVehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "energy": "Ladattu energia", "energyGrouped": "Aurinkoenergia vs. verkonenergia", "energyGroupedByGroup": "Energia {byGroup}", "energySubSolar": "{value} auringosta", "energySubTotal": "{value} yhteensä", "groupedCo2ByGroup": "CO₂-määrä {byGroup}", "groupedPriceByGroup": "Kokonaiskustannukset {byGroup}", "historyCo2": "CO₂-päästöt", "historyCo2Sub": "{value} yhteensä", "historyPrice": "Lata<PERSON>ust<PERSON>ukset", "historyPriceSub": "{value} yhteensä", "solar": "Aurinkoenergian osuus vuoden aikana", "solarByGroup": "Aurinkoenergian osuus {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energia (kWh)", "chargeduration": "<PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "<PERSON><PERSON><PERSON>", "finished": "Päättynyt", "identifier": "<PERSON><PERSON><PERSON>", "loadpoint": "La<PERSON><PERSON><PERSON><PERSON>", "meterstart": "<PERSON><PERSON><PERSON> (kWh)", "meterstop": "<PERSON><PERSON><PERSON> (kWh)", "odometer": "Ajokilometrit (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Hinta/kWh", "solarpercentage": "Aurinkoenergia (%)", "vehicle": "<PERSON>joneu<PERSON>"}, "csvPeriod": "Lataa {period} CSV", "csvTotal": "Lataa kaikki CSV", "date": "Al<PERSON><PERSON><PERSON>", "energy": "Ladattu", "filter": {"allLoadpoints": "kaikki <PERSON>", "allVehicles": "ka<PERSON>ki <PERSON>", "filter": "<PERSON><PERSON><PERSON>"}, "group": {"co2": "Päästö<PERSON>", "grid": "Sähköverkko", "price": "<PERSON><PERSON>", "self": "<PERSON><PERSON><PERSON>"}, "groupBy": {"loadpoint": "La<PERSON><PERSON><PERSON><PERSON>", "none": "Yhteensä", "vehicle": "<PERSON>joneu<PERSON>"}, "loadpoint": "La<PERSON><PERSON><PERSON><PERSON>", "noData": "Ei lataustapahtumia tässä kuussa.", "overview": "Yleiskatsaus", "period": {"month": "<PERSON><PERSON><PERSON><PERSON>", "total": "Yhteensä", "year": "<PERSON><PERSON><PERSON>"}, "price": "<PERSON><PERSON><PERSON>", "reallyDelete": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän istunnon?", "showIndividualEntries": "Näytä yksittäiset tapahtumat", "solar": "Aurinkoenergia", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Yhteensä", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON>"}, "vehicle": "<PERSON>joneu<PERSON>"}, "settings": {"fullscreen": {"enter": "<PERSON><PERSON> koko<PERSON>", "exit": "<PERSON><PERSON><PERSON> k<PERSON>", "label": "Kokonäyttö"}, "hiddenFeatures": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "Näytä käyttöliittymässä kokeelliset ominaisuudet."}, "language": {"auto": "Automaattinen", "label": "<PERSON><PERSON>"}, "sponsorToken": {"expires": "Sponso<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van<PERSON>ee {inXDays}. {getNewToken} ja päivitä se tänne.", "getNew": "<PERSON><PERSON>i", "hint": "Huomaa: automatisoimme tämän tuleva<PERSON>udessa."}, "telemetry": {"label": "Telemetria"}, "theme": {"auto": "o<PERSON>us", "dark": "tumma", "label": "<PERSON><PERSON><PERSON>", "light": "vaalea"}, "time": {"12h": "12 t", "24h": "24 t", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "title": "Käyttöliittymä", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mi": "mailit"}}, "smartCost": {"activeHours": "{active} / {total}", "activeHoursLabel": "Aktiiviset tunnit", "applyToAll": "<PERSON><PERSON><PERSON><PERSON> kaik<PERSON>a?", "batteryDescription": "Lataa kodin akun sähköverkosta.", "cheapTitle": "Lataaminen edullisesti sähköverkosta", "cleanTitle": "Viheränenergian lataminen verkosta", "co2Label": "CO₂ päästö", "co2Limit": "CO₂ raja", "loadpointDescription": "Mahdollistaa nopean lataamisen väliaikaisesti aurinkosähkötilassa (PV).", "modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> lataus verkosta", "none": "ei mit<PERSON>än", "priceLabel": "Energian hinta", "priceLimit": "<PERSON><PERSON><PERSON><PERSON>", "resetAction": "<PERSON><PERSON> r<PERSON>", "resetWarning": "Dynaamista sähköverkonhintaa tai sähkötuotannon CO₂-päästöjä ei ole mää<PERSON>, vaikka rajoit<PERSON> {limit} on asetettu. Siistitäänkö asetuksiasi?", "saved": "Tallennettu."}, "smartFeedInPriority": {"activeHoursLabel": "Tauotettu tuntia", "description": "<PERSON><PERSON><PERSON> ta<PERSON>an korkean sähkön hinnan a<PERSON>, jotta voidaan priorisoida sähkön myyntiä verkkoon.", "priceLabel": "<PERSON>erk<PERSON><PERSON>", "priceLimit": "Verkkoon syöttö rajoite", "resetWarning": "Dynaamista verkkoon myyntihintaa ei ole määritetty. <PERSON><PERSON><PERSON><PERSON> on määritetty raja {limit}. Siivotaanko määrityksiäsi?", "title": "<PERSON><PERSON>k<PERSON><PERSON> myynn<PERSON>i"}, "startupError": {"configFile": "<PERSON><PERSON><PERSON><PERSON> määritystiedostoa:", "configuration": "Määritys", "description": "Tarkasta asetustiedosto. Jos v<PERSON> ei ole apua, katso {0}.", "discussions": "GitHub keskustelut", "fixAndRestart": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>ma ja k<PERSON>ynnistä serveri uudelleen.", "hint": "Huomaa: Se saattaa olla sinun viallinen la<PERSON>esi (invertteri, mittari, …). Tarkista myös internetyhteytesi.", "lineError": "<PERSON><PERSON><PERSON> {0}.", "lineErrorLink": "rivi {0}", "restartButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}}