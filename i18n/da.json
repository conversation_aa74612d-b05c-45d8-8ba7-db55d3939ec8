{"batterySettings": {"batteryLevel": "Batteri-niveau", "bufferStart": {"above": "n<PERSON><PERSON> den er over {soc}.", "full": "n<PERSON>r den er på {soc}.", "never": "kun med nok overskud."}, "capacity": "{energy} af {total}", "control": "<PERSON><PERSON><PERSON>", "discharge": "Forhindrer afladning ved planlagt opladning og i hurtig mode.", "disclaimerHint": "OBS:", "disclaimerText": "Disse indstillinger gælder kun for solar modus. Indstillingerne ændres tilsvarende.", "gridChargeTab": "Opladning fra el<PERSON>et", "legendBottomName": "Prioritér opladning af husbatteriet", "legendBottomSubline": "Indtil der opnås {soc}.", "legendMiddleName": "Prioriter opladning af køretøj", "legendMiddleSubline": "<PERSON><PERSON><PERSON> hus<PERSON>teriet er over {soc}.", "legendTopAutostart": "Starter automatisk", "legendTopName": "Batteri-understøttet opladning af køretøj", "legendTopSubline": "n<PERSON><PERSON> husbatteriet er over {soc}.", "modalTitle": "<PERSON><PERSON>i", "usageTab": "<PERSON><PERSON><PERSON> an<PERSON>"}, "config": {"aux": {"description": "<PERSON><PERSON>, der justerer sit forbrug baseret på tilgængeligt overskud (som smarte vandvarmere). evcc forventer, at denne enhed reducerer sit strømforb<PERSON>, hvis det er nødvendigt.", "titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> selvregulerende forbrugsenhed", "titleEdit": "<PERSON><PERSON> selvregu<PERSON><PERSON>e forbrug<PERSON>"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>i", "titleEdit": "<PERSON><PERSON> batteri"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>lad<PERSON>å<PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>"}, "charger": {"chargers": "EV opladere", "generic": "Generisk integration", "heatingdevices": "Varm<PERSON>hed<PERSON>", "ocppHelp": "<PERSON><PERSON><PERSON> denne adresse til opladerens konfiguration.", "ocppLabel": "OCPP-Server URL", "switchsockets": "Omskiftelige stikkontakter", "template": "<PERSON>abrikant", "titleAdd": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "<PERSON><PERSON><PERSON>", "heating": "<PERSON>iger var<PERSON>met"}, "type": {"custom": {"charging": "Brugerdefineret oplader", "heating": "Brugerdefineret varmelegeme"}, "heatpump": "Brugerdefineret varmepumpe", "sgready": "Brugerdefineret varmepumpe (sg-klar, alle)", "sgready-boost": "Brugerdefineret varmepumpe (sg-klar, boost)", "switchsocket": "Brugerdefineret stikkontakt"}}, "circuits": {"description": "<PERSON><PERSON><PERSON>, at summen af alle ladepunkter forbundet til et kredsløb ikke overstiger de konfigurerede effekt- og strømgrænser. Kredsløb kan indlejres for at opbygge et hierarki.", "title": "Belastningsstyring"}, "control": {"description": "Standardværdierne er normalt fine. Kun hvis du ved, hvad du gør, kan du ændre dem.", "descriptionInterval": "Opdateringscyklus i sekunder. Definerer, hvor ofte evcc læser målerdata, justerer ladeeffekt og opdaterer brugergrænsefladen. Korte intervaller (< 30s) kan forårsage store udsving og fejlagtigt resultat.", "descriptionResidualPower": "Ski<PERSON> driftspunktet for kontrolsløjfen. Hvis du har et hjemmebatteri, anbe<PERSON><PERSON> det at indstille en værdi på 100 W. <PERSON><PERSON> denne måde vil batteriet få en lille prioritet frem for netbrug.", "labelInterval": "Opdateringsinterval", "labelResidualPower": "Tilbageværende effekt", "title": "kontrol adfærd"}, "deviceValue": {"amount": "<PERSON><PERSON><PERSON><PERSON>", "broker": "Broker", "bucket": "Bucket", "capacity": "Kapacitet", "chargeStatus": "Status", "chargeStatusA": "ikke tilslut<PERSON>t", "chargeStatusB": "tilsluttet", "chargeStatusC": "oplader", "chargeStatusE": "ingen strøm", "chargeStatusF": "fejl", "chargedEnergy": "<PERSON><PERSON><PERSON>", "co2": "Elnettet CO₂", "configured": "Konfigureret", "controllable": "Kan styres", "currency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current": "Ström", "currentRange": "Strømstyrke", "enabled": "Aktiveret", "energy": "Energi", "feedinPrice": "Indfødningstarif", "gridPrice": "<PERSON> pris", "heaterTempLimit": "Opvarmning begrænsning", "hemsType": "System", "identifier": "RFID-identifikator", "no": "nej", "odometer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "org": "Organisation", "phaseCurrents": "Strømstyrke L1, L2, L3", "phasePowers": "Effekt L1, L2, L3", "phaseVoltages": "Spænding L1, L2, L3", "phases1p3p": "Fase-skift", "power": "Effekt", "powerRange": "Effekt", "range": "Rækkevidde", "singlePhase": "<PERSON>kel<PERSON> fase", "soc": "<PERSON><PERSON><PERSON>", "solarForecast": "Sol prognose", "temp": "Temperatur", "topic": "<PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "K<PERSON>retø<PERSON><PERSON> begræ<PERSON>", "yes": "ja"}, "deviceValueChargeStatus": {"A": "A (ikke forbundet)", "B": "B (forbundet)", "C": "C (oplader)"}, "devices": {"auxMeter": "Smart forbrugsenhed", "batteryStorage": "Batterilagring", "solarSystem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "editor": {"loading": "Indlæser YAML-editor…"}, "eebus": {"description": "Indstilling der tillader evcc at kommunikere med andre EEBus enheder.", "title": "EEBus"}, "ext": {"description": "Kan bruges til belastningsstyring eller statistikformål.", "titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON> måler", "titleEdit": "Rediger ekstern måler"}, "form": {"danger": "<PERSON><PERSON>", "deprecated": "<PERSON><PERSON><PERSON><PERSON>", "example": "Eksempel", "optional": "valgfrit"}, "general": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customHelp": "Opret en brugerdefineret enhed ved hjælp af evcc's plugin-system.", "customOption": "Brugerdefineret enhed", "delete": "Slet", "docsLink": "Se dokumentation.", "experimental": "Eksperimentel", "hideAdvancedSettings": "Sk<PERSON><PERSON> avance<PERSON>e in<PERSON>", "invalidFileSelected": "Ugyldig fil er valgt", "noFileSelected": "Ingen fil valgt.", "off": "slukket", "on": "tæ<PERSON><PERSON>", "password": "Adgangskode", "readFromFile": "<PERSON><PERSON><PERSON> fra fil", "remove": "<PERSON><PERSON><PERSON>", "save": "Gem", "selectFile": "Gennemse", "showAdvancedSettings": "Vis avance<PERSON>e in<PERSON>", "telemetry": "Telemetri", "templateLoading": "Indlæser...", "title": "Titel", "validateSave": "Valider & gem"}, "grid": {"title": "El<PERSON>-m<PERSON><PERSON>", "titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for elnettet", "titleEdit": "<PERSON><PERSON> måler for elnettet"}, "hems": {"description": "Tilslut evcc til et andet energistyringssystem i hjemmet.", "title": "HEMS"}, "icon": {"change": "æ<PERSON><PERSON>"}, "influx": {"description": "Skriver opladningsdata og andre målinger til InfluxDB. Brug Grafana eller andre værktøjer til at visualisere data.", "descriptionToken": "Tjek InfluxDB-dokumentation for at lære, hvordan du opretter en. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "<PERSON>ad selvsignerede certifikater", "labelDatabase": "Database", "labelInsecure": "Certifikatvalidering", "labelOrg": "Organisation", "labelPassword": "Adgangskode", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "Brugernavn", "title": "InfluxDB", "v1Support": "Har du brug for support til InfluxDB 1.x?", "v2Support": "Tilbage til InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>di<PERSON> energimåler", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargerError": {"charging": "Konfiguration af en oplader er påkrævet.", "heating": "Varmelegemet skal konfigureres."}, "chargerLabel": {"charging": "<PERSON><PERSON><PERSON>", "heating": "Varmelegeme"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Benytter en strømstyrke mellem 6 og 16 A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Benytter en strømstyrke mellem 6 og 32 A.", "chargerPowerCustom": "andre", "chargerPowerCustomHelp": "Definer strømstyrke interval.", "chargerTypeLabel": "Oplader type", "chargingTitle": "Tilstand", "circuitHelp": "Belastningsstyring som sikrer, at effekt- og strømgrænser ikke overskrides.", "circuitLabel": "K<PERSON><PERSON>lø<PERSON>", "circuitUnassigned": "ikke tildelt", "defaultModeHelp": {"charging": "Opladningstilstand når køretøjet forbindes.", "heating": "Indstilles ved systemstart."}, "defaultModeHelpKeep": "<PERSON><PERSON><PERSON> den senest valgte tilstand.", "defaultModeLabel": "Standardtilstand", "delete": "Slet", "electricalSubtitle": "<PERSON><PERSON> t<PERSON>vl, spørg din elektriker.", "electricalTitle": "Elektrisk", "energyMeterHelp": "<PERSON><PERSON><PERSON>, hvis opladeren ikke har en integreret.", "energyMeterLabel": "<PERSON><PERSON><PERSON>", "estimateLabel": "Interpoler opladningsniveauet mellem API-opdateringer", "maxCurrentHelp": "Skal være større end minimum strømstyrken.", "maxCurrentLabel": "<PERSON><PERSON><PERSON><PERSON> strømstyrke", "minCurrentHelp": "Brug kun lavere end 6 A, hvis du er sikker på hvad du gør.", "minCurrentLabel": "Minimum strømstyrke", "noVehicles": "Ingen køretøjer er konfigureret.", "option": {"charging": "\"<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "phases1p": "1-fase", "phases3p": "3-f<PERSON>r", "phasesAutomatic": "Automatisk fase-valg", "phasesAutomaticHelp": "<PERSON> oplader understøtter automatisk skift mellem 1- og 3-faset opladning. I hovedmenuen kan du justere faseadfærd under opladning.", "phasesHelp": "Antal forbundne faser.", "phasesLabel": "<PERSON><PERSON><PERSON>", "pollIntervalDanger": "Regelmæssig forespørgsel på køretøjet kan dræne køretøjets batteri. Nogle køretøjsproducenter kan aktivt forhindre opladning i dette tilfælde. Anbefales ikke! Brug kun dette, hvis du er opmærksom på risiciene.", "pollIntervalHelp": "Tid mellem køretøjets API-opdateringer. Korte intervaller kan dræne køretøjets batteri.", "pollIntervalLabel": "Opdateringsinterval", "pollModeAlways": "altid", "pollModeAlwaysHelp": "Hent status opdateringer med jævne mellemrum.", "pollModeCharging": "<PERSON><PERSON><PERSON>", "pollModeChargingHelp": "Hent kun køretøjets status når der lades.", "pollModeConnected": "forbundet", "pollModeConnectedHelp": "Opdater køretøjets status med jævne mellemrum, når det er forbundet.", "pollModeLabel": "opdaterings adfærd", "priorityHelp": "Højere prioriterede får fortrin til solenergioverskud.", "priorityLabel": "Prioritet", "save": "Gem", "showAllSettings": "Vis alle indstillinger", "solarBehaviorCustomHelp": "Definer dine egne aktiverings- og deaktiveringstærskler samt forsinkelser.", "solarBehaviorDefaultHelp": "Start efter {enableDelay} af tilstrækkeligt overskud. Stop, når der ikke er nok overskud i {disableDelay}.", "solarBehaviorLabel": "Solar", "solarModeCustom": "egen", "solarModeMaximum": "max solenergi", "thresholdDisableDelayLabel": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "thresholdDisableHelpInvalid": "Brug en positiv værdi.", "thresholdDisableHelpPositive": "Stop, n<PERSON>r der bruges mere end {power} fra elnettet i {delay}.", "thresholdDisableHelpZero": "Stop, når den minimale effekt ikke kan opfyldes i {delay}.", "thresholdDisableLabel": "<PERSON><PERSON><PERSON><PERSON> strøm fra el<PERSON>et", "thresholdEnableDelayLabel": "Aktiver forsinkelse", "thresholdEnableHelpInvalid": "Brug en negativ værdi.", "thresholdEnableHelpNegative": "Start når {surplus} overskud er tilgængelig i {delay}.", "thresholdEnableHelpZero": "Start når den minimale nødvendige effekt kan opfyldes i {delay}.", "thresholdEnableLabel": "Brug elnettet", "titleAdd": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> lader eller var<PERSON>me"}, "titleEdit": {"charging": "<PERSON><PERSON> lad<PERSON>", "heating": "<PERSON><PERSON> var<PERSON>", "unknown": "Rediger lader eller varmelegeme"}, "titleExample": {"charging": "Garage, Carport osv.", "heating": "<PERSON><PERSON><PERSON><PERSON><PERSON>, varmelegeme osv."}, "titleLabel": "Titel", "vehicleAutoDetection": "Automatisk registrering", "vehicleHelpAutoDetection": "Vælg automatisk det mest sandsynlige køretøj. Det er muligt selv at vælge.", "vehicleHelpDefault": "<PERSON><PERSON>, at dette køretøj oplader her. Automatisk registrering deaktiveret. <PERSON> tilsidesættelse er mulig.", "vehicleLabel": "Standard køretøj", "vehiclesTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "main": {"addAdditional": "Tilføj en ekstra måler", "addGrid": "<PERSON><PERSON><PERSON><PERSON><PERSON> måler til elnet", "addLoadpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> lader eller var<PERSON>me", "addPvBattery": "<PERSON><PERSON><PERSON><PERSON><PERSON> sol<PERSON> eller <PERSON>i", "addTariffs": "Til<PERSON><PERSON>j tariffer", "addVehicle": "Tilføj køretøj", "configured": "Konfigureret", "edit": "rediger", "loadpointRequired": "Der skal konfigureres mindst ét ladepunkt.", "name": "Navn", "title": "Opsætning", "unconfigured": "ikke konfigureret", "vehicles": "Mine køretøjer", "yaml": "<PERSON><PERSON>er fra evcc.yaml kan ikke ændres her."}, "messaging": {"description": "Modtag beskeder om dine opladningssessioner.", "title": "Beskeder"}, "meter": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Slet", "generic": "Generiske integrationer", "option": {"aux": "<PERSON><PERSON><PERSON><PERSON><PERSON> selvregulerende forbrugsenhed", "battery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ext": "Tilføj en ekstra måler", "pv": "Tilføj en solenergimåler"}, "save": "Gem", "specific": "Specifikke integrationer", "template": "<PERSON>abrikant", "titleChoice": "<PERSON><PERSON>d ø<PERSON>ker du at tilføje?", "validateSave": "Valider og gem"}, "modbus": {"baudrate": "Baudrate", "comset": "ComSet", "connection": "Modbus forbindelse", "connectionHintSerial": "Enheden er direkte forbundet til evcc via et RS485-interface.", "connectionHintTcpip": "Enheden kan adresseres fra evcc via LAN/Wifi.", "connectionValueSerial": "Serial / USB", "connectionValueTcpip": "Netværk", "device": "Enhedsnavn", "deviceHint": "Eksempel: /dev/ttyUSB0", "host": "IP-adresse eller værtsnavn", "hostHint": "Eksempel: *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus protokol", "protocolHintRtu": "Forbindelse gennem en RS485 til Ethernet-adapter uden protokoloversættelse.", "protocolHintTcp": "Enheden har indbygget LAN/Wifi-understøttelse eller er forbundet via en RS485 til Ethernet-adapter med protokoloversættelse.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "<PERSON>ad flere klienter at få adgang til en enkelt Modbus-enhed.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Godkendelse", "description": "<PERSON>ret forbindelse til en MQTT-broker for at udveksle data med andre systemer på dit netværk.", "descriptionClientId": "Afsender af meddelelserne. Hvis tom bruges `evcc-[rand]`.", "descriptionTopic": "<PERSON><PERSON><PERSON> tom for at deaktivere udgivelse.", "labelBroker": "Broker", "labelCaCert": "Server certifikat (CA)", "labelCheckInsecure": "<PERSON>ad selvsignerede certifikater", "labelClientCert": "Klient certifikat", "labelClientId": "Klient ID", "labelClientKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelInsecure": "Certifikatvalidering", "labelPassword": "Adgangskode", "labelTopic": "<PERSON><PERSON>", "labelUser": "Brugernavn", "publishing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "MQTT"}, "network": {"descriptionHost": "Brug .local suffiks for at aktivere mDNS. Relevant for opdagelse af mobilappen og nogle OCPP-opladere.", "descriptionPort": "Port til webgrænsefladen og API. Du skal opdatere din browser-URL, hvis du ændrer dette.", "descriptionSchema": "<PERSON><PERSON><PERSON><PERSON> kun, hvordan URL'er genereres. Valg af HTTPS vil ikke aktivere kryptering.", "labelHost": "Værtsnavn", "labelPort": "port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "Netværk"}, "options": {"boolean": {"no": "nej", "yes": "ja"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "Opvar<PERSON>ning", "standby": "Standby"}, "schema": {"http": "HTTP (ikke-krypteret)", "https": "HTTPS (krypteret)"}, "status": {"A": "A (ikke forbundet)", "B": "B (forbundet)", "C": "C (lader)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON>"}, "section": {"additionalMeter": "Tilføj ekstra målere", "general": "<PERSON><PERSON><PERSON>", "grid": "<PERSON><PERSON><PERSON>", "integrations": "<PERSON><PERSON>", "loadpoints": "Lader og varmelegemer", "meter": "Sol og batteri", "system": "System", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "Indtast token", "changeToken": "Udskift token", "description": "Sponsormodellen hjælper os med at vedligeholde projektet og bæredygtigt bygge nye og spændende funktioner. Som sponsor får du adgang til alle opladning mulighederne.", "descriptionToken": "<PERSON> får et token fra {url}. Vi tilbyder også et prøvetoken til test.", "error": "Dit Sponsortoken er ikke gyldigt.", "labelToken": "Sponsor token", "title": "<PERSON><PERSON><PERSON><PERSON>", "tokenRequired": "Du skal konfigurere et sponsortoken, før du kan oprette denne enhed.", "tokenRequiredLearnMore": "<PERSON><PERSON><PERSON> mere.", "tokenRequiredShort": "Der er ingen sponsor token konfiguret.", "trialToken": "Test token"}, "system": {"backupRestore": {"backup": {"action": "Download sikkerhedskopi...", "confirmationButton": "Download sikkerhedskopi", "confirmationText": "Indtast venligst din adgangskode for at downloade databasefilen.", "description": "Sikkerhedskopier dine data til en fil. Den kan bruges til at gendanne dine data i tilfælde af systemfejl.", "title": "Sikkerhedskopier"}, "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmWithPassword": "Bekræft handling", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gendan og nulstil dine data. <PERSON><PERSON><PERSON>g, hvis du vil flytte dine data til et andet system.", "note": "Bemærk: Alle ovenstående handlinger påvirker kun dine databasedata. Konfigurationsfilen evcc.yaml forbliver uændret.", "reset": {"action": "Nulstil...", "confirmationButton": "Nulstil & genstart", "confirmationText": "<PERSON><PERSON> sletter dine valgte data. <PERSON><PERSON><PERSON> for, at du har downloadet en sikkerhedskopi.", "description": "Har du problemer med konfigurationen og vil starte forfra? Slet alle data og start forfra.", "sessions": "Opladningssessioner", "sessionsDescription": "Sletter din opladningshistorik.", "settings": "Konfiguration og indstillinger", "settingsDescription": "Sletter alle konfigurerede enheder, tjenester og opladsningsplaner mm.", "title": "Nulstil"}, "restore": {"action": "Gendan...", "confirmationButton": "Gendan & genstart", "confirmationText": "Det<PERSON> vil overskrive hele din database. <PERSON><PERSON><PERSON> for at du først har downloadet en sikkerhedskopi.", "description": "Gendan dine data fra en sikkerhedskopi. Dette vil overskrive alle dine nuværende data.", "labelFile": "Sikkerhedskopi fil", "title": "Gendan"}, "title": "Sikkerhedskopiering og gendannelse"}, "logs": "Log", "restart": "Genstart", "restartRequiredDescription": "Genstart for at se effekten.", "restartRequiredMessage": "Konfiguration er ændret.", "restartingDescription": "Vent venligst…", "restartingMessage": "Genstarter evcc."}, "tariffs": {"description": "Definer dine ener<PERSON><PERSON><PERSON> for at beregne omkostningerne ved dine opladningssessioner.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Vises på hovedskærm og på browserfane.", "label": "Titel", "title": "<PERSON>iger titel"}, "validation": {"failed": "mislyk<PERSON>es", "label": "Status", "running": "validering…", "success": "succes", "unknown": "ukendt", "validate": "validerer"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "chargingSettings": "Opladnings indstillinger", "defaultMode": "Standardtilstand", "defaultModeHelp": "Opladnings tilstand når køretøj forbindes.", "delete": "Slet", "generic": "<PERSON>", "identifiers": "RFID identifikatorer", "identifiersHelp": "Liste over RFID-strenge til identifikation af køretøjet. Én post pr. linje. Den aktuelle identifikator findes på den respektive ladestation på oversigtssiden.", "maximumCurrent": "<PERSON><PERSON><PERSON><PERSON> strømstyrke", "maximumCurrentHelp": "Skal være højere end minimal strømstyrke.", "maximumPhases": "<PERSON><PERSON><PERSON><PERSON> antal faser", "maximumPhasesHelp": "Hvor mange faser kan dette køretøj oplades med? Bruges til at beregne det krævede minimale soloverskud og planens varighed.", "minimumCurrent": "<PERSON><PERSON> strømstyrke", "minimumCurrentHelp": "<PERSON><PERSON> kun under 6A, hvis du ved, hvad du har med at gøre.", "online": "Køretøj med online API", "primary": "Generiske integrationer", "priority": "Prioritet", "priorityHelp": "Højere prioritet betyder, at dette køretøj får fortrin til soloverskud.", "save": "Gem", "scooter": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>ooter", "template": "<PERSON>abrikant", "titleAdd": "Tilføj køretøj", "titleEdit": "<PERSON><PERSON> kø<PERSON>", "validateSave": "Valider og gem"}}, "footer": {"community": {"greenEnergy": "Solenergi", "greenEnergySub1": "oplader med evcc", "greenEnergySub2": "siden oktober 2022", "greenShare": "<PERSON><PERSON><PERSON>", "greenShareSub1": "strøm leveret af", "greenShareSub2": "sol, og batteriopbevaring", "power": "Opladningsstrøm", "powerSub1": "{activeClients} af {totalClients} deltagere", "powerSub2": "oplader…", "tabTitle": "Live fællesskab"}, "savings": {"co2Saved": "{value} gemt", "co2Title": "CO₂ Udledning", "configurePriceCo2": "<PERSON><PERSON><PERSON> h<PERSON>dan man indstiller pris og CO₂ data.", "footerLong": "{percent} Solenergi", "footerShort": "{percent} Solenergi", "modalTitle": "Oversigt over lad<PERSON><PERSON><PERSON>", "moneySaved": "{value} gemt", "percentGrid": "{grid} kWh elnettet", "percentSelf": "{self} kWh solenergi", "percentTitle": "Solenergi", "period": {"30d": "seneste 30 dage", "365d": "seneste 365 dage", "thisYear": "<PERSON><PERSON>", "total": "hele tiden"}, "periodLabel": "Periode:", "priceTitle": "Energi<PERSON><PERSON>", "referenceGrid": "<PERSON><PERSON>", "referenceLabel": "Referencedata:", "tabTitle": "Mine data"}, "sponsor": {"becomeSponsor": "Bliv sponsor", "becomeSponsorExtended": "<PERSON><PERSON><PERSON> os direkte for at få klistermærker.", "confetti": "Klar til konfetti?", "confettiPromise": "Du får klistermærker og digital konfetti", "sticker": "... eller evcc-klistermærker?", "supportUs": "Vores mission er at gøre solopladning til normen. Hjælp evcc ved at betale, hvad det er værd for dig.", "thanks": "<PERSON><PERSON>, {sponsor}! Dit bidrag hjælper med at udvikle evcc yderligere.", "titleNoSponsor": "<PERSON><PERSON><PERSON> os", "titleSponsor": "Du er en støtter", "titleTrial": "Afprøvnings tilstand", "titleVictron": "Sponsoreret af Victron Energy", "trial": "Du er i afprøvningstilstand og kan bruge alle funktioner. Overvej venligst at støtte projektet.", "victron": "Du bruger evcc på Victron Energy udstyr og har adgang til alle funktioner."}, "telemetry": {"optIn": "Jeg vil gerne bidrage med mine data.", "optInMoreDetails": "<PERSON><PERSON><PERSON> {0}.", "optInMoreDetailsLink": "her", "optInSponsorship": "Sponsorering påkrævet."}, "version": {"availableLong": "ny version tilgængelig", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "He<PERSON>", "modalInstalledVersion": "Installeret version", "modalNoReleaseNotes": "Der er ingen tilgængelige release notes. Mere info om den nye version:", "modalTitle": "Ny version tilgængelig", "modalUpdate": "Installere", "modalUpdateNow": "Installer nu", "modalUpdateStarted": "Starter den nye version af evcc…", "modalUpdateStatusStart": "Installation startede:"}}, "forecast": {"co2": {"average": "Gennemsnit", "lowestHour": "Reneste time", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "modalTitle": "Prognose", "price": {"average": "Gennemsnit", "lowestHour": "Billigste time", "range": "S<PERSON><PERSON>nd<PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "I overmorgen", "partly": "delvis", "remaining": "resterende", "today": "I dag", "tomorrow": "I morgen"}, "solarAdjust": "<PERSON><PERSON> solprognosen på baggrund af produktions data{percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Sol"}}, "header": {"about": "Om", "authProviders": {"confirmLogout": "Er du sikker på du vil fjerne forbindelsen til {title}?", "title": "Autorisation status"}, "blog": "Blog", "docs": "Dokumentation", "github": "GitHub", "login": "Login til køretøjer", "logout": "Log ud", "nativeSettings": "Skift server", "needHelp": "Brug for hjælp?", "sessions": "Op<PERSON><PERSON><PERSON> sessioner"}, "help": {"discussionsButton": "GitHub diskussioner", "documentationButton": "Dokumentation", "issueButton": "Meld en fejl", "issueDescription": "Fundet en mærkelig eller forkert adfærd?", "logsButton": "Se logfil", "logsDescription": "Check log for fejl.", "modalTitle": "Brug for hjælp?", "primaryActions": "Noget fungerer ikke, som det skulle? Disse er gode steder at få hjælp.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "Ja, genstart!", "description": "Under normale omstændigheder bør genstart ikke være nødvendig. Overvej at indsende en fejlmelding, hvis du har brug for at genstarte evcc regelmæssigt.", "disclaimer": "Bemærk: evcc afsluttes og har brug for at operativsystemet genstarter tjenesten.", "modalTitle": "Er du sikker på, at du vil genstarte?"}, "restartButton": "Genstart", "restartDescription": "Har du prøvet at slukke og tænde den igen?", "secondaryActions": "<PERSON><PERSON>g ikke i stand til at løse dit problem? Her er nogle mere ekstreme muligheder."}, "log": {"areaLabel": "Filtrer per område", "areas": "Alle områder", "download": "Hent hele log filen", "levelLabel": "Filtrer på log niveau", "nAreas": "{count} o<PERSON><PERSON><PERSON><PERSON>", "noResults": "Ingen matchende logposter.", "search": "<PERSON><PERSON><PERSON>", "selectAll": "vælg alt", "showAll": "Vis alle poster", "title": "Log filer", "update": "Opdater automatisk"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "demoMode": "Login er ikke mulig i demo mode.", "error": "<PERSON><PERSON> mislykkedes: ", "iframeHint": "Åbn evcc i en ny fane.", "iframeIssue": "<PERSON>t kodeord er korrekt, men din browser ser ud til at have droppet godkendelses-cookien. <PERSON><PERSON> kan s<PERSON>, hvis du kører evcc i en iframe via HTTP.", "invalid": "Ugyldig adgangskode.", "login": "<PERSON>g på", "password": "Adgangskode", "reset": "Nulstil adgangskode?", "title": "Godkendelse"}, "main": {"chargingPlan": {"active": "Aktiv", "addRepeatingPlan": "Tilføj plan for gentagelse", "arrivalTab": "Ankomst", "day": "<PERSON><PERSON>", "departureTab": "Afgang", "goal": "Opladnings mål", "modalTitle": "Lade plan", "none": "ingen", "planNumber": "Plan {number}", "preconditionDescription": "Oplad {duration} før afgang til batterikonditionering.", "preconditionLong": "<PERSON> opladning", "preconditionOptionAll": "alt", "preconditionOptionNo": "nej", "preconditionShort": "<PERSON>", "remove": "<PERSON><PERSON><PERSON>", "repeating": "Gentager", "repeatingPlans": "Gentager plan", "selectAll": "Væ<PERSON>g alt", "time": "Tid", "title": "Plan", "titleMinSoc": "Min. ladning", "titleTargetCharge": "Afgang", "unsavedChanges": "Der er ændringer som ikke er gemt. Skal de benyttes nu?", "update": "<PERSON><PERSON><PERSON>", "weekdays": "<PERSON><PERSON>"}, "energyflow": {"battery": "<PERSON><PERSON><PERSON>", "batteryCharge": "Batteriet oplades", "batteryDischarge": "Batteriet aflades", "batteryGridChargeActive": "<PERSON><PERSON><PERSON> fra <PERSON>", "batteryGridChargeLimit": "<PERSON><PERSON><PERSON> fra el<PERSON> n<PERSON>", "batteryHold": "<PERSON><PERSON><PERSON> (låst)", "batteryTooltip": "{energi} af {total} ({soc})", "forecastTooltip": "prognose: resterende solenergi produktion i dag", "gridImport": "Forbrug fra elnet", "homePower": "Forbrug", "loadpoints": "Oplader| Oplader | {count} opladere", "noEnergy": "Ingen målerdata", "pv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvExport": "Eksport til elnet", "pvProduction": "Produktion", "selfConsumption": "<PERSON><PERSON> forbrug"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON>…", "connected": "Standby.", "vehicleLimit": "Opvarmning begrænsning", "waitForVehicle": "<PERSON><PERSON>. Venter på varmelegeme…"}, "loadpoint": {"avgPrice": "⌀ pris", "charged": "<PERSON><PERSON><PERSON>", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON><PERSON><PERSON>", "fallbackName": "Ladepunkt", "finished": "Sluttidspunkt", "power": "Effekt", "price": "<PERSON><PERSON>", "remaining": "Resterende tid", "remoteDisabledHard": "{source}: slukket", "remoteDisabledSoft": "{source}: Adaptiv sol-opladning er slukket", "solar": "solenergi"}, "loadpointSettings": {"batteryBoost": {"description": "Hurtig opladning fra husbatteriet.", "label": "Batteri boost", "mode": "Kun tilgængelig i solar og min+solar mode.", "once": "Boost er aktiveret for denne opladning."}, "batteryUsage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currents": "Ladestrøm", "default": "standard", "disclaimerHint": "Bemærk:", "limitSoc": {"description": "Opladningsgrænse som benyttes når dette køretøj er forbundet.", "label": "standard opladningsgrænse"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "Køretøjet bliver „hurtigt” opladet til {0} i solar mode, og fortsætter derefter med solenergioverskuddet. Nyttig til at sikre en minimum rækkevidde selv på mørke dage.", "label": "<PERSON>. opladning %"}, "onlyForSocBasedCharging": "Disse indstillinger er kun tilgængelig for køretøjer med kendt opladningsniveau.", "phasesConfigured": {"label": "<PERSON><PERSON><PERSON>", "no1p3pSupport": "<PERSON><PERSON><PERSON> er din oplader forbundet?", "phases_0": "automatisk skift", "phases_1": "1 fase", "phases_1_hint": "({min} til {max})", "phases_3": "3 fase", "phases_3_hint": "({min} til {max})"}, "smartCostCheap": "<PERSON><PERSON> opladning fra elnet", "smartCostClean": "<PERSON><PERSON><PERSON><PERSON> opladning fra elnet", "title": "Indstillinger {0}", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+Sol", "now": "<PERSON><PERSON>", "off": "<PERSON>a", "pv": "Sol", "smart": "Smart"}, "provider": {"login": "<PERSON>g på", "logout": "Log ud"}, "startConfiguration": "Lad os starte konfigurationen", "targetCharge": {"activate": "<PERSON><PERSON><PERSON>", "co2Limit": "CO₂ grænse på {co2}", "costLimitIgnore": "Den konfigurerede {limit} vil blive ignoreret i denne periode.", "currentPlan": "Aktiv plan", "descriptionEnergy": "<PERSON>vornår skal {targetEnergy} være sendt til køretøjet?", "descriptionSoc": "Hvornår skal køretøjet være opladet til {targetSoc}?", "goalReached": "<PERSON><PERSON><PERSON> er allerede nået", "inactiveLabel": "<PERSON><PERSON><PERSON> tid", "nextPlan": "Næste plan", "notReachableInTime": "<PERSON><PERSON><PERSON> vil blive n<PERSON>et {overrun} senere.", "onlyInPvMode": "Opladningsplan virker kun i solar mode.", "planDuration": "Opladningstid", "planPeriodLabel": "Periode", "planPeriodValue": "{start} til {end}", "planUnknown": "<PERSON>kke kendt endnu", "preview": "forhåndsvising", "priceLimit": "pris grænse på {price}", "remove": "<PERSON><PERSON><PERSON>", "setTargetTime": "ingen", "targetIsAboveLimit": "Den indstillede opladningsgrænse på {limit} ignoreres i denne periode.", "targetIsAboveVehicleLimit": "Køretøjets begrænsning er under opladningsmålet.", "targetIsInThePast": "<PERSON><PERSON><PERSON><PERSON> et tidspunkt i fremtiden, Marty.", "targetIsTooFarInTheFuture": "Vi justerer planen så snart vi ved mere om fremtiden.", "title": "<PERSON><PERSON><PERSON> tid", "today": "i dag", "tomorrow": "i morgen", "update": "Opdatering", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON> at indstille det.", "vehicleCapacityRequired": "Køretøjets batterikapacitet skal kendes for at estimere opladningstiden."}, "targetChargePlan": {"chargeDuration": "Opladningstid", "co2Label": "CO₂ udledning ⌀", "priceLabel": "<PERSON>er<PERSON> pris", "timeRange": "{day} {range} t", "unknownPrice": "stadig ukendt"}, "targetEnergy": {"label": "Beg<PERSON><PERSON><PERSON><PERSON>", "noLimit": "ingen"}, "vehicle": {"addVehicle": "Tilføj køretøj", "changeVehicle": "<PERSON><PERSON><PERSON>", "detectionActive": "Detektering af køretøj…", "fallbackName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreActions": "Flere handlinger", "none": "Intet køretøj", "notReachable": "Ingen kontakt til køretøj. Prøv at genstarte evcc.", "targetSoc": "Beg<PERSON><PERSON><PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Temp. grænse", "unknown": "Gæstekøretøj", "vehicleSoc": "<PERSON><PERSON><PERSON>"}, "vehicleStatus": {"awaitingAuthorization": "Venter på godkendelse.", "batteryBoost": "Boost fra batteri er aktiv.", "charging": "<PERSON><PERSON><PERSON>…", "cheapEnergyCharging": "<PERSON><PERSON> energi er tilgængelig.", "cheapEnergyNextStart": "Billig energi i {duration}.", "cheapEnergySet": "Prisgrænse fastsat.", "cleanEnergyCharging": "<PERSON><PERSON><PERSON><PERSON> energi er tilgængelig.", "cleanEnergyNextStart": "<PERSON><PERSON><PERSON><PERSON> energi i {duration}.", "cleanEnergySet": "CO₂ grænse fastsat.", "climating": "Forkonditionering registreret.", "connected": "Forbundet.", "disconnectRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> at forbinde igen.", "disconnected": "A<PERSON>brud<PERSON>.", "feedinPriorityNextStart": "<PERSON><PERSON><PERSON> indf<PERSON>ringsp<PERSON> i {duration}.", "feedinPriorityPausing": "Soloplad<PERSON> sat på pause for at maksimere nettilførsel.", "finished": "<PERSON><PERSON><PERSON>g.", "minCharge": "Minimum opladning til {soc}.", "pvDisable": "Ikke nok overskud. <PERSON>er snart pause.", "pvEnable": "Overskud tilgængeligt. <PERSON>er snart.", "scale1p": "Reducerer snart til 1-faset opladning.", "scale3p": "Øger snart til 3-faset opladning.", "targetChargeActive": "Opladningsplan er aktiv. Forventes afsluttet om {duration}.", "targetChargePlanned": "Opladningsplan starter om {duration}.", "targetChargeWaitForVehicle": "Plan for opladning er klar. Venter på køretøj…", "vehicleLimit": "Køretøjets grænse", "vehicleLimitReached": "Køretøjets grænse er nået.", "waitForVehicle": "Parat. Venter på køretøj…", "welcome": "<PERSON><PERSON> indledende opladning for at bekræfte forbindelsen."}, "vehicles": "<PERSON><PERSON>", "welcome": "Hej !"}, "notifications": {"dismissAll": "A<PERSON>vis alle", "logs": "Se hele log filen", "modalTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "offline": {"configurationError": "Fejl under opstart. Tjek din konfiguration og genstart.", "message": "<PERSON><PERSON><PERSON> forbundet til en server.", "restart": "Genstart", "restartNeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for at anvende ændringer.", "restarting": "Serveren kommer tilbage om et øjeblik."}, "passwordModal": {"description": "Angiv adgangskode for at beskytte konfigurations indstillinger. Det er stadig mulig at benytte hovedskærmen uden at logge på.", "empty": "Adgangskode må ikke være tomt", "error": "Fejl: ", "labelCurrent": "Nuværende adgangskode", "labelNew": "Ny adgangskode", "labelRepeat": "Gentag adgangskoden", "newPassword": "<PERSON><PERSON> adgangskode", "noMatch": "Adgangskode stemmer ikke", "titleNew": "Angiv Administrator <PERSON><PERSON><PERSON>", "titleUpdate": "Opdater Administrator <PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePassword": "Opdater adgangskode"}, "session": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "co2": "CO2", "date": "Tidsrum", "delete": "Slet", "finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meterstart": "kilometertælleraflæsning", "meterstop": "Meter slut", "odometer": "Kilometerstand", "price": "<PERSON><PERSON>", "started": "Startet", "title": "Lade session"}, "sessions": {"avgPower": "⌀ Effekt", "avgPrice": "⌀ pris", "chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Pris {byGroup}", "byGroupLoadpoint": "<PERSON><PERSON>", "byGroupVehicle": "<PERSON><PERSON>", "energy": "Opladet energi", "energyGrouped": "Sol vs elnet energi", "energyGroupedByGroup": "Energi {byGroup}", "energySubSolar": "{value} sol", "energySubTotal": "{value} total", "groupedCo2ByGroup": "CO₂-mængde {byGroup}", "groupedPriceByGroup": "Total pris {byGroup}", "historyCo2": "CO₂-Emission", "historyCo2Sub": "{value} total", "historyPrice": "Opladningspris", "historyPriceSub": "{value} total", "solar": "<PERSON> andel over året", "solarByGroup": "Solar andel {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energi (kWh)", "chargeduration": "<PERSON><PERSON><PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "Oprettet", "finished": "<PERSON><PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "Ladepunkt", "meterstart": "Målerstart (kWh)", "meterstop": "Målerstop (kWh)", "odometer": "Kilometertal (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Pris/kWh", "solarpercentage": "Sol (%)", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "csvPeriod": "Download {period} CSV", "csvTotal": "Download total CSV", "date": "Start", "energy": "<PERSON><PERSON><PERSON>", "filter": {"allLoadpoints": "alle ladepunkter", "allVehicles": "alle køretøjer", "filter": "Filter"}, "group": {"co2": "Emission", "grid": "Elnet", "price": "<PERSON><PERSON>", "self": "Sol"}, "groupBy": {"loadpoint": "Opladnings punkt", "none": "Total", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Ladepunkt", "noData": "Ingen opladninger i denne måned.", "overview": "Overblik", "period": {"month": "<PERSON><PERSON><PERSON>", "total": "Total", "year": "<PERSON><PERSON>"}, "price": "<PERSON><PERSON>", "reallyDelete": "<PERSON>r du sikker på at du vil slette denne session?", "showIndividualEntries": "<PERSON>is individuelle sessioner", "solar": "solenergi", "title": "<PERSON><PERSON><PERSON><PERSON> Sessioner", "total": "Total", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Sol"}, "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Gå til fuldskærmsvisning", "exit": "Afslut fuldskærm", "label": "Fuldskærm"}, "hiddenFeatures": {"label": "Experimentalt", "value": "Vis eksperimentelle brugergrænsefladefunktioner."}, "language": {"auto": "Automatisk", "label": "Sp<PERSON>"}, "sponsorToken": {"expires": "<PERSON> sponsortoken udløber {inXDays}. {getNewToken} og opdater det her.", "getNew": "Hent en ny", "hint": "Note: Vi automatiserer dette i fremtiden."}, "telemetry": {"label": "Telemetri"}, "theme": {"auto": "system", "dark": "<PERSON>ørk", "label": "Design", "light": "lys"}, "time": {"12h": "12-timer", "24h": "24-timer", "label": "Tidsformat"}, "title": "Brugergrænseflade", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON>", "mi": "mil"}}, "smartCost": {"activeHours": "{active} af {total}", "activeHoursLabel": "Aktive timer", "applyToAll": "<PERSON><PERSON><PERSON> overalt?", "batteryDescription": "<PERSON><PERSON>r hjemmebatteriet med energi fra elnettet.", "cheapTitle": "<PERSON><PERSON> opladning fra el<PERSON>et", "cleanTitle": "<PERSON><PERSON><PERSON><PERSON> opladning fra el<PERSON>et", "co2Label": "CO₂ udledning", "co2Limit": "CO₂ grænse", "loadpointDescription": "Aktiverer midlertidig hurtig-opladning i solcelle tilstand.", "modalTitle": "Smart opladning fra elnettet", "none": "ingen", "priceLabel": "<PERSON>er<PERSON> pris", "priceLimit": "<PERSON><PERSON> græ<PERSON>", "resetAction": "<PERSON><PERSON><PERSON>", "resetWarning": "Dynamisk elnet pris eller CO₂-kilde er ikke konfigureret. Men der er alligevel sat en begrænsning på {limit}. Skal din konfiguration justeres?", "saved": "Gemt."}, "smartFeedInPriority": {"activeHoursLabel": "Pauserede timer", "description": "Sætter opladningen på pause under h<PERSON><PERSON> priser for at prioritere rentabel nettilførsel.", "priceLabel": "Indfødningspris", "priceLimit": "Indfødningsgrænse", "resetWarning": "Der er ingen dynamisk indfødnings tarif konfigureret. Der er dog stadig en grænse på {limit}. Ryd op i din konfiguration?", "title": "Indfødnings prioritet"}, "startupError": {"configFile": "Konfigurationsfil brugt:", "configuration": "Opsætning", "description": "Tjek venligst din konfigurationsfil. Hvis fejlmeddelelsen ikke hjæ<PERSON>, så tjek {0}.", "discussions": "GitHub Diskussioner", "fixAndRestart": "L<PERSON>s problemet og genstart serveren.", "hint": "Bemærk: Det kan og<PERSON>å være, at du har en defekt enhed (inverter, må<PERSON>, ...). Tjek dine netværksforbindelser.", "lineError": "<PERSON>jl i {0}.", "lineErrorLink": "linje {0}", "restartButton": "Genstart", "title": "Fe<PERSON>l ved opstart"}}