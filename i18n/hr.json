{"batterySettings": {"batteryLevel": "<PERSON><PERSON><PERSON>", "bufferStart": {"above": "kada je iznad {soc}.", "full": "kad je na {soc}.", "never": "samo s dovoljno viška."}, "capacity": "{energy} od {total}", "control": "Uprav<PERSON><PERSON><PERSON>", "discharge": "Spriječi pražnjenje u brzom načinu rada i planirano punjenje.", "disclaimerHint": "Napomena:", "disclaimerText": "Ove postavke utječu samo na solarni modus. Punjenje se prilagođava u skladu s tim.", "gridChargeTab": "Punjenje mrežom", "legendBottomName": "Prioritet punjenu kućne baterije", "legendBottomSubline": "dok ne dosegne {soc} napunjenosti.", "legendMiddleName": "Prioritet punjenju vozila", "legendMiddleSubline": "kada je kućna baterija iznad {soc}.", "legendTopAutostart": "Počni automatski", "legendTopName": "Punjenje vozila iz baterije", "legendTopSubline": "kada je kućna baterija iznad {soc}.", "modalTitle": "<PERSON><PERSON><PERSON>", "usageTab": "Upotreba baterije"}, "config": {"aux": {"description": "Uređaj koji prilagođava vlastitu potrošnju prema dostupnom višku energije (poput pametnih grijača vode). evcc očekuje kako će ovaj uređaj smanjiti potrošnju energije kada je to potrebno.", "titleAdd": "<PERSON><PERSON><PERSON> t<PERSON>š<PERSON>", "titleEdit": "Urediti samoregulirajuće trošilo"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>"}, "charge": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>"}, "charger": {"chargers": "EV punjači", "generic": "Standardne integracije", "heatingdevices": "Uređaji za grijanje", "ocppHelp": "Ko<PERSON>rajte ovu adresu u postavke punjača.", "ocppLabel": "URL OCPP poslužitelja", "switchsockets": "Promijenjive utičnice", "template": "<PERSON>iz<PERSON><PERSON><PERSON><PERSON>", "titleAdd": {"charging": "<PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON>"}, "titleEdit": {"charging": "Urediti punjač", "heating": "Urediti gri<PERSON>č"}, "type": {"custom": {"charging": "Korisnički definirani punjač", "heating": "Korisnički definirani grijač"}, "heatpump": "<PERSON><PERSON><PERSON><PERSON><PERSON> definirana dizalica topline"}}, "circuits": {"description": "Osigurava da suma svih točaka punjenja povezanih na mrežu ne prelazi postavljene granice snage i struje. Krugovi mogu biti složeni hijerarhijski.", "title": "Upravljanje opterećenjem"}, "control": {"description": "Uobičajene vrijednosti su uglavnom ispravne. Promijenite samo ako znate što radite.", "descriptionInterval": "Učestalost osvježavanja podataka u sekundama. Definira koliko će često evcc čitati podatke s brojača, prilagoditi snagu punjenja i osvježavati sučelje. Prekratki intervali (kraći od 30s) mogu prouzročiti česte oscilacije i prouzročiti neželjene ishode.", "descriptionResidualPower": "Pomiče operacijsku točku kontrolne petlje. Ukoliko imate kućnu bateriju, savjetuje se vrijednost od 100W. Na taj način će upotreba baterije dobiti prioritet nad energetskom mrežom.", "labelInterval": "Interval ažuriranja", "labelResidualPower": "Preostala snaga", "title": "Učestalost osvježavanja"}, "deviceValue": {"amount": "Iznos", "broker": "Posrednik", "bucket": "Kanta", "capacity": "Kapacitet", "chargeStatus": "<PERSON><PERSON>", "chargeStatusA": "nije pove<PERSON>", "chargeStatusB": "povezano", "chargeStatusC": "punjenje", "chargeStatusE": "bez snage", "chargeStatusF": "<PERSON><PERSON><PERSON><PERSON>", "chargedEnergy": "Napunjeno", "co2": "Mreža CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "Kontrolirano", "currency": "Valuta", "current": "Struja", "currentRange": "Struja", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "energy": "Energija", "feedinPrice": "Cijena izvoza", "gridPrice": "Cijena uvoza", "heaterTempLimit": "Ograničenje grijača", "hemsType": "Sustav", "identifier": "RFID-identifikator", "no": "ne", "odometer": "Odometar", "org": "Organizacija", "phaseCurrents": "Struja L1, L2, L3", "phasePowers": "Snaga L1, L2, L3", "phaseVoltages": "Napon L1, L2, L3", "phases1p3p": "Promjena faza", "power": "Snaga", "powerRange": "<PERSON><PERSON><PERSON><PERSON> snaga", "range": "Doseg", "singlePhase": "<PERSON><PERSON> faza", "soc": "Napunjenost", "solarForecast": "Prognoza za solare", "temp": "Temperatura", "topic": "<PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "Ograničenje vozila", "yes": "da"}, "deviceValueChargeStatus": {"A": "A (nije povezano)", "B": "B (povezano)", "C": "C (punjenje)"}, "devices": {"auxMeter": "<PERSON><PERSON><PERSON>", "batteryStorage": "Baterijski spremnik", "solarSystem": "<PERSON><PERSON> sustav"}, "editor": {"loading": "Učitavanje YAML urednika…"}, "eebus": {"description": "Postavke koje omogućuju evcc-u komunikaciju s drugim EEBus uređajima.", "title": "EEBus"}, "ext": {"description": "Može se koristiti za upravljanje opterećenjem ili za statistiku.", "titleAdd": "<PERSON><PERSON><PERSON> m<PERSON>", "titleEdit": "Ured<PERSON> m<PERSON>"}, "form": {"danger": "Opasnost", "deprecated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON>", "optional": "opcionalno"}, "general": {"cancel": "Odustani", "customHelp": "Izradite posebni uređaj koristeći evccov sustav dodataka.", "customOption": "Posebni uređaj", "delete": "Obrisati", "docsLink": "Vidjeti dokumentaciju.", "experimental": "Eksperimentalno", "hideAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON>", "invalidFileSelected": "<PERSON>eisp<PERSON><PERSON><PERSON>", "noFileSelected": "Datoteka nije odabrana.", "off": "isključeno", "on": "uključeno", "password": "Lozinka", "readFromFile": "Učitati iz datoteke", "remove": "Ukloniti", "save": "Spremiti", "selectFile": "Pretraži", "showAdvancedSettings": "Prikaži napred<PERSON>av<PERSON>", "telemetry": "Telemetrija", "templateLoading": "U<PERSON>ita<PERSON><PERSON>…", "title": "<PERSON><PERSON><PERSON>", "validateSave": "Provjeriti i spremiti"}, "grid": {"title": "Mr<PERSON><PERSON><PERSON> bro<PERSON>", "titleAdd": "<PERSON><PERSON><PERSON> bro<PERSON>", "titleEdit": "Uredi mrežno brojilo"}, "hems": {"description": "Povezati evcc s nekim drugim sustavom za upravljanje kućnom energijom (HEMS).", "title": "HEMS"}, "icon": {"change": "prom<PERSON>na"}, "influx": {"description": "Zapisuje podatke u InfluxDB. Upotrijebite Grafanu ili druge alate za vizualizaciju podataka.", "descriptionToken": "Za izradu pogledajte InfluxDB dokumentaciju. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Kanta", "labelCheckInsecure": "Dozvoli samopotpisane certifikate", "labelDatabase": "Baza podataka", "labelInsecure": "Provjera certifikata", "labelOrg": "Organizacija", "labelPassword": "Lozinka", "labelToken": "API token", "labelUrl": "URL", "labelUser": "Korisničko ime", "title": "InfluxDB", "v1Support": "Potrebna podrška za InfluxDB 1.x?", "v2Support": "Natrag na InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "<PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON>"}, "addMeter": "<PERSON><PERSON><PERSON> brojilo energije", "cancel": "Odustati", "chargerError": {"charging": "Nužno je postaviti punjač."}, "chargerLabel": {"charging": "<PERSON><PERSON><PERSON><PERSON>"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Koristiti će raspon struje od 6 i 16A.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Koristiti će raspon struje od 6 do 32A.", "chargerPowerCustom": "drugo", "chargerPowerCustomHelp": "<PERSON><PERSON><PERSON> z<PERSON>ban raspon struje.", "chargerTypeLabel": "<PERSON><PERSON>", "chargingTitle": "Punjenje", "circuitHelp": "Postavljanje upravljanja energijom kako bi se osiguralo da se ne prijeđu trenutna ograničenja.", "circuitLabel": "Strujni krug", "circuitUnassigned": "nepostavljen", "defaultModeHelp": {"charging": "<PERSON><PERSON><PERSON> punjenja kada se vozilo poveže."}, "defaultModeHelpKeep": "Zadržava posljednje korišteni način punjenja.", "defaultModeLabel": "Uobičajeni način", "delete": "Obrisati", "electricalSubtitle": "<PERSON><PERSON> sigu<PERSON>, pitajte električara.", "electricalTitle": "Elektrooprema", "energyMeterHelp": "Dodatni strujomjer ako punjač nema integrirani.", "energyMeterLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "estimateLabel": "Intepolirati razine punjenja između API očitavanja", "maxCurrentHelp": "<PERSON>ra biti veće od minimalne struje.", "maxCurrentLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> struja", "minCurrentHelp": "Postavite ispod 6A samo ako znate što radite.", "minCurrentLabel": "Minimalna struja", "noVehicles": "<PERSON><PERSON> jedno vozilo nije <PERSON>l<PERSON>.", "phases1p": "1 faza", "phases3p": "3 faze", "phasesAutomatic": "Automatske faze", "phasesAutomaticHelp": "Vaš punjač podržava prebacivanje između 1 i 3 faze. Na glavom zaslonu možete postaviti željeno ponašanje prilikom punjenja.", "phasesHelp": "Broj faza povezanih s punjačem.", "phasesLabel": "Faze", "pollIntervalDanger": "Često slanje upita vozilo može isprazniti bateriju. Neki proizvođači vozila mogu aktivno spriječiti punjenje u takvim situacijama. Nije preporučljivo! Koristite ovo samo ako ste svjesni rizika.", "pollIntervalHelp": "Vrijeme između API očitavanja. Kratki intervali mogu isprazniti bateriju vozila.", "pollIntervalLabel": "Interval očitavanja", "pollModeAlways": "uvijek", "pollModeAlwaysHelp": "Uvijek ažurirati status pri standardnim intervalima.", "pollModeCharging": "punjenje", "pollModeChargingHelp": "Ažurirati status vozila samo prilikom punjenja.", "pollModeConnected": "povezano", "pollModeConnectedHelp": "Ažurirati status vozila u standardnim intervalima samo kad je vozilo povezano.", "pollModeLabel": "Ponašanje ažuriranja", "priorityHelp": "Punjači višeg prioriteta imaju prednost pri korištenju viškova iz solarne elektrane.", "priorityLabel": "Prioritet", "save": "Spremiti", "showAllSettings": "Prikazati sve postavke", "solarBehaviorCustomHelp": "Postavite vlastite razine i odgode.", "solarBehaviorDefaultHelp": "Puniti samo s viškom iz solara. Započeti nakon što je višak bio dostupan {enableDelay}. Zaustaviti kada viška nije bilo dulje od {disableDelay}.", "solarBehaviorLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solarModeCustom": "podešeno", "solarModeMaximum": "maks<PERSON><PERSON>o <PERSON>", "thresholdDisableDelayLabel": "Onemogući odgodu", "thresholdDisableHelpInvalid": "Molim upotrijebite pozitivnu vrijednost.", "thresholdDisableHelpPositive": "Zaustavi punjenje kada iz mreže koristi više od {power} energije kroz {delay}.", "thresholdDisableHelpZero": "Zaustaviti kada se minimalna snaga punjenja ne može postići za {delay}.", "thresholdDisableLabel": "Onemogući energiju iz mreže", "thresholdEnableDelayLabel": "Omogući odgodu", "thresholdEnableHelpInvalid": "<PERSON><PERSON> koristite negativnu vrijednost.", "thresholdEnableHelpNegative": "Započeti punjenje kada postoji višak od {surplus} kroz {delay}.", "thresholdEnableHelpZero": "Započeti kada je minimalni višak energije dostupan više od {delay}.", "thresholdEnableLabel": "Omogućiti energiju iz mreže", "titleAdd": "<PERSON><PERSON><PERSON> to<PERSON> punjenja", "titleEdit": "Urediti točku punjenja", "titleExample": "Garaža, dvorište i sl.", "titleLabel": "<PERSON><PERSON><PERSON>", "vehicleAutoDetection": "autodetekcija", "vehicleHelpAutoDetection": "Automatski odabire najvjerojatnije vozilo. Moguće je i ručno izmijeniti.", "vehicleHelpDefault": "Uvijek pretpostavi da se ovo vozilo ovdje puni. Autodetekcija je onemogućena, ali je moguće ručno izmijeniti.", "vehicleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> voz<PERSON>", "vehiclesTitle": "Voz<PERSON>"}, "main": {"addAdditional": "<PERSON><PERSON><PERSON> bro<PERSON>", "addGrid": "<PERSON><PERSON><PERSON> bro<PERSON>", "addLoadpoint": "<PERSON><PERSON><PERSON>", "addPvBattery": "Dodati PV ili bateriju", "addTariffs": "<PERSON><PERSON><PERSON> tarife", "addVehicle": "<PERSON><PERSON><PERSON> v<PERSON>", "configured": "postavljeno", "edit": "u<PERSON>i", "loadpointRequired": "<PERSON><PERSON> jedna točka punjenja mora biti postavljena.", "name": "Ime", "title": "Postavke", "unconfigured": "<PERSON><PERSON>", "vehicles": "Moja vozila", "yaml": "Uređaji od evcc.yaml se ne mogu urediti."}, "messaging": {"description": "Primite poruke o vašim postupcima punjenja.", "title": "Obavijesti"}, "meter": {"cancel": "Odustani", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "Uobičajene integracije", "option": {"aux": "<PERSON><PERSON><PERSON><PERSON>", "battery": "<PERSON><PERSON><PERSON>o<PERSON>", "ext": "<PERSON><PERSON><PERSON> br<PERSON>", "pv": "<PERSON><PERSON><PERSON> bro<PERSON>"}, "save": "Sp<PERSON>i", "specific": "Specifične integracije", "template": "<PERSON>iz<PERSON><PERSON><PERSON><PERSON>", "titleChoice": "<PERSON><PERSON> do<PERSON>?", "validateSave": "Provjeri i spremi"}, "modbus": {"baudrate": "<PERSON>rzina prijenosa podataka", "comset": "ComSet", "connection": "Modbus veza", "connectionHintSerial": "<PERSON><PERSON><PERSON> je uređaj povezan s evccom direktom putem RS485 sučelja.", "connectionHintTcpip": "<PERSON><PERSON><PERSON> ure<PERSON>aj je evccu dostupan putem LAN/WiFi-a.", "connectionValueSerial": "Serijski / USB", "connectionValueTcpip": "Mreža", "device": "<PERSON><PERSON>", "deviceHint": "Primjer: /dev/ttyUSB0", "host": "IP adresa ili mrežno ime", "hostHint": "Primjer: *********", "id": "Modbus ID", "port": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "protocol": "Modbus protokol", "protocolHintRtu": "Veza putem RS485 ili mrežnog adaptera bez prevođenja protokola.", "protocolHintTcp": "U<PERSON><PERSON><PERSON> ima vlastiti LAN/WiFi priključak ili je povezano putem RS485 ili mrežnog uređaja s prevođenjem protokola.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Dozvoli spajanje više korisnika na jedan Modbus uređaj.", "title": "Modbus proxy"}, "mqtt": {"authentication": "Provjera vjerodostojnosti", "description": "Spoji se na MQTT posrednika za izmjenu podataka s drugim sustavima na vašoj mreži.", "descriptionClientId": "Autor poruke. Ukoliko ni<PERSON>, koristiti će se `evcc-[rand]`.", "descriptionTopic": "Ostavite praznim kako bi <PERSON>mogućili objavljivanje.", "labelBroker": "Posrednik", "labelCaCert": "Certifikat <PERSON> (CA)", "labelCheckInsecure": "Dozvoli samopotpisane certifikate", "labelClientCert": "Certif<PERSON>t k<PERSON>", "labelClientId": "ID korisnika", "labelClientKey": "Ključ korisnika", "labelInsecure": "Provjera certifikata", "labelPassword": "Lozinka", "labelTopic": "<PERSON><PERSON>", "labelUser": "Korisničko ime", "publishing": "Objavljivanje", "title": "MQTT"}, "network": {"descriptionHost": "Upotrijebite nastavak .local kako bi omogućili mDNS. Korisno za otkrivanje mobilne aplikacije i nekih OCPP punjača.", "descriptionPort": "Port za web sučelje i API. Morati ćete osvježiti URL u pretraživačau ukoliko ovo promijenite.", "descriptionSchema": "Utječe samo na generiranje URL-a. Odabir HTTPS-a neće omogućiti kriptografiju.", "labelHost": "<PERSON><PERSON>", "labelPort": "Port", "labelSchema": "<PERSON><PERSON>", "title": "Mreža"}, "options": {"boolean": {"no": "ne", "yes": "da"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (nekriptirano)", "https": "HTTPS (kriptirano)"}, "status": {"A": "A (nije povezano)", "B": "B (povezano)", "C": "C (punjenje)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON> bro<PERSON> elektrane", "titleEdit": "Urediti brojilo solarne elektrane"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON><PERSON> bro<PERSON>", "general": "<PERSON><PERSON><PERSON>", "grid": "Mreža", "integrations": "Integracije", "loadpoints": "Točke punjenja", "meter": "Solarna elektrana i baterija", "system": "Sustav", "vehicles": "Voz<PERSON>"}, "sponsor": {"addToken": "Unesite token", "changeToken": "Promijenite token", "description": "Sponzorski model nam omogućava održavanje projekta i razvoj novih mogućnosti. Sponzori imaju pristup svim implementacijama za punjače.", "descriptionToken": "Token možete dobiti na {url}. Moguće je zatražiti i probni token za testiranje.", "error": "Sponzorski token nije is<PERSON>van.", "labelToken": "S<PERSON>nzors<PERSON> token", "title": "Sponzorstvo", "tokenRequired": "Prije izrade ovog uređaja morate postaviti sponzorski token.", "tokenRequiredLearnMore": "Pročitajte više.", "trialToken": "Privre<PERSON>i token"}, "system": {"logs": "Zapisi", "restart": "Ponovno pokretanje", "restartRequiredDescription": "Ponovno pokrenite za primjenu promjena.", "restartRequiredMessage": "Postavke izmijenjene.", "restartingDescription": "<PERSON><PERSON>", "restartingMessage": "Ponovno pokretanje evcc-a."}, "tariffs": {"description": "Unesite svoje tarife električne energije kako bi se izračunala cijena punjenja.", "title": "<PERSON><PERSON><PERSON>"}, "title": {"description": "Prikazano na glavnom zaslonu i kartici pretraživača.", "label": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>lov"}, "validation": {"failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "running": "verifika<PERSON>ja…", "success": "<PERSON><PERSON><PERSON>š<PERSON>", "unknown": "nepoznato", "validate": "verific<PERSON><PERSON>"}, "vehicle": {"cancel": "Odustani", "chargingSettings": "Postavke punjenja", "defaultMode": "Uobičajeni način", "defaultModeHelp": "<PERSON><PERSON><PERSON> punjenja kada se povezuje vozilo.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "generic": "Druge integracije", "identifiers": "RFID identifikatori", "identifiersHelp": "Popis RFID oznaka za identifikaciju vozila. Jedan u svakom redu. Trenutni se identifikatori mogu naći na točci punjenja, na preglednoj stranici.", "maximumCurrent": "Maksimalna jakost struje", "maximumCurrentHelp": "<PERSON>ra biti veća od minimalne jakosti struje.", "maximumPhases": "Maksimalni broj faza", "maximumPhasesHelp": "S koliko faza se ovo vozilo može puniti? Koristi se za izračun potrebnog solarnog viška te za izračun trajanja punjenja.", "minimumCurrent": "Minimalna jakost struje", "minimumCurrentHelp": "Ne postavljajte na manje od 6A, osim ako jako dobro znate što radite.", "online": "Vozilo s internetskim API sučeljem", "primary": "Uobičajene integracije", "priority": "Prioritet", "priorityHelp": "Viši prioritet znaći da će ovo vozilo imati preferirani pristup višku energije iz solara.", "save": "Sp<PERSON>i", "scooter": "<PERSON><PERSON><PERSON>", "template": "<PERSON>iz<PERSON><PERSON><PERSON><PERSON>", "titleAdd": "<PERSON><PERSON><PERSON> voz<PERSON>", "titleEdit": "<PERSON><PERSON><PERSON> vozilo", "validateSave": "Verificiraj i spremi"}}, "footer": {"community": {"greenEnergy": "Solarno", "greenEnergySub1": "napunjeno s evcc-om", "greenEnergySub2": "od listopada 2022", "greenShare": "Solarni udio", "greenShareSub1": "snaga koju pruža", "greenShareSub2": "solarna i baterijska pohrana", "power": "<PERSON><PERSON><PERSON>", "powerSub1": "{activeClients} od {totalClients} sudionika", "powerSub2": "punjenje…", "tabTitle": "Živa zajednica"}, "savings": {"co2Saved": "{value} <PERSON><PERSON><PERSON><PERSON><PERSON>", "co2Title": "CO₂ emisija", "configurePriceCo2": "Nauči kako konfigurirati podatke o cijeni i CO₂.", "footerLong": "{percent} solarna energija", "footerShort": "{percent} solarno", "modalTitle": "Pregled energije punjenja", "moneySaved": "{value} <PERSON><PERSON><PERSON><PERSON><PERSON>", "percentGrid": "{grid} kWh mreža", "percentSelf": "{self} kWh solarno", "percentTitle": "Solarna energija", "period": {"30d": "zadnjih 30 dana", "365d": "zadnjih 365 dana", "thisYear": "ova godina", "total": "s<PERSON><PERSON><PERSON><PERSON>"}, "periodLabel": "Razdoblje:", "priceTitle": "Cijena energije", "referenceGrid": "<PERSON><PERSON><PERSON><PERSON>", "referenceLabel": "Referentni podaci:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "<PERSON><PERSON><PERSON> sponzor", "becomeSponsorExtended": "Direktno nas podržite kako biste dobili naljepnice.", "confetti": "S<PERSON><PERSON>an/na za konfete?", "confettiPromise": "Dobivaš naljepnice i digitalne konfete", "sticker": "… ili evcc naljepnice?", "supportUs": "<PERSON><PERSON> misija je unaprijediti punjenje putem solarne energije. Pomognite evccu plaćajući onoliko koliko vam vrijedi.", "thanks": "<PERSON><PERSON><PERSON>, {sponsor}! Tvoj doprinos pomaže u daljnjem razvoju evcc-a.", "titleNoSponsor": "Podrži nas", "titleSponsor": "Ti si podržavatelj", "titleTrial": "Probni način rada", "titleVictron": "Sponzor - Victron Energy", "trial": "U probnom ste načinu rada i možete koristiti sve mogućnosti. Molim razmotrite sponzoriranje projekta.", "victron": "Koristite evcc na uređaju od Victron Energy i imate pristup svim mogućnostima."}, "telemetry": {"optIn": "<PERSON><PERSON><PERSON> dati svoje podatke.", "optInMoreDetails": "<PERSON><PERSON><PERSON><PERSON> {0}.", "optInMoreDetailsLink": "ovdje", "optInSponsorship": "Potrebno sponzorstvo."}, "version": {"availableLong": "dostupna je nova verzija", "modalCancel": "Odustani", "modalDownload": "<PERSON><PERSON><PERSON>", "modalInstalledVersion": "Instalirana verzija", "modalNoReleaseNotes": "Nema dos<PERSON>pnih napomena o izdanju. Više informacija o novoj verziji:", "modalTitle": "Dostupna je nova verzija", "modalUpdate": "Instaliraj", "modalUpdateNow": "<PERSON><PERSON><PERSON><PERSON> sada", "modalUpdateStarted": "Pokretanje nove evcc verzije …", "modalUpdateStatusStart": "Instalacija je pokrenuta:"}}, "forecast": {"co2": {"average": "Prosjek", "lowestHour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sat", "range": "Ra<PERSON><PERSON>"}, "modalTitle": "Predviđanje", "price": {"average": "Prosjek", "lowestHour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sat", "range": "Opseg"}, "solar": {"dayAfterTomorrow": "Prekosutra", "partly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remaining": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "tomorrow": "Sutra"}, "solarAdjust": "<PERSON><PERSON> solarnu prognozu prema pravim podacima proizvodnje {percent}.", "type": {"co2": "CO₂", "price": "Cijena", "solar": "Solarno"}}, "header": {"about": "O", "blog": "Blog", "docs": "Dokumentacija", "github": "GitHub", "login": "Prijave vozila", "logout": "<PERSON><PERSON><PERSON><PERSON>", "nativeSettings": "Promjena poslužitelja", "needHelp": "Trebate pomoć?", "sessions": "<PERSON><PERSON><PERSON> punjenja"}, "help": {"discussionsButton": "GitHub rasprave", "documentationButton": "Dokumentacija", "issueButton": "Prijavi gre<PERSON>", "issueDescription": "<PERSON><PERSON><PERSON> se čudno ili pogrešno ponašanje?", "logsButton": "<PERSON><PERSON> zapisa", "logsDescription": "Pogledajte greške u zapisima.", "modalTitle": "<PERSON><PERSON><PERSON><PERSON> pomo<PERSON>?", "primaryActions": "Nešto ne radi onako kako bi trebalo? Ovo su dobra mjesta za pomoć.", "restart": {"cancel": "Odustani", "confirm": "Da, pokreni ponovo!", "description": "U normalnim okolnostima ponovno pokretanje ne bi trebalo biti potrebno. Prijavi grešku ako redovito moraš ponovo pokretati evcc.", "disclaimer": "Napomena: evcc će prekinuti rad i osloniti se na operacijski sustav za ponovno pokretanje usluge.", "modalTitle": "<PERSON><PERSON><PERSON> želiš ponovo pokrenuti aplikaciju?"}, "restartButton": "Pokreni ponovo", "restartDescription": "Jeste li pokušali isključiti i ponovo uključiti aplikaciju?", "secondaryActions": "<PERSON><PERSON> ne možete riješiti problem? Evo još nekih opcija."}, "log": {"areaLabel": "Filter po području", "areas": "Sva područja", "download": "Preuzmite cijeli zapis", "levelLabel": "Filter po razini zapisa", "nAreas": "{count} područ<PERSON>", "noResults": "<PERSON><PERSON>.", "search": "Traži", "selectAll": "odaberi sve", "showAll": "Prikaži sve zapise", "title": "Zapisi", "update": "Automatska nadogradnja"}, "loginModal": {"cancel": "Ot<PERSON>ži", "demoMode": "Prijava nije podržana u demo načinu rada.", "error": "Prijava neusp<PERSON>šna: ", "iframeHint": "Otvoriti evcc u novoj kartici.", "iframeIssue": "<PERSON><PERSON><PERSON> lo<PERSON>ka je is<PERSON>na, ali se čini kako je vaš pretraž<PERSON> odbacio validacijski kolačić. Ovo se može dogoditi ako koristiti evcc unutar iframe-a putem HTTP-a.", "invalid": "Lozinka je neispravna.", "login": "<PERSON><PERSON><PERSON><PERSON>", "password": "Lozinka", "reset": "Ponovno postavljanje lozinke?", "title": "Autentifikacija"}, "main": {"chargingPlan": {"active": "Aktivno", "addRepeatingPlan": "Dodaj ponavlja<PERSON>i plan", "arrivalTab": "<PERSON><PERSON><PERSON>", "day": "<PERSON>", "departureTab": "Odlazak", "goal": "<PERSON><PERSON><PERSON>", "modalTitle": "<PERSON>", "none": "bez", "planNumber": "Plan {number}", "preconditionDescription": "Puniti vozilo {duration} prije polaska radi optimiziranja baterije.", "preconditionLong": "Kasno punjenje", "preconditionOptionAll": "sve", "preconditionOptionNo": "ne", "preconditionShort": "Kasno", "remove": "Ukloni", "repeating": "ponavljanje", "repeatingPlans": "Ponavljajući planovi", "selectAll": "Odaberi sve", "time": "<PERSON><PERSON>je<PERSON>", "title": "Plan", "titleMinSoc": "<PERSON><PERSON> punjenje", "titleTargetCharge": "Odlazak", "unsavedChanges": "Postoje promjene koje nisu spremljene. Spremiti ih?", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekdays": "<PERSON>"}, "energyflow": {"battery": "Baterija", "batteryCharge": "Punjen<PERSON>", "batteryDischarge": "Pražnjenje baterije", "batteryGridChargeActive": "aktivno punjenje putem mreže", "batteryGridChargeLimit": "punjene putem mreže kad", "batteryHold": "Baterija (zaključano)", "batteryTooltip": "{energy} od {total} ({soc})", "forecastTooltip": "predviđanje: preostala današnja solarna proizvodnja", "gridImport": "Upotreba mreže", "homePower": "Potrošnja", "loadpoints": "Punjač| Punjač | {count} punjača", "noEnergy": "<PERSON><PERSON> m<PERSON> podata<PERSON>", "pv": "<PERSON><PERSON> sustav", "pvExport": "Izvoz u mrežu", "pvProduction": "Proizvodnja", "selfConsumption": "Samopotrošnja"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON>…", "connected": "<PERSON><PERSON><PERSON><PERSON>.", "vehicleLimit": "Ograničenje grijača", "waitForVehicle": "Spremno. Čeka se na grijač…"}, "loadpoint": {"avgPrice": "⌀ cijena", "charged": "Napunjeno", "co2": "⌀ CO₂", "duration": "<PERSON><PERSON><PERSON><PERSON>", "fallbackName": "<PERSON><PERSON><PERSON> punje<PERSON>", "finished": "Vrijeme završetka", "power": "Snaga", "price": "T<PERSON>šak", "remaining": "<PERSON><PERSON><PERSON>", "remoteDisabledHard": "{source}: isključeno", "remoteDisabledSoft": "{source}: adaptivno solarno punjenje isključeno", "solar": "Solarno"}, "loadpointSettings": {"batteryBoost": {"description": "Brzo napuni iz kućne baterije.", "label": "Pojačati baterijom", "mode": "Dostupno samo u '<PERSON><PERSON>' i 'Min+Solar' načinima rada.", "once": "Pojačavanje je aktivirano za ovo punjenje."}, "batteryUsage": "<PERSON><PERSON><PERSON>", "currents": "<PERSON><PERSON><PERSON>", "default": "zadano", "disclaimerHint": "Bilješka:", "limitSoc": {"description": "Ograničenje napajanja koje se koristi kada je ovo vozilo povezano.", "label": "Zadano <PERSON>"}, "maxCurrent": {"label": "Maks. struja"}, "minCurrent": {"label": "<PERSON><PERSON> struja"}, "minSoc": {"description": "<PERSON><PERSON><PERSON> se „brzo” puni na {0} u solarnom modusu, zatim sa solarnim viškom. Korisno za osiguravanje minimalnog dometa čak i u danima s manje sunca.", "label": "<PERSON><PERSON>"}, "onlyForSocBasedCharging": "Ove su opcije dostupne samo za vozila s poznatom razinom napunjenosti.", "phasesConfigured": {"label": "Faze", "no1p3pSupport": "<PERSON>ko je vaš punja<PERSON> povezan?", "phases_0": "automatsko prebacivanje", "phases_1": "jednofazno", "phases_1_hint": "({min} do {max})", "phases_3": "trofazno", "phases_3_hint": "({min} do {max})"}, "smartCostCheap": "Punjenje jeftinom energijom iz mreže", "smartCostClean": "Punjenje čistom energijom iz mreže", "title": "Postavke za {0}", "vehicle": "<PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+Solar", "now": "Brz<PERSON>", "off": "Isklj.", "pv": "Solarno", "smart": "Pametno"}, "provider": {"login": "prijavi se", "logout": "odja<PERSON> se"}, "startConfiguration": "Započnimo <PERSON>", "targetCharge": {"activate": "Aktiviraj", "co2Limit": "CO₂ granica od {co2}", "costLimitIgnore": "Konfigu<PERSON>rani {limit} zanemarit će se tijekom ovog razdoblja.", "currentPlan": "Aktivni plan", "descriptionEnergy": "Do kada bi {targetEnergy} trebalo napojiti vozilo?", "descriptionSoc": "Kada bi se vozilo moralo napuniti na {targetSoc}?", "goalReached": "<PERSON><PERSON><PERSON> je već dostignut", "inactiveLabel": "Ciljano v<PERSON>me", "nextPlan": "Sljedeći plan", "notReachableInTime": "<PERSON><PERSON><PERSON>e se postići {overrun} kasnije.", "onlyInPvMode": "Plan punjenja radi samo u solarnom modusu.", "planDuration": "<PERSON><PERSON><PERSON><PERSON>", "planPeriodLabel": "Razdoblje", "planPeriodValue": "{start} do {end}", "planUnknown": "jo<PERSON> nije poznato", "preview": "Pregled plana", "priceLimit": "ograničenje cijene od {price}", "remove": "Ukloni", "setTargetTime": "bez", "targetIsAboveLimit": "Konfigurirano ograničenje napajanja od {limit} bit će zanemareno tijekom ovog razdoblja.", "targetIsAboveVehicleLimit": "Ograničenje vozila je ispod cilja punjenja.", "targetIsInThePast": "Odaberi vrijeme u budućnosti.", "targetIsTooFarInTheFuture": "Prilagodit ćemo plan čim budemo znali više o budućnosti.", "title": "Ciljano v<PERSON>me", "today": "danas", "tomorrow": "sutra", "update": "<PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON><PERSON> kako to konfigurirati.", "vehicleCapacityRequired": "Kapacitet baterije vozila je potreban za procjenjivanje vremena punjenja."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "co2Label": "⌀ CO₂ emisije", "priceLabel": "Cijena energije", "timeRange": "{day} {range} h", "unknownPrice": "jos nepoznato"}, "targetEnergy": {"label": "Ogranič<PERSON><PERSON>", "noLimit": "bez"}, "vehicle": {"addVehicle": "<PERSON><PERSON><PERSON> voz<PERSON>", "changeVehicle": "Promijeni vozilo", "detectionActive": "Otkrivanje vozila …", "fallbackName": "<PERSON><PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "none": "<PERSON><PERSON> vozila", "notReachable": "Vozilo nije dostupno. Pokušajte ponovno pokrenuti evcc.", "targetSoc": "Ogranič<PERSON><PERSON>", "temp": "Temp.", "tempLimit": "Granica temperature", "unknown": "<PERSON><PERSON><PERSON>", "vehicleSoc": "Napunjeno"}, "vehicleStatus": {"awaitingAuthorization": "Čekanje na dozvolu.", "batteryBoost": "Pojačavanje baterijom je aktivno.", "charging": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "cheapEnergyCharging": "<PERSON><PERSON><PERSON> energija je dostupna.", "cheapEnergyNextStart": "<PERSON><PERSON><PERSON> energija za {duration}.", "cheapEnergySet": "Postavljena granica cijene.", "cleanEnergyCharging": "Čista energija je dostupna.", "cleanEnergyNextStart": "Čista energija za {duration}.", "cleanEnergySet": "Granica za CO₂ postavljena.", "climating": "Otkriveno predkondicioniranje.", "connected": "Povezano.", "disconnectRequired": "Zadatak prekinut. <PERSON><PERSON> ponovno spojite.", "disconnected": "Nepovezano.", "finished": "Završeno.", "minCharge": "Minimalno punjenje do {soc}.", "pvDisable": "Nema dovoljno viška. Uskoro se zaustavlja.", "pvEnable": "Dostupan je višak. Uskoro počinje.", "scale1p": "Uskoro se prebacuje na jednofazno punjenje.", "scale3p": "Uskoro se prebacuje na trofazno punjenje.", "targetChargeActive": "Aktivan je plan punjenja. Procijenjeni završetak za {duration}.", "targetChargePlanned": "Planirano punjenje počinje za {duration}.", "targetChargeWaitForVehicle": "Planirano punjenje je spremno. Čeka se na vozilo…", "vehicleLimit": "Granica vozila", "vehicleLimitReached": "Dosegnuto ograničenje na vozilu.", "waitForVehicle": "Spremno. Čeka se na vozilo …", "welcome": "Kratko početno punjenje kako bi se potvrdila povezanost."}, "vehicles": "Parkiranje", "welcome": "Dobrodo<PERSON>li!"}, "notifications": {"dismissAll": "Odbaci sve", "logs": "Pregled cijelog zapisa", "modalTitle": "Obavijesti"}, "offline": {"configurationError": "Greška pri pokretanju. Provjerite postavke i ponovno pokrenite.", "message": "Ne postoji veza s poslužiteljam.", "restart": "Ponovno pokreni", "restartNeeded": "Nužno za primjenu promjena.", "restarting": "Poslužitelj će biti dostupan za koji trenutak."}, "passwordModal": {"description": "Postavite lozinku za zaštitu postavki. Početna stranica se može koristiti i bez prijave.", "empty": "Lozinka ne bi trebala biti prazna", "error": "Greška: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON>", "labelNew": "Nova lozinka", "labelRepeat": "Ponovite lozinku", "newPassword": "Izradi lozinku", "noMatch": "Lozinke se ne podudaraju", "titleNew": "<PERSON><PERSON>", "titleUpdate": "Obnovi lozinku administratora", "updatePassword": "<PERSON><PERSON>novi lozinku"}, "session": {"cancel": "Odustani", "co2": "CO₂", "date": "Razdoblje", "delete": "Izbriši", "finished": "Gotovo", "meter": "<PERSON><PERSON><PERSON><PERSON>", "meterstart": "Početno stanje mjerača", "meterstop": "Završno stanje mjerača", "odometer": "Kilometraža", "price": "Cijena", "started": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> punjenja"}, "sessions": {"avgPower": "⌀ snaga", "avgPrice": "⌀ cijena", "chargeDuration": "<PERSON><PERSON><PERSON><PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ cijena {byGroup}", "byGroupLoadpoint": "mjesta punjenja", "byGroupVehicle": "vozila", "energy": "Napunjena energija", "energyGrouped": "Izvor el. energije", "energyGroupedByGroup": "Energija {byGroup}", "energySubSolar": "{value} solarno", "energySubTotal": "{value} uk<PERSON><PERSON>", "groupedCo2ByGroup": "CO₂ količina {byGroup}", "groupedPriceByGroup": "Ukupna cijena {byGroup}", "historyCo2": "CO₂ onečišćenje", "historyCo2Sub": "{value} uk<PERSON><PERSON>", "historyPrice": "<PERSON><PERSON><PERSON><PERSON> punjenja", "historyPriceSub": "{value} uk<PERSON><PERSON>", "solar": "Udio solarne energije tokom godine", "solarByGroup": "Solarna energija {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energija (kWh)", "chargeduration": "<PERSON><PERSON><PERSON><PERSON>", "co2perkwh": "CO₂/kWh", "created": "<PERSON><PERSON><PERSON>", "finished": "Gotovo", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadpoint": "<PERSON><PERSON><PERSON> punje<PERSON>", "meterstart": "Početno stanje mjerača (kWh)", "meterstop": "Završno stanje mjerača (kWh)", "odometer": "Kilometraža (km)", "price": "Cijena", "priceperkwh": "Cijena/kWh", "solarpercentage": "Solarno (%)", "vehicle": "<PERSON><PERSON><PERSON>"}, "csvPeriod": "Preuzmi CSV za {period}", "csvTotal": "Preuzmi CSV za ukupno", "date": "Početak", "energy": "Napunjeno", "filter": {"allLoadpoints": "sva mjesta <PERSON>", "allVehicles": "sva vozila", "filter": "<PERSON><PERSON><PERSON>"}, "group": {"co2": "Onečišćenje", "grid": "Mreža", "price": "Cijena", "self": "Solar"}, "groupBy": {"loadpoint": "<PERSON><PERSON><PERSON><PERSON>", "none": "Ukupno", "vehicle": "<PERSON><PERSON><PERSON>"}, "loadpoint": "<PERSON><PERSON><PERSON> punje<PERSON>", "noData": "U ovom mjesecu nema sesija napaja<PERSON>.", "overview": "Pregled", "period": {"month": "<PERSON><PERSON><PERSON><PERSON>", "total": "Ukupno", "year": "<PERSON><PERSON>"}, "price": "T<PERSON>šak", "reallyDelete": "<PERSON><PERSON><PERSON> želiš izbrisati ovu sesiju?", "showIndividualEntries": "Prikaži pojedine zapise", "solar": "Solarno", "title": "<PERSON><PERSON><PERSON> punjenja", "total": "Ukupno", "type": {"co2": "CO₂", "price": "Cijena", "solar": "Solar"}, "vehicle": "<PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Puni z<PERSON>lon", "exit": "Izlaz iz punog zaslona", "label": "Puni z<PERSON>lon"}, "hiddenFeatures": {"label": "Eksperimentalno", "value": "Prikaži eksperimentalne značajke korisničkog sučelja."}, "language": {"auto": "Automatski", "label": "<PERSON><PERSON><PERSON>"}, "sponsorToken": {"expires": "<PERSON><PERSON><PERSON> s<PERSON> token isteče {inXDays}. {getNewToken} i obnovi ga ovdje.", "getNew": "Pruzmi svježu", "hint": "Napomena: To ćemo automatizirati u budućnosti."}, "telemetry": {"label": "Telemetrija"}, "theme": {"auto": "sustav", "dark": "tamni", "label": "<PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON>"}, "time": {"12h": "12h", "24h": "24h", "label": "Format prikaza vremena"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unit": {"km": "km", "label": "<PERSON><PERSON><PERSON>", "mi": "milje"}}, "smartCost": {"activeHours": "{active} od {total}", "activeHoursLabel": "Aktivno radno vrijeme", "applyToAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> svuda?", "batteryDescription": "Puni kućnu bateriju energijom iz mreže.", "cheapTitle": "<PERSON><PERSON><PERSON> punjenje iz mreže", "cleanTitle": "Čisto punjenje iz mreže", "co2Label": "Co₂ emisija", "co2Limit": "Ogranič<PERSON><PERSON>", "loadpointDescription": "Omogućuje privremeno brzo punjenje u solarnom modusu.", "modalTitle": "Smart Grid punjenje", "none": "bez", "priceLabel": "Cijena energije", "priceLimit": "Ograničenje cijene", "resetAction": "Ukloniti ograničenje", "resetWarning": "Ne postoji dinamična cijena energije ili izvor informacija o CO₂ nije postavljen. No, čini se kako još uvijek postoji ograničenje od {limit}. Ispraviti postavke?", "saved": "Spremljeno."}, "startupError": {"configFile": "Korištena konfiguracijska datoteka:", "configuration": "Konfiguracija", "description": "Provjeri konfiguracijsku datoteku. Ako poruka o pogreš<PERSON> ne pomogne, pogledaj {0}.", "discussions": "GitHub rasprave", "fixAndRestart": "Ispravi problem i ponovo pokreni poslužitelj.", "hint": "Napomena: <PERSON><PERSON><PERSON><PERSON> može biti da imaš ne<PERSON> (inverter, mjerač, …). Provjeri svoje mrežne veze.", "lineError": "<PERSON><PERSON><PERSON><PERSON> u {0}.", "lineErrorLink": "{0}. retku", "restartButton": "Pokreni ponovo", "title": "Pogreška pri pokretanju"}}