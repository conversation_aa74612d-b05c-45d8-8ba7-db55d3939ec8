{"batterySettings": {"batteryLevel": "Nivel de bat<PERSON>", "bufferStart": {"above": "cuando esté por encima de {soc}.", "full": "si está en {soc}.", "never": "cuando hay suficiente excedente."}, "capacity": "{energy} de {total}", "control": "Control de la batería", "discharge": "Evitar la descarga en modo rápido y la carga planificada", "disclaimerHint": "Nota:", "disclaimerText": "Estos parámetros solo afectan al modo solar. El comportamiento de carga se ajusta correspondientemente.", "gridChargeTab": "Carga de red", "legendBottomName": "Priorizar la batería de casa", "legendBottomSubline": "hasta alcanzar {soc}", "legendMiddleName": "Priorizar la carga de vehículos", "legendMiddleSubline": "cuando la batería de casa sobrepasa {soc}.", "legendTopAutostart": "Inicio automá<PERSON>", "legendTopName": "Carga de vehículos asistida por baterías", "legendTopSubline": "cuando la batería de casa sobrepasa {soc}.", "modalTitle": "Bat<PERSON><PERSON>", "usageTab": "Uso de la batería"}, "config": {"battery": {"titleAdd": "<PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON>"}, "circuits": {"description": "Se asegura que la suma de todos los puntos de carga conectados a un circuito no exceda la potencia configurada y los límites actuales. Los circuitos pueden ser anidados para construir una jerarquía", "title": "Gestión de carga"}, "control": {"description": "Usualmente los valores predeterminados están bien. Solo cámbialos si sabes lo que estás haciendo.", "descriptionInterval": "Ciclo de actualización del bucle de control en segundos. Define la frecuencia con la que evcc lee los datos del contador, ajusta la potencia de carga y actualiza la interfaz de usuario. Los intervalos cortos (< 30s) pueden causar oscilaciones y comportamientos no deseados.", "descriptionResidualPower": "Cambia el punto de funcionamiento del circuito de control. Si tienes una batería doméstica, se recomienda establecer un valor de 100 W. De esta manera, la batería tendrá una ligera prioridad sobre el uso de la red eléctrica", "labelInterval": "Intervalo de actualización", "labelResidualPower": "Fuerza residual", "title": "Controlar la conducta"}, "deviceValue": {"broker": "Broker", "bucket": "Bucket", "capacity": "Capacidad", "chargeStatus": "Estado", "chargeStatusA": "no conectado", "chargeStatusB": "conectad@", "chargeStatusC": "cargando", "chargeStatusE": "sin energía", "chargeStatusF": "error", "chargedEnergy": "<PERSON><PERSON>", "co2": "Red de CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON>", "controllable": "Controlable", "currency": "Divisa", "current": "<PERSON><PERSON><PERSON>", "currentRange": "<PERSON><PERSON><PERSON>", "enabled": "Activado", "energy": "Energía", "feedinPrice": "Precio de entrada", "gridPrice": "Precio de red", "hemsType": "Sistema", "no": "no", "odometer": "Cuentakilómetros", "org": "Organización", "phaseCurrents": "Actual L1, L2, L3", "phasePowers": "Potencia L1, L2, L3", "phaseVoltages": "Voltaje L1, L2, L3", "power": "Potencia", "powerRange": "Potencia", "range": "Alcance", "soc": "SoC", "temp": "Temperatura", "topic": "<PERSON><PERSON>", "url": "URL", "yes": "sí"}, "eebus": {"description": "Configuración que permite a evcc comunicarse con otros dispositivos EEBus", "title": "EEBus"}, "form": {"example": "Ejemplo", "optional": "opcional"}, "general": {"cancel": "<PERSON><PERSON><PERSON>", "docsLink": "Ver documentación.", "experimental": "Experimental", "hideAdvancedSettings": "Ocultar configuración avanzada", "off": "apagar", "on": "encender", "password": "Contraseña", "readFromFile": "Leer del archivo", "remove": "<PERSON><PERSON><PERSON>", "save": "Guardar", "showAdvancedSettings": "Mostrar configuración avanzada", "telemetry": "Telemetría", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "grid": {"title": "Contador de red", "titleAdd": "Añadir medidor de cuadrí<PERSON>", "titleEdit": "Editar medidor de cuadrícula"}, "hems": {"description": "Conecta evcc a otro sistema de gestión de energía del hogar", "title": "HEMS"}, "influx": {"description": "Escribe datos de cobro y otras métricas en InfluxDB. Utiliza Grafana u otras herramientas para visualizar los datos", "descriptionToken": "Consulta la documentación de InfluxDB para saber cómo crear uno. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Bucket", "labelCheckInsecure": "Permitir certificados autofirmados", "labelDatabase": "Base de datos", "labelInsecure": "Verificación de certificado", "labelOrg": "Organización", "labelPassword": "Contraseña", "labelToken": "Token de la API", "labelUrl": "URL", "labelUser": "Nombre de usuario", "title": "InfluxDB", "v1Support": "¿Necesitas soporte para InfluxDB 1.x?", "v2Support": "Volver a InfluxDB 2.x"}, "main": {"addLoadpoint": "Añadir un punto de carga", "addPvBattery": "Añadir solar o batería", "addVehicle": "Añadir un vehículo", "configured": "configurado", "edit": "editar", "title": "Configuración", "unconfigured": "no configurado", "vehicles": "<PERSON><PERSON> <PERSON>", "yaml": "Configurado en evcc.yaml. No editable en la interfaz de usuario"}, "messaging": {"description": "Recibir mensajes sobre tus sesiones de carga", "title": "Notificaciónes"}, "meter": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Bo<PERSON>r", "save": "Guardar", "template": "Fabricante", "titleChoice": "¿Qué quieres añadir?", "validateSave": "Validar y guardar"}, "modbusproxy": {"description": "Permite que varios clientes accedan a un único dispositivo Modbus", "title": "Proxy Modbus"}, "mqtt": {"authentication": "Autenticación", "description": "Conéctate a un agente MQTT para intercambiar datos con otros sistemas en tu red", "descriptionClientId": "Autor del mensaje. Si se deja en blanco, se utiliza `evcc-[rand]`", "descriptionTopic": "Déjelo en blanco para desactivar la publicación", "labelBroker": "Broker", "labelCaCert": "Certificado de servidor (CA)", "labelCheckInsecure": "Permitir certificados autofirmados", "labelClientCert": "Certificado de cliente", "labelClientId": "ID del cliente", "labelClientKey": "Clave del cliente", "labelInsecure": "Verificación de certificado", "labelPassword": "Contraseña", "labelTopic": "<PERSON><PERSON>", "labelUser": "Nombre de usuario", "publishing": "Publicación", "title": "MQTT"}, "network": {"descriptionHost": "Utiliza el sufijo .local para habilitar mDNS. Se requiere para detectar la aplicación móvil y algunos cargadores de pared OCPP", "descriptionPort": "Puerto para la interfaz web y API. Deberá actualizar la URL desde tu navegador si cambias esto", "descriptionSchema": "Solo afecta la forma en que se generan las URL. Seleccionar HTTPS no habilitará el cifrado", "labelHost": "Hostname", "labelPort": "Puerto", "labelSchema": "Esquema", "title": "Red"}, "options": {"boolean": {"no": "no", "yes": "sí"}, "endianness": {"big": "big endian", "little": "little endian"}, "schema": {"http": "HTTP (sin cifrar)", "https": "HTTPS (cifrado)"}}, "pv": {"titleAdd": "<PERSON><PERSON><PERSON> contad<PERSON> solar", "titleEdit": "<PERSON><PERSON> con<PERSON>"}, "section": {"general": "General", "grid": "Red eléctrica", "integrations": "Integraciones", "loadpoints": "Puntos de carga", "meter": "Solar y Batería", "system": "Sistema", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sponsor": {"addToken": "Ingresa el token del patrocinador", "changeToken": "Cambiar el token de patrocinador", "description": "El modelo de patrocinio nos ayuda a mantener el proyecto y a desarrollar de forma sostenible nuevas y emocionantes funciones. Como patrocinador, obtienes acceso a todas las implementaciones de cargadores", "descriptionToken": "Obtienes el token desde {url}. También ofrecemos un token de prueba para probar", "error": "El token de patrocinador no es válido", "labelToken": "Token del patrocinador", "title": "Patrocinio"}, "system": {"logs": "Registros", "restart": "Reiniciar", "restartRequiredDescription": "Por favor, reinicie para aplicar los cambios", "restartRequiredMessage": "La configuración cambió", "restartingDescription": "<PERSON>spere por favor…", "restartingMessage": "Reiniciando evcc"}, "tariffs": {"description": "Define tus tarifas energéticas para calcular los costes de tus sesiones de carga.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Se muestra en la pantalla principal y en la pestaña del navegador", "label": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "validation": {"failed": "fallido", "label": "Estado", "running": "validando...", "success": "exitoso", "unknown": "desconocido", "validate": "validar"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Bo<PERSON>r", "generic": "Otras integraciones", "online": "Vehículos con API en línea", "save": "Guardar", "scooter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "template": "Fabricante", "titleAdd": "Añadir un vehículo", "titleEdit": "Editar el vehículo", "validateSave": "Validar y guardar"}}, "footer": {"community": {"greenEnergy": "Energía Solar", "greenEnergySub1": "<PERSON><PERSON> con evcc", "greenEnergySub2": "desde octubre de 2022", "greenShare": "Parte solar", "greenShareSub1": "energía proporcionada por", "greenShareSub2": "solar y almacenamiento de baterías", "power": "potencia de carga", "powerSub1": "{activeClients} de {totalClients} usuarios", "powerSub2": "cargando...", "tabTitle": "Comunidad en vivo"}, "savings": {"co2Saved": "{value} guardado", "co2Title": "Emisiones de CO₂", "configurePriceCo2": "Aprende a configurar los datos de los precios y el CO₂", "footerLong": "{percent} Autoconsumo", "footerShort": "{percent} Sol", "modalTitle": "Evaluación de la energía de carga", "moneySaved": "{value} guardado", "percentGrid": "{grid} kWh red", "percentSelf": "{self} kWh solar", "percentTitle": "Energía solar", "period": {"30d": "últimos 30 días", "365d": "<PERSON><PERSON><PERSON> a<PERSON>", "thisYear": "este año", "total": "desde siempre"}, "periodLabel": "Periodo:", "priceTitle": "Precio de energía", "referenceGrid": "red", "referenceLabel": "Datos de referencia:", "tabTitle": "<PERSON><PERSON> datos"}, "sponsor": {"becomeSponsor": "Conviértete en patrocinador", "becomeSponsorExtended": "Apóyanos directamente para obtener stickers", "confetti": "¿Quieres un poco de confeti?", "confettiPromise": "También hay pegatinas y confeti digital", "sticker": "...o pegatinas de evcc?", "supportUs": "Nuestra misión es hacer que la carga solar sea la norma. Ayuda a evcc pagando lo que vale para ti", "thanks": "¡Gracias por tu patrocinio, {sponsor}! Esto nos ayudará a seguir desarrollando evcc.", "titleNoSponsor": "Ap<PERSON>yan<PERSON>", "titleSponsor": "<PERSON>res un seguidor", "titleTrial": "<PERSON><PERSON>", "titleVictron": "Patrocinado por Victron Energy", "trial": "Estas en modo de prueba y puedes utilizar todas las funciones. Considera apoyar el proyecto", "victron": "Utiliza evcc en el hardware de Victron Energy y tiene acceso a todas las funciones"}, "telemetry": {"optIn": "También me gustaría contribuir con mis datos.", "optInMoreDetails": "<PERSON><PERSON> detalles {0}.", "optInMoreDetailsLink": "aquí", "optInSponsorship": "Se requiere patrocinio."}, "version": {"availableLong": "nueva versión disponible", "modalCancel": "<PERSON><PERSON><PERSON>", "modalDownload": "<PERSON><PERSON><PERSON>", "modalInstalledVersion": "Versión instalada", "modalNoReleaseNotes": "No hay notas de lanzamiento disponibles. Puedes encontrar más información sobre la nueva versión aquí:", "modalTitle": "Nueva versión disponible", "modalUpdate": "Instalar", "modalUpdateNow": "<PERSON><PERSON><PERSON> ahora", "modalUpdateStarted": "Después de la actualización, evcc se reiniciará.", "modalUpdateStatusStart": "Instalación iniciada:"}}, "header": {"about": "Sobre", "blog": "Blog", "docs": "Documentación", "github": "GitHub", "login": "Registro de vehículos", "logout": "<PERSON><PERSON><PERSON>", "nativeSettings": "Cambiar el servidor", "needHelp": "¿Necesitas ayuda?", "sessions": "Sesiones de carga"}, "help": {"discussionsButton": "Debates en GitHub", "documentationButton": "Documentación", "issueButton": "Reportar un error", "issueDescription": "¿Encontraste un comportamiento extraño o incorrecto?", "logsButton": "Ver los registros", "logsDescription": "Compruebe los registros en busca de errores", "modalTitle": "¿Necesitas ayuda?", "primaryActions": "Si algo no funciona como debería, puedes obtener ayuda aqui.", "restart": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "¡Sí, reinicia!", "description": "En circunstancias normales no debería ser necesario reiniciar. Por favor, considera la posibilidad de presentar un error si necesita reiniciar evcc de forma regular.", "disclaimer": "Nota: evcc terminará y dependerá del sistema operativo para reiniciar el servicio", "modalTitle": "¿Se<PERSON>ro que quieres volver a empezar?"}, "restartButton": "Reiniciar", "restartDescription": "¿Has probado de apagar y volver a encender", "secondaryActions": "¿Sigues sin poder resolver tu problema? Aquí tienes otras opciones más contundentes."}, "log": {"areaLabel": "Filtrar por zona", "areas": "Todas las areas", "download": "<PERSON><PERSON><PERSON> registro completo", "levelLabel": "Filtrar por nivel de registro", "nAreas": "{count} <PERSON><PERSON><PERSON>", "noResults": "No hay entradas en el registro coincidentes", "search": "Buscar", "selectAll": "sele<PERSON><PERSON>r todo", "showAll": "Mostrar todas las entradas", "title": "Registros", "update": "Actualización automática"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON>", "error": "Error en el inicio de sesion: ", "iframeHint": "Abrir evcc en una nueva pestaña", "iframeIssue": "Tu contraseña es correcta, pero tu navegador parece haber eliminado la cookie de autenticación. Esto puede suceder si ejecutas evcc en un iframe a través de HTTP", "invalid": "La contraseña no es válida.", "login": "Registro", "password": "Contraseña", "reset": "¿Restablecer la contraseña?", "title": "Autenticación"}, "main": {"chargingPlan": {"active": "Activo", "addRepeatingPlan": "Añadir plan recurrente", "arrivalTab": "Llegadas", "day": "Día", "departureTab": "Salidas", "goal": "Objetivo de la carga", "modalTitle": "Plan de carga", "none": "ning<PERSON>", "planNumber": "Plan {number}", "remove": "<PERSON><PERSON><PERSON>", "repeating": "repitiendo", "repeatingPlans": "Planes recurrentes", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "time": "Tiempo", "title": "Plan", "titleMinSoc": "Carga mínima", "titleTargetCharge": "Partida", "unsavedChanges": "Hay cambios sin guardar. ¿Aplicar ahora?", "update": "Aplicar", "weekdays": "Días"}, "energyflow": {"battery": "Bater<PERSON>", "batteryCharge": "Cargar la batería", "batteryDischarge": "Descargar la batería", "batteryHold": "Batería (bloqueada)", "batteryTooltip": "{energy} de {total} ({soc})", "gridImport": "Consumo de red", "homePower": "Consu<PERSON>", "loadpoints": "Punto de carga | Punto de carga | {count} Puntos de carga", "noEnergy": "Sin lecturas", "pvExport": "Excedente", "pvProduction": "Producción", "selfConsumption": "Autoconsumo"}, "heatingStatus": {"charging": "Calefacción...", "waitForVehicle": "Listo. Esperando a que se caliente..."}, "loadpoint": {"avgPrice": "Precio ⌀", "charged": "<PERSON><PERSON>", "co2": "CO₂ ⌀", "duration": "Duración", "fallbackName": "Punto de carga", "power": "Potencia", "price": "Precio", "remaining": "Tiempo restante", "remoteDisabledHard": "{source}: desactivado", "remoteDisabledSoft": "{source}: Carga adaptativa desactivada", "solar": "Solar"}, "loadpointSettings": {"batteryBoost": {"description": "Carga rápida desde la batería de casa", "label": "Aumento de la batería", "mode": "Solo disponible en modo solar y min+solar.", "once": "Boost activo para esta sesión de carga"}, "batteryUsage": "Bat<PERSON><PERSON>", "currents": "Corriente de carga", "default": "defecto", "disclaimerHint": "Nota:", "limitSoc": {"description": "Límite de carga que se utiliza cuando este vehículo está conectado", "label": "Límite predeterminado"}, "maxCurrent": {"label": "Corriente de carga máxima"}, "minCurrent": {"label": "Corriente de carga mínima"}, "minSoc": {"description": "El vehículo se carga “rápido” al {0} en modo energía solar. Luego continúa con el excedente de la energía solar. Útil para asegurar una autonomía mínima incluso en los días más oscuros", "label": "Carga de min. %"}, "onlyForSocBasedCharging": "Estas opciones sólo están disponibles para vehículos con nivel de carga conocido", "phasesConfigured": {"label": "Fases", "no1p3pSupport": "¿Cómo está conectado tu cargador?", "phases_0": "Cambio automático", "phases_1": "monofásica", "phases_1_hint": "({min} a {max})", "phases_3": "trifásica", "phases_3_hint": "({min} a {max})"}, "smartCostCheap": "Carga barata desde la red", "smartCostClean": "Carga desde una red ecológica", "title": "Configuraciones {0}", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mode": {"minpv": "Min+Sol", "now": "<PERSON><PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON><PERSON>", "pv": "Solar", "smart": "Inteligente"}, "provider": {"login": "iniciar se<PERSON><PERSON>", "logout": "cer<PERSON> se<PERSON>"}, "targetCharge": {"activate": "Activar", "co2Limit": "Límite de CO₂ del {co2}", "costLimitIgnore": "El {limit} configurado será ignorado durante este periodo.", "currentPlan": "Plan activo", "descriptionEnergy": "¿Hasta cuándo deben haberse cargado {targetEnergy} al vehículo?", "descriptionSoc": "¿Hasta cuándo debe haberse cargarse el vehículo al {targetSoc}?", "inactiveLabel": "Tiempo objetivo", "nextPlan": "Próximo plan", "notReachableInTime": "El objetivo se alcanzará {overrun} más tarde", "onlyInPvMode": "El plan de carga sólo funciona en modo solar", "planDuration": "Tiempo de carga", "planPeriodLabel": "<PERSON><PERSON><PERSON>", "planPeriodValue": "de {start} a {end}", "planUnknown": "aún no se sabe", "preview": "Vista previa", "priceLimit": "límite en el precio de {price}", "remove": "retirar", "setTargetTime": "niguno", "targetIsAboveLimit": "El límite de carga configurado de {limit} se ignorará durante este periodo", "targetIsAboveVehicleLimit": "El límite del vehículo está por debajo del objetivo de carga", "targetIsInThePast": "Elige un tiempo en el futuro, <PERSON>.", "targetIsTooFarInTheFuture": "Ajustaremos el plan en cuanto sepamos más sobre el futuro.", "title": "Tiempo objetivo", "today": "hoy", "tomorrow": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "vehicleCapacityDocs": "Aprende a configurarlo", "vehicleCapacityRequired": "La capacidad de la batería del vehículo es necesaria para estimar el tiempo de carga"}, "targetChargePlan": {"chargeDuration": "Tiempo de carga", "co2Label": "Emisión de CO₂ ⌀", "priceLabel": "Precio de la energía", "timeRange": "{day} {range} hora", "unknownPrice": "aún desconocido"}, "targetEnergy": {"label": "Objetivo de carga", "noLimit": "ning<PERSON>"}, "vehicle": {"addVehicle": "Añadir un vehículo", "changeVehicle": "Cambia de vehículo", "detectionActive": "Reconociendo vehículo ...", "fallbackName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreActions": "Más acciones", "none": "<PERSON><PERSON><PERSON>", "notReachable": "No se pudo acceder al vehículo. Intente reiniciar evcc", "targetSoc": "Objetivo de carga", "temp": "T.<PERSON>", "tempLimit": "Temperatura límite", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>o", "vehicleSoc": "<PERSON>vel de carga"}, "vehicleStatus": {"awaitingAuthorization": "Esperando autorización", "batteryBoost": "Refuerzo de batería activo", "charging": "Cargando ...", "cheapEnergyCharging": "Energía barata disponible.", "cheapEnergyNextStart": "Energía barata durante {duration}", "cheapEnergySet": "Límite de precio establecido", "cleanEnergyCharging": "Energía limpia disponible.", "cleanEnergyNextStart": "Energía limpia durante {duration}", "cleanEnergySet": "Límite de CO₂ establecido.", "climating": "Preacondicionamiento detectado.", "connected": "Conectado.", "disconnectRequired": "Sesión terminada. <PERSON>r favor, vuelve a conectarte", "disconnected": "Desconectado.", "finished": "Finalizado.", "minCharge": "Carga mínima hasta {soc}.", "pvDisable": "No hay suficientes excedentes. Haremos una pausa pronto", "pvEnable": "Excedente disponible. La carga se reanudará en breve.", "scale1p": "Pronto se reducirá a carga monofásica", "scale3p": "Pronto pasaremos a carga trifásica", "targetChargeActive": "Plan de carga activo. Finalización estimada en {duration}", "targetChargePlanned": "El plan de carga comienza en {duration}", "targetChargeWaitForVehicle": "Plan de carga listo. Esperando vehículo...", "vehicleLimit": "Límite de vehículos", "vehicleLimitReached": "Límite del vehículos alcanzado", "waitForVehicle": "Listo para cargar. Esperando vehículo.", "welcome": "Breve carga inicial para confirmar la conexión."}, "vehicles": "Estacionamiento"}, "notifications": {"dismissAll": "Eliminar notificaciones", "logs": "Ver registros completos", "modalTitle": "Notificaciónes"}, "offline": {"configurationError": "Error durante el inicio. Verifica tu configuración y reinicia", "message": "No conectado con servidor.", "restart": "<PERSON><PERSON><PERSON>", "restartNeeded": "Requerido para aplicar cambios", "restarting": "El servidor volverá a estar disponible pronto"}, "passwordModal": {"description": "Establezca una contraseña para proteger la configuración. La vista principal permanece accesible sin iniciar sesión", "empty": "La contraseña no debe estar vacía", "error": "Error: ", "labelCurrent": "Contraseña actual", "labelNew": "Nueva contraseña", "labelRepeat": "Repita la contraseña", "newPassword": "<PERSON><PERSON><PERSON> con<PERSON>", "noMatch": "Las contraseñas no coinciden", "titleNew": "Establecer contraseña de administrador", "titleUpdate": "Cambiar contraseña de administrador", "updatePassword": "Cambiar la contraseña"}, "session": {"cancel": "<PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON>", "delete": "Bo<PERSON>r", "finished": "Finalizó", "meter": "<PERSON><PERSON><PERSON>", "meterstart": "Iniciar el medidor", "meterstop": "Parar el medidor", "odometer": "Kilometraje", "price": "Precio", "started": "<PERSON><PERSON><PERSON>", "title": "Cargando la sesión"}, "sessions": {"avgPower": "⌀ Potencia", "avgPrice": "Precio ⌀", "chargeDuration": "Duración", "chartTitle": {"avgCo2ByGroup": "CO₂ ⌀ {byGroup}", "avgPriceByGroup": "Precio ⌀ {byGroup}", "byGroupLoadpoint": "por punto de carga", "byGroupVehicle": "por vehí<PERSON>lo", "energy": "Energía cargada", "energyGrouped": "“Energía solar versus energía de red”", "energyGroupedByGroup": "Energía {byGroup}", "energySubSolar": "{value} solar", "energySubTotal": "{value} total", "groupedCo2ByGroup": "CO₂-Importe {byGroup}", "groupedPriceByGroup": "Coste total {byGroup}", "historyCo2": "Emisiones de CO₂", "historyCo2Sub": "{value} total", "historyPrice": "Precio de carga", "historyPriceSub": "{value} total", "solar": "Cuota solar anual", "solarByGroup": "<PERSON><PERSON><PERSON> {byGroup}"}, "co2": "CO₂ ⌀", "csv": {"chargedenergy": "Energía (kWh)", "chargeduration": "«Duración»", "co2perkwh": "CO₂/kWh", "created": "Hora de inicio", "finished": "Hora de finalización", "identifier": "Identificador", "loadpoint": "Punto de carga", "meterstart": "Inicio de metro (kWh)", "meterstop": "Parón de metro (kWh)", "odometer": "Kilometraje (km)", "price": "Precio", "priceperkwh": "Precio/kWh", "solarpercentage": "Solar (%)", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "csvPeriod": "<PERSON><PERSON><PERSON> el {period} CSV", "csvTotal": "Descargar todos los CSV", "date": "Comenzar", "energy": "<PERSON><PERSON>", "filter": {"allLoadpoints": "todos los puntos de recarga", "allVehicles": "todos los vehiculos", "filter": "Filtrar"}, "group": {"co2": "Emisiones", "grid": "Red eléctrica", "price": "Precio", "self": "Energía Solar"}, "groupBy": {"loadpoint": "Punto de carga", "none": "Total", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loadpoint": "Punto de carga", "noData": "No hay sesiones de carga este mes.", "overview": "Resumen", "period": {"month": "<PERSON><PERSON>", "total": "Total", "year": "<PERSON><PERSON>"}, "price": "Precio", "reallyDelete": "¿De verdad quieres eliminar esta sesión?", "showIndividualEntries": "Mostrar sesiones individuales", "solar": "Solar", "title": "Sesiones de carga", "total": "Total", "type": {"co2": "CO₂", "price": "Precio", "solar": "Energía Solar"}, "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"fullscreen": {"enter": "Iniciar a pantalla completa", "exit": "Salir de la pantalla completa", "label": "Pantalla completa"}, "hiddenFeatures": {"label": "<PERSON> prue<PERSON>", "value": "Mostrar las características experimentales de la interfaz de usuario."}, "language": {"auto": "Automático", "label": "Idioma"}, "sponsorToken": {"expires": "Tu token de patrocinador expira en {inXDays}. {getNewToken} y puedes renovarlo aquí.", "getNew": "Coge uno nuevo", "hint": "Nota: automatizaremos esto en el futuro."}, "telemetry": {"label": "Telemetría"}, "theme": {"auto": "sistema", "dark": "oscuro", "label": "<PERSON><PERSON>", "light": "claro"}, "title": "Interfaz de usuario", "unit": {"km": "km", "label": "Unidades", "mi": "millas"}}, "smartCost": {"activeHours": "{active} de {total}", "activeHoursLabel": "Horas funcionando", "applyToAll": "¿Aplicar en todas partes?", "batteryDescription": "Carga la batería doméstica con la red eléctrica", "cheapTitle": "Carga barata desde la red", "cleanTitle": "Carga desde una red ecológica", "co2Label": "Emisiones de CO₂", "co2Limit": "Límite de CO₂", "loadpointDescription": "Habilita temporalmente la carga rápida en modo solar", "modalTitle": "Red inteligente de carga", "none": "ning<PERSON>", "priceLabel": "Precio de la energía", "priceLimit": "Límite de precio", "saved": "Guardado."}, "startupError": {"configFile": "Archivo de configuración utilizado:", "configuration": "Configuración", "description": "Por favor revisa tu archivo de configuración. Si el mensaje de error no te ayuda, busca una solución en nuestro {0}.", "discussions": "<PERSON><PERSON><PERSON> GitHub", "fixAndRestart": "Por favor, soluciona el problema y reinicia el servidor.", "hint": "Nota: Tam<PERSON>én puede ser que tengas un aparato averiado (inversor, contador, ...). Comprueba sus conexiones de red.", "lineError": "Error en {0}.", "lineErrorLink": "Línea {0}", "restartButton": "Reiniciar", "title": "Error al iniciar"}}