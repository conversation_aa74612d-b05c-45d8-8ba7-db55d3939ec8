{"batterySettings": {"batteryLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "bufferStart": {"above": "amikor {soc} felett van.", "full": "amikor {soc}-on van.", "never": "csak ha van el<PERSON>g tö<PERSON>."}, "capacity": "{energy} / {total}", "control": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>", "discharge": "Kisütés megakadályoza gyorstöltési és tervezett töltési üzemmódban.", "disclaimerHint": "Megjegyzés:", "disclaimerText": "Ezek a beállítások csak a szolár üzemmódot érintik. A töltés módja ennek megfelelően kerül beállításra.", "gridChargeTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendBottomName": "Otthoni akkumulátoros töltés priorizálása", "legendBottomSubline": "ameddig eléri {soc}-ot.", "legendMiddleName": "Jármű töltésének priorizálása", "legendMiddleSubline": "amikor az otthoni akku<PERSON> {soc} felett van.", "legendTopAutostart": "Automatikusan indul", "legendTopName": "Energiat<PERSON><PERSON><PERSON><PERSON> t<PERSON>ott töltés", "legendTopSubline": "amikor az otthoni akku<PERSON> {soc} felett van.", "modalTitle": "<PERSON>tt<PERSON><PERSON>", "usageTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "config": {"aux": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, amely a rendelkezésre álló többlet alapján állítja be a fogyasztását (például intelligens vízmelegítők). Az evcc arra számít, hogy ez az eszköz szükség esetén csökkenti az energiafogyasztását.", "titleAdd": "Önszab<PERSON><PERSON><PERSON><PERSON> hozzáadása", "titleEdit": "Önszab<PERSON><PERSON><PERSON><PERSON>ó szerkesztése"}, "battery": {"titleAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "titleEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "charge": {"titleAdd": "Töltő Fogyasztásmérő hozzáadása", "titleEdit": "Töltő Fogyasztásmérő szerkesztése"}, "charger": {"chargers": "EV töltők", "generic": "Általános integrációk", "heatingdevices": "Fűtőberendezések", "ocppHelp": "Másolja be ezt a címet a töltő konfigurációjába.", "ocppLabel": "OCPP-Server URL", "switchsockets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template": "G<PERSON><PERSON><PERSON><PERSON>", "titleAdd": {"charging": "Töltő Hozzáadása"}, "titleEdit": {"charging": "Töltő Szerkesztése"}}, "circuits": {"description": "Gondoskodik arról, hogy az áramkörhöz csatlakoztatott összes terhelési pont összege ne haladja meg a beállított teljesítmény- és áramkorlátokat. Az áramkörök egymásba ágyazhatók a hierarchia felépítéséhez.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "control": {"description": "Általában az alapértelmezett értékek működőképesek. Csak akkor változtasd meg őket, ha tudod, hogy mit csinálsz.", "descriptionInterval": "A vezérlőkör frissítési ciklusa másodpercekben. <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az evcc milyen gyakran olvassa ki a mérőadatokat, állítsa be a töltési teljesítményt és frissítse a felhasználói felületet. A rövid <PERSON> (<30 s) oszcillációt és nem kívánt viselkedést okozhatnak.", "descriptionResidualPower": "Eltolja a vezérlőkör működési pontját. Ha otthoni akkumulátorral rendelkezik, javasoljuk, hogy 100 W-os értéket állítson be. Így az akkumulátor kismértékben élvez prioritást a hálózati használattal szemben.", "labelInterval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelResidualPower": "Tartalék energia", "title": "Vezérl<PERSON><PERSON> mód"}, "deviceValue": {"amount": "Mennyiség", "broker": "<PERSON><PERSON><PERSON><PERSON>", "bucket": "Vödör", "capacity": "Kapacitás", "chargeStatus": "<PERSON><PERSON><PERSON><PERSON>", "chargeStatusA": "ninc<PERSON>", "chargeStatusB": "csatlakoztatva", "chargeStatusC": "t<PERSON>ltés", "chargeStatusE": "nincs <PERSON>", "chargeStatusF": "hiba", "chargedEnergy": "Töltve", "co2": "Hálózat CO₂", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllable": "Vezérelhető", "currency": "Valuta", "current": "<PERSON><PERSON><PERSON><PERSON>", "currentRange": "<PERSON><PERSON>", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "energy": "Energia", "feedinPrice": "Kötelező átvételi ár", "gridPrice": "Villamosenergia ára", "heaterTempLimit": "Fűtés limit", "hemsType": "Rendszer", "identifier": "RFID-Azonosító", "no": "nem", "odometer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "org": "Szervezet", "phaseCurrents": "<PERSON>ram L1, L2, L3", "phasePowers": "Teljesítmény L1, L2, L3", "phaseVoltages": "Feszültség L1, L2, L3", "phases1p3p": "Fázis v<PERSON>", "power": "Teljesítmény", "powerRange": "Teljesítmény", "range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singlePhase": "Egyfázisú", "soc": "Töltöttség", "solarForecast": "Napsütés előrejelzés", "temp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "topic": "<PERSON><PERSON><PERSON>", "url": "URL", "vehicleLimitSoc": "Jármű limit", "yes": "igen"}, "deviceValueChargeStatus": {"A": "A (nincs <PERSON>)", "B": "B (csatlakoztatva)", "C": "C (töltés)"}, "devices": {"auxMeter": "<PERSON><PERSON>", "batteryStorage": "Akkumulátoros tároló", "solarSystem": "Naperőmű"}, "editor": {"loading": "YAML szerkesztő betöltése…"}, "eebus": {"description": "Konfiguráció ami engedélyezi az evcc-nek, hogy kommunikáljon más EEBus eszközökkel.", "title": "EEBus"}, "ext": {"description": "Használható töltés menedzsmentre vagy statisztikai célokra.", "titleAdd": "<PERSON><PERSON>ls<PERSON> Mér<PERSON>", "titleEdit": "Külső Mérő <PERSON>té<PERSON>"}, "form": {"danger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deprecated": "elavult", "example": "<PERSON><PERSON><PERSON>", "optional": "opcionális"}, "general": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "customHelp": "Hozz létre egy felhasz<PERSON><PERSON>ó <PERSON>l definiált eszközt az evcc bővítményrendszerével.", "customOption": "Fel<PERSON><PERSON><PERSON><PERSON><PERSON> definiált eszköz", "delete": "Törlés", "docsLink": "Dokumentáció megtekintése", "experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideAdvancedSettings": "Speci<PERSON><PERSON> beállítások elrejtése", "off": "ki", "on": "be", "password": "Je<PERSON><PERSON><PERSON>", "readFromFile": "Fájlból olvasás", "remove": "Eltávolítás", "save": "Men<PERSON>s", "showAdvancedSettings": "Speciális beállítások megjelenítése", "telemetry": "Telemetria", "templateLoading": "Betöltés...", "title": "Cím", "validateSave": "Ellenőrzés & mentés"}, "grid": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "titleAdd": "Hálózati <PERSON>", "titleEdit": "Hálózati Mér<PERSON>keszté<PERSON>"}, "hems": {"description": "Az evcc csatlakoztatása más otthoni energia menedzsment rendszerhez.", "title": "HEMS"}, "icon": {"change": "változtatás"}, "influx": {"description": "Lementi a töltési adatokat és egyéb metrikákat az InfluxDB-be. Használd a <PERSON>ana-t vagy egyéb eszközöket az adatok vizualizálásához.", "descriptionToken": "Ellenőrizd az InfluxDB dokumentációját, ha szeretnél létrehozni egyet. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Bucket)", "labelCheckInsecure": "Önaláírt tanúsítványok engedélyezése", "labelDatabase": "Adatb<PERSON><PERSON><PERSON>", "labelInsecure": "Tanúsítvány hitelesítés", "labelOrg": "Szervezet", "labelPassword": "Je<PERSON><PERSON><PERSON>", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "InfluxDB", "v1Support": "Szükséged van támogatásra az InfluxDB 1.x verzióhoz?", "v2Support": "Vissza az InfluxDB 2.x verzióhoz"}, "loadpoint": {"addCharger": {"charging": "Töltő hozzáadása"}, "addMeter": "Dedikált töltő fogyasztásmérő hozzáadása", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "chargerError": {"charging": "Töltő konfigurálása szükséges."}, "chargerLabel": {"charging": "Töltő"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "6 és 16 A közötti áramtartomány lesz hasz<PERSON>.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "6 és 32 A közötti áramtartomány lesz hasz<PERSON>.", "chargerPowerCustom": "<PERSON><PERSON><PERSON><PERSON>", "chargerPowerCustomHelp": "Definiáljon egy egyedi <PERSON>.", "chargerTypeLabel": "Töltő típusa", "chargingTitle": "Töltés", "circuitHelp": "Terheléskezelési hozzárendelés a teljesítmény- és áramkorlátok túllépésének biztosítására.", "circuitLabel": "Áramkör", "circuitUnassigned": "nincs ho<PERSON>", "defaultModeHelp": {"charging": "T<PERSON><PERSON><PERSON><PERSON> mó<PERSON>, amikor csatlakoztatja a járművet."}, "defaultModeHelpKeep": "A legutoljára kiválasztott töltési mód lesz aktív.", "defaultModeLabel": "Alapértelmezett mód", "delete": "Törlés", "electricalSubtitle": "<PERSON> kétséged van, kérdezd meg a villanyszerelődet.", "electricalTitle": "Elektromos", "energyMeterHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ha a töltő nem rendelkezik egy integrálttal.", "energyMeterLabel": "Fogyasztásmérő", "estimateLabel": "Interpolálja a töltési szintet az API frissítések között", "maxCurrentHelp": "Nagyobbnak kell lennie mint a minimális áram.", "maxCurrentLabel": "Maximum áram", "minCurrentHelp": "Csak akkor menj 6 A <PERSON>á, ha tudod, hogy mit c<PERSON><PERSON><PERSON><PERSON>.", "minCurrentLabel": "Minimum áram", "noVehicles": "<PERSON><PERSON>s j<PERSON> konfigurálva.", "phases1p": "1-<PERSON><PERSON><PERSON><PERSON>", "phases3p": "3-<PERSON><PERSON><PERSON><PERSON>", "phasesAutomatic": "Automatikus <PERSON>", "phasesAutomaticHelp": "A töltőd támogatja az automatikus váltást az 1- és a 3 fázisú töltés között. A főképernyőn lehet állítani a fázisokat töltés közben.", "phasesHelp": "Fázisok száma csatlakoztatva a töltőhöz.", "phasesLabel": "Fázisok", "pollIntervalDanger": "A jármű rendszeres lekérdezése lemerítheti a jármű akkumulátorát. Egyes járműgyártók ebben az esetben aktívan megakadályozhatják a töltést. Nem ajánlott! Csak akkor használja, ha tisztában van a kockázatokkal.", "pollIntervalHelp": "Idő a jármű API frissítései között. A rövid intervallumok meríthetik a jármű akkumulátorát.", "pollIntervalLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pollModeAlways": "<PERSON><PERSON><PERSON>", "pollModeAlwaysHelp": "Mindig kér<PERSON>n <PERSON>frissítéseket rendszeres időközönként.", "pollModeCharging": "t<PERSON>ltés", "pollModeChargingHelp": "Csak töltés közben kérjen frissítést a jármű állapotáról.", "pollModeConnected": "csatlakoztatva", "pollModeConnectedHelp": "Rendszeres időközönként frissítse a jármű állapotát, amikor csatlakoztatva van.", "pollModeLabel": "Frissítés viselk<PERSON>e", "priorityHelp": "A magasabb prioritású töltőpontok lesznek előnyben részesítve a napelemes többletből.", "priorityLabel": "Prioritás", "save": "Men<PERSON>s", "showAllSettings": "Összes beállítás megjelenítése", "solarBehaviorCustomHelp": "Definiálja a saját engedélyezési és letiltási küszöbértékeit és késleltetéseit.", "solarBehaviorDefaultHelp": "Csak napelemes többlettel való töltés. Indítás {enableDelay} többlet esetén. Megállítás, ha nincs elég többlet {disableDelay}.\"", "solarBehaviorLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "solarModeCustom": "e<PERSON><PERSON><PERSON>", "solarModeMaximum": "maximum szolár", "thresholdDisableDelayLabel": "Késleletetés letiltása", "thresholdDisableHelpInvalid": "Kérlek használj pozitív értéket.", "thresholdDisableHelpPositive": "Töltés meg<PERSON>llí<PERSON>, amikor több mint {power} lett a hálózatból vételezve {delay} ideig.", "thresholdDisableHelpZero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a minimum töltési teljesítmény nem biztosítható {delay} ideig.", "thresholdDisableLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thresholdEnableDelayLabel": "Késleltetés engedélyezése", "thresholdEnableHelpInvalid": "<PERSON><PERSON><PERSON>k használj negatív értéket.", "thresholdEnableHelpNegative": "<PERSON><PERSON><PERSON><PERSON> elindí<PERSON>, amikor {surplus} többlet elérhető {delay} ideig.\"", "thresholdEnableHelpZero": "Indítás, amikor a minimum töltési teljesítmény többlet elérhető {delay} ideig.", "thresholdEnableLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> engedélyezése", "titleAdd": "Töltőpont Hozzáadása", "titleEdit": "Töltőpont Szerkesztése", "titleExample": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ibe<PERSON>ó, stb.", "titleLabel": "Megnevezés", "vehicleAutoDetection": "auto felismerés", "vehicleHelpAutoDetection": "Automatikusan kiválasztja a legvalószínűbb járművet. Kézi felülírás le<PERSON>é<PERSON>.", "vehicleHelpDefault": "<PERSON><PERSON>, hogy ez a jármű itt töltődik. Az automatikus felismerés letiltva. Kézi felülí<PERSON>ás le<PERSON>é<PERSON>.", "vehicleLabel": "Alapértelmezett <PERSON>", "vehiclesTitle": "Járművek"}, "main": {"addAdditional": "Tová<PERSON>i mé<PERSON> ho<PERSON>", "addGrid": "Hálózati fogyasztásmérő hozzáadása", "addLoadpoint": "Töltőpont hozzáadása", "addPvBattery": "Na<PERSON><PERSON> vagy energia<PERSON><PERSON><PERSON><PERSON>", "addTariffs": "<PERSON><PERSON><PERSON>", "addVehicle": "Jármű hozzáadása", "configured": "konfi<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "szerkesztés", "loadpointRequired": "Legalább egy töltőpontnak konfigurálva kell lennie.", "name": "Név", "title": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unconfigured": "nincs k<PERSON><PERSON><PERSON><PERSON>", "vehicles": "J<PERSON>rm<PERSON>veim", "yaml": "Eszköz az evcc.yaml fájlból nem szerkeszthető."}, "messaging": {"description": "Értesítési üzenetek beállítása a töltési folyamatokról.", "title": "Értesítések"}, "meter": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Törlés", "generic": "Általános integrációk", "option": {"aux": "Önszab<PERSON><PERSON><PERSON><PERSON> hozzáadása", "battery": "Akkumulátoros mérő hozzáadása", "ext": "Külső mérő hozz<PERSON>ad<PERSON>a", "pv": "Na<PERSON>em m<PERSON> ho<PERSON>"}, "save": "Men<PERSON>s", "specific": "Specifikus integrá<PERSON>k", "template": "G<PERSON><PERSON><PERSON><PERSON>", "titleChoice": "<PERSON>t s<PERSON><PERSON><PERSON><PERSON>?", "validateSave": "Ellenőrzés & mentés"}, "modbus": {"baudrate": "Baud rate", "comset": "ComSet", "connection": "Modbus ka<PERSON>t", "connectionHintSerial": "Az eszköz közvetlenül van csatlakoztatva az evcc-hez RS485-ös interfészen keresztül.", "connectionHintTcpip": "Az eszköz címezhető az evcc-ből LAN/Wifi-n keresztül.", "connectionValueSerial": "Soros / USB", "connectionValueTcpip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "device": "Eszköz neve", "deviceHint": "Példa: /dev/ttyUSB0", "host": "IP cím vagy <PERSON>v", "hostHint": "Példa: *********", "id": "Modbus ID", "port": "Port", "protocol": "Modbus protokoll", "protocolHintRtu": "Csatlakozás RS485-Ethernet adapteren keresztül protokollfordítás nélkül.", "protocolHintTcp": "Az eszköz natív LAN/Wifi támogatással rendelkezik, vagy RS485-Ethernet adapteren keresztül csatlakozik protokollfordítással.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Több kliens csatlakozásának engedélyezése egy Modbus eszközhöz.", "title": "Modbus Proxy"}, "mqtt": {"authentication": "Hitelesítés", "description": "Csatlakozás egy MQTT brókerhez az adatok cseréjéhez egy másik rendszerrel a hálózaton.", "descriptionClientId": "Az üzenetek szerzője. Ha üres, akkor az `evcc-[rand]` lesz <PERSON>.", "descriptionTopic": "Hagyd üresen a publikálás letiltásához.", "labelBroker": "<PERSON><PERSON><PERSON><PERSON>", "labelCaCert": "<PERSON><PERSON><PERSON> tan<PERSON> (CA)", "labelCheckInsecure": "Önaláírt tanúsítványok engedélyezése", "labelClientCert": "<PERSON><PERSON><PERSON>", "labelClientId": "Kliens ID", "labelClientKey": "<PERSON><PERSON><PERSON> k<PERSON>", "labelInsecure": "Tanúsítvány hitelesítés", "labelPassword": "Je<PERSON><PERSON><PERSON>", "labelTopic": "<PERSON><PERSON><PERSON>", "labelUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publishing": "Közzététel", "title": "MQTT"}, "network": {"descriptionHost": "Használd a .local utótagot az mDNS engedélyezéséhez. A mobilalkalmazás és egyes OCPP-töltők felfedezése szempontjából releváns.", "descriptionPort": "Port a webes felülethez és az API-hoz. Frissítened kell a böngésződ URL-jét, ha ezt megváltoztatod.", "descriptionSchema": "Csak az URL-ek létrehozásának módját érinti. A HTTPS kiválasztása nem engedélyezi a titkosítást.", "labelHost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelPort": "Port", "labelSchema": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options": {"boolean": {"no": "nem", "yes": "igen"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "schema": {"http": "HTTP (titkosítatlan)", "https": "HTTPS (titkosított)"}, "status": {"A": "A (nincs <PERSON>)", "B": "B (csatlakoztatva)", "C": "C (töltés)"}}, "pv": {"titleAdd": "Napel<PERSON><PERSON>", "titleEdit": "Napelemes M<PERSON>"}, "section": {"additionalMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON>lán<PERSON>", "grid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integrations": "Integrációk", "loadpoints": "Töltőpontok", "meter": "Napelem és Akkumulátor", "system": "Rendszer", "vehicles": "Járművek"}, "sponsor": {"addToken": "<PERSON><PERSON> be<PERSON>", "changeToken": "Token megváltoztatása", "description": "A szponzorációs modell segít a projekt fenntartásában és az új izgalmas funkciók bevezetésében. Szponzorként teljes hozzáférést kapsz az összes töltőberendezés implementációjához.", "descriptionToken": "A tokent innen kapja: {url}. A teszteléshez próba tokent is kínálunk.", "error": "A szponzor token nem érvényes.", "labelToken": "Szponzor token", "title": "Sz<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "tokenRequired": "Konfigurálnod kell e<PERSON>, miel<PERSON>tt létre tudnád hozni ezt az eszközt.", "tokenRequiredLearnMore": "<PERSON><PERSON><PERSON> meg többet.", "trialToken": "Próbaidős token"}, "system": {"logs": "Na<PERSON><PERSON>ó", "restart": "Újraindítás", "restartRequiredDescription": "Kérlek indítsd újra a hatás eléréséhez.", "restartRequiredMessage": "A Konfiguráció megváltozott.", "restartingDescription": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>rj…", "restartingMessage": "evcc újraindítása."}, "tariffs": {"description": "Határozza meg energiatarifáját a töltési folyamatok költségeinek kiszámításához.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Ez jelenik meg a főképernyőn és a böngésző címsorában.", "label": "Megnevezés", "title": "Megnevezés Szerkesztése"}, "validation": {"failed": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "success": "sikeres", "unknown": "ismeretlen", "validate": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vehicle": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "chargingSettings": "Töltési beállítások", "defaultMode": "Alapértelmezett mód", "defaultModeHelp": "Töltési mód a jármű csatlakoztatásakor.", "delete": "Törlés", "generic": "<PERSON><PERSON><PERSON><PERSON>g<PERSON>", "identifiers": "RFID azonosítók", "identifiersHelp": "A jármű azonosítására szolgáló RFID karakterláncok listája. Soronként egy bejegyzés. Az aktuális azonosító a megfelelő töltési pontnál található az áttekintő oldalon.", "maximumCurrent": "Maximum áram", "maximumCurrentHelp": "Nagyobbnak kell lennie, mint a minimum áram", "maximumPhases": "<PERSON><PERSON><PERSON>", "maximumPhasesHelp": "Hány fá<PERSON>l tud ez a jármű tölteni? A szükséges minimális szolá<PERSON> többlet és a tervezett időtartam kiszámításához has<PERSON>ják.", "minimumCurrent": "Minimum áram", "minimumCurrentHelp": "Csak akkor menj 6A alá, ha tudod, hogy mit c<PERSON><PERSON><PERSON><PERSON>.", "online": "Járművek online API-val", "primary": "Általános integrációk", "priority": "Prioritás", "priorityHelp": "A magasabb prioritás azt jelenti, hogy ez a jármű előnyben részesíti a napenergia-többlet elérését.", "save": "Men<PERSON>s", "scooter": "Roller", "template": "G<PERSON><PERSON><PERSON><PERSON>", "titleAdd": "Jármű hozzáadása", "titleEdit": "Jármű szerkesztése", "validateSave": "Ellenőrzés & mentés"}}, "footer": {"community": {"greenEnergy": "Napenergia", "greenEnergySub1": "lett töltve evcc-vel", "greenEnergySub2": "2022 Októbere óta", "greenShare": "Napenergia aránya", "greenShareSub1": "energiát biztosított a", "greenShareSub2": "napenergia, és az akkumulá<PERSON>", "power": "Töltési energia", "powerSub1": "{activeClients} / {totalClients} résztvevőből", "powerSub2": "tölt <PERSON>n…", "tabTitle": "<PERSON><PERSON><PERSON> közösség"}, "savings": {"co2Saved": "{value} me<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "co2Title": "CO₂ Emisszió", "configurePriceCo2": "Ismerje meg az ár- és CO₂-adatok konfigurálását.", "footerLong": "{percent} napenergia", "footerShort": "{percent} nap", "modalTitle": "Töltési Energia Áttekintés", "moneySaved": "{value} me<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentGrid": "{grid} <PERSON>h h<PERSON>", "percentSelf": "{self} kWh napenergia", "percentTitle": "Napenergia", "period": {"30d": "elmúlt 30 nap", "365d": "elmúlt 365 nap", "thisYear": "id<PERSON>", "total": "összesen"}, "periodLabel": "Periódus:", "priceTitle": "Energia Ára", "referenceGrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "referenceLabel": "Referencia adat:", "tabTitle": "<PERSON><PERSON>"}, "sponsor": {"becomeSponsor": "<PERSON><PERSON><PERSON><PERSON>", "becomeSponsorExtended": "Támogass közvetlenül, hogy matricákat kapj.", "confetti": "Készen állsz a konfettire?", "confettiPromise": "Ka<PERSON>z matricákat és digitális konfettit", "sticker": "… vagy s<PERSON> evcc matricákat?", "supportUs": "A küldetésünk az, hogy a napelemes töltést normává tegyük. Segítsd az evcc-t annyival, amennyit megér neked.", "thanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {sponsor}! A hozzájárulásod segít tovább fejleszteni az evcc-t.", "titleNoSponsor": "Támogass minket", "titleSponsor": "Te már támo<PERSON> vagy", "titleTrial": "Próbaverzió", "titleVictron": "Szponzorálta a Victron Energy", "trial": "Jelenleg próbaverziót használsz korlátlan funkciókkal. Kérlek fontold meg a projekt támogatását.", "victron": "Jelenleg a Victron Energy hardverén használod az evcc-t korlátlan funkciókkal."}, "telemetry": {"optIn": "Szeretnék hozzájárulni az adataimmal.", "optInMoreDetails": "Részletek {0}.", "optInMoreDetailsLink": "itt", "optInSponsorship": "Szponzorálás szükséges."}, "version": {"availableLong": "<PERSON><PERSON> verz<PERSON>", "modalCancel": "<PERSON><PERSON><PERSON><PERSON>", "modalDownload": "Letöltés", "modalInstalledVersion": "Telepített verzió", "modalNoReleaseNotes": "Nem érhető el kiadási jegyzet. További információ az új verzióról:", "modalTitle": "<PERSON><PERSON> verzió elé<PERSON>", "modalUpdate": "Telepítés", "modalUpdateNow": "Telepítés most", "modalUpdateStarted": "Az új evcc verzió indítása…", "modalUpdateStatusStart": "A telepítés elkezdődött:"}}, "forecast": {"co2": {"average": "Átlag", "lowestHour": "Legtisztább óra", "range": "Tartomány"}, "modalTitle": "Előrejelzés", "price": {"average": "Átlag", "lowestHour": "Legolcsóbb óra", "range": "Tartomány"}, "solar": {"dayAfterTomorrow": "Holnap után", "partly": "részlegesen", "remaining": "hátralévő", "today": "Ma", "tomorrow": "Holnap"}, "solarAdjust": "Módosítsa a napenergia-előrejelzést a valós termelési adatok alapján {percent}.", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "header": {"about": "Névjegy", "blog": "Blog", "docs": "Do<PERSON>ment<PERSON><PERSON>ó", "github": "GitHub", "login": "Jármű Bejelentkezések", "logout": "Kijelentkezés", "nativeSettings": "Szerver Váltás", "needHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van segítségre?", "sessions": "Töltési Folyamatok"}, "help": {"discussionsButton": "GitHub Közösségi <PERSON>", "documentationButton": "Do<PERSON>ment<PERSON><PERSON>ó", "issueButton": "Hibabejelentés", "issueDescription": "Furcsa vagy hibás működést észleltél?", "logsButton": "<PERSON><PERSON><PERSON><PERSON>", "logsDescription": "Ellenőrizd a naplót hiba esetén.", "modalTitle": "Segítség<PERSON> van szükséged?", "primaryActions": "Valami nem úgy működik, ahogy működnie kellene? <PERSON>zek j<PERSON>, hogy választ kapj a problémádra.", "restart": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON>, <PERSON><PERSON>raindít<PERSON>!", "description": "Normál körülmények között nem szükséges az újraindítás. Kérlek fontold meg a hibabejelentést, ha az evcc-t gyakran újra kell indítanod.", "disclaimer": "Megjegyzés: az evcc bezáródik és az operációs rendszertől függően újraindítja a szolgáltatást.", "modalTitle": "Biztosan újra szeretnéd indítani?"}, "restartButton": "Újraindítás", "restartDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> már be- és kikapcsolni?", "secondaryActions": "Még mindíg nem oldódott meg a problémád? Itt van néhány keményebb lehetőség."}, "log": {"areaLabel": "Szűrés terület alapján", "areas": "<PERSON><PERSON> terü<PERSON>", "download": "Teljes napló letöltése", "levelLabel": "Szűrés a napló szintje alapján", "nAreas": "{count} te<PERSON><PERSON><PERSON>", "noResults": "<PERSON><PERSON><PERSON> e<PERSON>ez<PERSON> napló bejegyzés.", "search": "Keresés", "selectAll": "összes kiválasztása", "showAll": "Az összes bejegyzés megjelenítése", "title": "Na<PERSON><PERSON>ó", "update": "Automatikus f<PERSON>"}, "loginModal": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "error": "Sikertelen bejelentkezés: ", "iframeHint": "evcc megnyitása új lapon.", "iframeIssue": "A j<PERSON><PERSON>d <PERSON>, de <PERSON><PERSON> tű<PERSON>, hogy a böngésződ elvetette a hitelesítési sütit. Ez akkor történhet meg, ha az evcc-t iframe-ben futtatod HTTP-n.", "invalid": "A jelszó érvénytelen.", "login": "Bejelentkezés", "password": "Je<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON>?", "title": "Hitelesítés"}, "main": {"chargingPlan": {"active": "Aktív", "addRepeatingPlan": "Ismétlődő terv hozzáadása", "arrivalTab": "Érkezés", "day": "Nap", "departureTab": "Indulás", "goal": "Töltési cél", "modalTitle": "Töltési Tervezet", "none": "nincs", "planNumber": "Terv {number}", "preconditionDescription": "Töltse {duration} indulás el<PERSON>tt az akkumulátor előkondícionálásához.", "preconditionLong": "Késleltetett Töltés", "preconditionOptionAll": "minden", "preconditionOptionNo": "nem", "preconditionShort": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Törlés", "repeating": "ismétlődő", "repeatingPlans": "Ismétlődő tervek", "selectAll": "Összes kiválasztása", "time": "<PERSON><PERSON><PERSON>", "title": "<PERSON>rv", "titleMinSoc": "<PERSON>", "titleTargetCharge": "Indulás", "unsavedChanges": "Vannak nem mentett módosítások. Alkalmazza most?", "update": "Alkalmazás", "weekdays": "Napok"}, "energyflow": {"battery": "Battery", "batteryCharge": "Energiat<PERSON><PERSON><PERSON>", "batteryDischarge": "Energia<PERSON><PERSON><PERSON><PERSON>", "batteryGridChargeActive": "hálózatból töltés aktív", "batteryGridChargeLimit": "hálózatból töltés ha", "batteryHold": "Energiat<PERSON><PERSON><PERSON> (lezárva)", "batteryTooltip": "{energy} / {total} ({soc})", "forecastTooltip": "előrejelzés: ma fennmaradó napenergia-termelés", "gridImport": "Hálózatból import", "homePower": "Fogyasztás", "loadpoints": "Töltő| Töltő | {count} töltő", "noEnergy": "<PERSON><PERSON><PERSON> m<PERSON> adat", "pv": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "pvExport": "Hálózatba export", "pvProduction": "<PERSON><PERSON><PERSON>", "selfConsumption": "<PERSON><PERSON><PERSON><PERSON>"}, "heatingStatus": {"charging": "<PERSON><PERSON><PERSON><PERSON>…", "connected": "Készenlét.", "vehicleLimit": "Fűtés limit", "waitForVehicle": "Üzemkész. Fűtésre várakozás…"}, "loadpoint": {"avgPrice": "⌀ <PERSON>r", "charged": "Töltve", "co2": "⌀ CO₂", "duration": "Időtar<PERSON>", "fallbackName": "T<PERSON>ltőpont", "finished": "Befejezési idő", "power": "Teljesítmény", "price": "Költség", "remaining": "Hátralévő", "remoteDisabledHard": "{source}: kikapcsolva", "remoteDisabledSoft": "{source}: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> t<PERSON>", "solar": "Nap"}, "loadpointSettings": {"batteryBoost": {"description": "Gyorstöltés otthoni akkumulátorról.", "label": "Akkumulátoros Rásegítés", "mode": "Csak szolár és min+szol<PERSON>r módban elérhető.", "once": "Rásegítés aktív ehhez a töltési folyamathoz."}, "batteryUsage": "<PERSON>tt<PERSON><PERSON>", "currents": "Töltőáram", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disclaimerHint": "Megjegyzés:", "limitSoc": {"description": "Töltési <PERSON>, amikor a jármű csatlakoztatva van.", "label": "Alapértelmezett limit"}, "maxCurrent": {"label": "<PERSON><PERSON>"}, "minCurrent": {"label": "<PERSON><PERSON>"}, "minSoc": {"description": "A jármű gyorstöltve lesz {0}-ra <PERSON><PERSON><PERSON><PERSON><PERSON> üzemmódban. Ezután folytatódik a töltés a napelemes többletenergiával. Hasznos egy minimum tartományt megadni a felhősebb napokra.", "label": "<PERSON>. <PERSON> %"}, "onlyForSocBasedCharging": "Ezek a beállítások csak olyan járművekre elérhetőek, amiknek ismert a töltési szintje.", "phasesConfigured": {"label": "<PERSON><PERSON><PERSON><PERSON>", "no1p3pSupport": "<PERSON><PERSON><PERSON> m<PERSON> van csatlakoztatva a töltőd?", "phases_0": "auto kapcsolás", "phases_1": "1 fázis", "phases_1_hint": "({min}-tól {max}-ig)", "phases_3": "3 fázis", "phases_3_hint": "({min}-tól {max}-ig)"}, "smartCostCheap": "<PERSON><PERSON><PERSON><PERSON>zati Töltés", "smartCostClean": "Tiszta Hálózati Töltés", "title": "Beállítások {0}", "vehicle": "Jármű"}, "mode": {"minpv": "Min+<PERSON><PERSON><PERSON><PERSON><PERSON>", "now": "Gyors", "off": "<PERSON>", "pv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smart": "<PERSON><PERSON>"}, "provider": {"login": "bejelentkezés", "logout": "kijelentkezés"}, "startConfiguration": "Konfigu<PERSON><PERSON><PERSON><PERSON>és<PERSON>", "targetCharge": {"activate": "Aktiválás", "co2Limit": "CO₂ limit, ami {co2}", "costLimitIgnore": "A konfigurált {limit} figyelmen kívűl lesz hagyva ezen időszakban.", "currentPlan": "Aktív terv", "descriptionEnergy": "<PERSON><PERSON><PERSON> kell a {targetEnergy}-t tölten<PERSON> a j<PERSON>rm<PERSON>?", "descriptionSoc": "<PERSON><PERSON> legyen a jármű feltöltve {targetSoc}-ra?", "goalReached": "<PERSON><PERSON><PERSON> a célt", "inactiveLabel": "Tervezett idő", "nextPlan": "Következő terv", "notReachableInTime": "A tervezet el lesz érve {overrun}.", "onlyInPvMode": "A töltési idő csak napelemes üzemmódban működik.", "planDuration": "Töltési idő", "planPeriodLabel": "<PERSON><PERSON><PERSON><PERSON>", "planPeriodValue": "{start} to {end}", "planUnknown": "még nem ismert", "preview": "<PERSON><PERSON><PERSON>", "priceLimit": "ár limit: {price}", "remove": "Eltávolítás", "setTargetTime": "nincs", "targetIsAboveLimit": "A konfigurált töltési limit, ami {limit} figyelmen kívül lesz hagyva ebben a periódusban.", "targetIsAboveVehicleLimit": "A Jármű limitje a töltési cél alatt van.", "targetIsInThePast": "<PERSON><PERSON><PERSON><PERSON> egy <PERSON> a jövőben, Marty.", "targetIsTooFarInTheFuture": "Hozzáigazítjuk a tervet, amint többet tudunk a jövőről.", "title": "<PERSON><PERSON><PERSON>", "today": "ma", "tomorrow": "holnap", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleCapacityDocs": "<PERSON><PERSON><PERSON> meg, hogyan kell konfigur<PERSON>lni.", "vehicleCapacityRequired": "A jármű akkumulátor kapacitása szükséges a hozzávetőleges idő meghatározásához."}, "targetChargePlan": {"chargeDuration": "Töltési idő", "co2Label": "CO₂ emisszió ⌀", "priceLabel": "Energia ára", "timeRange": "{day} {range} h", "unknownPrice": "még is<PERSON>tlen"}, "targetEnergy": {"label": "Limit", "noLimit": "nincs"}, "vehicle": {"addVehicle": "Jármű hozzáadása", "changeVehicle": "Jármű cseréje", "detectionActive": "Jármű detektálása…", "fallbackName": "Jármű", "moreActions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "notReachable": "A jármű nem elérhető. Próbálja meg újraindítani az evcc-t.", "targetSoc": "Limit", "temp": "Hőm.", "tempLimit": "Hőm. limit", "unknown": "Vend<PERSON>g <PERSON>", "vehicleSoc": "Töltöttség"}, "vehicleStatus": {"awaitingAuthorization": "Hitelesítésre várakozás.", "batteryBoost": "Akkumulátoros rásegítés aktív.", "charging": "<PERSON><PERSON><PERSON><PERSON>…", "cheapEnergyCharging": "Olcsó energia elérhető.", "cheapEnergyNextStart": "Olcsó energia {duration}.", "cheapEnergySet": "Ár limit be<PERSON><PERSON><PERSON><PERSON><PERSON>.", "cleanEnergyCharging": "Tiszta energia elérhető.", "cleanEnergyNextStart": "Olcsó energia {duration}.", "cleanEnergySet": "CO₂ limit beállítva.", "climating": "Elő-kondícionálás érzékelve.", "connected": "Csatlakoztatva.", "disconnectRequired": "A munkamenet megszakadt. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>zon <PERSON>.", "disconnected": "Lecsatlakoztatva.", "finished": "Befejezve.", "minCharge": "<PERSON><PERSON><PERSON><PERSON> tö<PERSON> {soc}-ig.", "pvDisable": "<PERSON><PERSON><PERSON> el<PERSON>g tö<PERSON>t. Szüneteltetés hamarosan.", "pvEnable": "Többlet elérhető. Indítás hamarosan.", "scale1p": "1 Fázisú töltésre váltás hamarosan.", "scale3p": "3 Fázisú töltésre váltás hamarosan.", "targetChargeActive": "Töltési terv aktív. Becs<PERSON>lt befejezés {duration}.", "targetChargePlanned": "A töltési tervezet ekkor keződik: {duration}.", "targetChargeWaitForVehicle": "Töltési terv üzemkész. Járműre várakozás…", "vehicleLimit": "Jármű limi.", "vehicleLimitReached": "Jármű limit elérve.", "waitForVehicle": "Üzemkész. Járműre várakozás…", "welcome": "<PERSON><PERSON><PERSON> k<PERSON> töltés a csatlakozás megerősítéséhez."}, "vehicles": "Parkolás", "welcome": "Üdv a fedélzeten!"}, "notifications": {"dismissAll": "Összeset figyelmen kívül hagyja", "logs": "Teljes nap<PERSON>ó megtekintése", "modalTitle": "Értesítések"}, "offline": {"configurationError": "Hiba az indítás során. Ellenőrizd a konfigurációd és indítsd újra.", "message": "Nem csatlakozik a szerverhez.", "restart": "Újraindítás", "restartNeeded": "A módosítások végrehajtásához szükséges.", "restarting": "A szerver egy pillanat múlva elérhető lesz."}, "passwordModal": {"description": "Á<PERSON><PERSON><PERSON><PERSON> be egy j<PERSON>t a konfigurációs beállítások védelmére. A főképernyő használata továbbra is lehetséges bejelentkezés nélkül.", "empty": "A jelszó nem lehet üres", "error": "Hiba: ", "labelCurrent": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "labelNew": "<PERSON><PERSON>", "labelRepeat": "<PERSON><PERSON><PERSON><PERSON> megismétlése", "newPassword": "<PERSON><PERSON><PERSON><PERSON> készíté<PERSON>", "noMatch": "A jelszavak nem egyeznek", "titleNew": "Adminisztr<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "titleUpdate": "Adminisztrá<PERSON> j<PERSON><PERSON><PERSON>í<PERSON>", "updatePassword": "Jelszó módosítás"}, "session": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Törlés", "finished": "Befejeződött", "meter": "<PERSON><PERSON><PERSON><PERSON>", "meterstart": "M<PERSON>rő in<PERSON>í<PERSON>", "meterstop": "<PERSON><PERSON><PERSON><PERSON>", "odometer": "Futásteljesítmény", "price": "<PERSON><PERSON>", "started": "Elkezdődött", "title": "Töltési Folyamatok"}, "sessions": {"avgPower": "⌀ Teljesítmény", "avgPrice": "⌀ <PERSON>r", "chargeDuration": "Időtar<PERSON>", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Ár {byGroup}", "byGroupLoadpoint": "töltőpont szerint", "byGroupVehicle": "jármű szerint", "energy": "Töltött Energia", "energyGrouped": "<PERSON><PERSON><PERSON> vs. Hálózati Energia", "energyGroupedByGroup": "Energia {byGroup}", "energySubSolar": "{value} nap", "energySubTotal": "{value} összesen", "groupedCo2ByGroup": "CO₂-Mennyiség {byGroup}", "groupedPriceByGroup": "Összes Költség {byGroup}", "historyCo2": "CO₂-Emissziók", "historyCo2Sub": "{value} összesen", "historyPrice": "Töltési Költségek", "historyPriceSub": "{value} összesen", "solar": "Napelem aránya egy évre vetítve", "solarByGroup": "Na<PERSON><PERSON> {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Energia (kWh)", "chargeduration": "Időtar<PERSON>", "co2perkwh": "CO₂/kWh", "created": "Elindítva", "finished": "Befejezve", "identifier": "Azonosító", "loadpoint": "T<PERSON>ltőpont", "meterstart": "Fogyasztásmérő kezdeti <PERSON>ll<PERSON> (kWh)", "meterstop": "Fogyasztásmérő befe<PERSON> (kWh)", "odometer": "Óraállás (km)", "price": "<PERSON><PERSON>", "priceperkwh": "Ár/kWh", "solarpercentage": "Napenergia (%)", "vehicle": "Jármű"}, "csvPeriod": "Letöltés - {period} .CSV", "csvTotal": "Letöltés - Összes időszak .CSV", "date": "Kezdés", "energy": "Töltve", "filter": {"allLoadpoints": "<PERSON><PERSON> t<PERSON>", "allVehicles": "<PERSON><PERSON>", "filter": "Szűrés"}, "group": {"co2": "Emissziók", "grid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "self": "Nap"}, "groupBy": {"loadpoint": "T<PERSON>ltőpont", "none": "Összes", "vehicle": "Jármű"}, "loadpoint": "T<PERSON>ltőpont", "noData": "<PERSON><PERSON><PERSON> t<PERSON> folyamat ebben a hónap<PERSON>.", "overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "period": {"month": "Hónap", "total": "Összes", "year": "<PERSON><PERSON>"}, "price": "Σ Költség", "reallyDelete": "Biztosan szeretné törölni ezt a folyamatot?", "showIndividualEntries": "Egyéni munkamenetek megjelenítése", "solar": "Napenergia", "title": "Töltési Folyamatok", "total": "Összesen", "type": {"co2": "CO₂", "price": "<PERSON><PERSON>", "solar": "Nap"}, "vehicle": "Jármű"}, "settings": {"fullscreen": {"enter": "<PERSON><PERSON><PERSON>", "exit": "Kilépés a teljes képernyőből", "label": "<PERSON><PERSON><PERSON>"}, "hiddenFeatures": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Kísérleti UI funkciók megjelenítése."}, "language": {"auto": "Automatikus", "label": "Nyelv"}, "sponsorToken": {"expires": "A szponzor token-ed le fog járni {inXDays}. {getNewToken} és frissítsd itt.", "getNew": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Megjegyzés: Ezt a jövőben automatizálni fogjuk."}, "telemetry": {"label": "Telemetria"}, "theme": {"auto": "re<PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "light": "vil<PERSON>gos"}, "time": {"12h": "12h", "24h": "24h", "label": "<PERSON><PERSON><PERSON>"}, "title": "Felhasználói felület", "unit": {"km": "km", "label": "Mértékegység", "mi": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "smartCost": {"activeHours": "{active} / {total}", "activeHoursLabel": "Aktív órák", "applyToAll": "Minenhol alkalmazás?", "batteryDescription": "Az otthoni töltéstároló töltése a hálózatról.", "cheapTitle": "<PERSON><PERSON><PERSON><PERSON>zati Töltés", "cleanTitle": "Tiszta Hálózati Töltés", "co2Label": "CO₂ emisszió", "co2Limit": "CO₂ limit", "loadpointDescription": "Engedélyezi az átmeneti gyorstöltést szolár üzemmódban.", "modalTitle": "<PERSON><PERSON>ózati Tölt<PERSON>", "none": "nincs", "priceLabel": "Energia ár", "priceLimit": "Ár limit", "resetAction": "<PERSON>it <PERSON>", "resetWarning": "Nincs konfigurálva dinamikus hálózati ár vagy CO₂-forrás. Azonban még mindig van {limit} korlát. Megtisztítja a konfigurációt?", "saved": "Elmentve."}, "startupError": {"configFile": "Konfigu<PERSON><PERSON><PERSON><PERSON> fá<PERSON><PERSON>:", "configuration": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kérlek ellenőrizd a konfigurációs fájlodat. Ha a hibaüzenet nem segít, akkor nézd meg a {0}.", "discussions": "GitHub Közösségi oldal", "fixAndRestart": "Kérlek javítsd ki a problémát és indítsd újra a szervert.", "hint": "Megjegyzés: <PERSON><PERSON><PERSON>, hogy van egy hibás eszközöd (inverter, mérő, …) Ellenőrizd a hálózati kapcsolatokat.", "lineError": "Hiba a {0}.", "lineErrorLink": "sor {0}", "restartButton": "Újraindítás", "title": "Indítási hiba"}}