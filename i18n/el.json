{"batterySettings": {"batteryLevel": "Ποσοστό Μπαταρίας", "bufferStart": {"above": "όταν είναι πάνω από {soc}.", "full": "όταν είναι {soc}.", "never": "μόνο με αρκετό πλεόνασμα."}, "capacity": "{energy} από {total}", "control": "Έλεγχος Μπαταρίας", "discharge": "Αποτροπή εκφόρτισης σε γρήγορη λειτουργία και προγραμματισμένη φόρτιση.", "disclaimerHint": "Σημείωση:", "disclaimerText": "Αυτές οι ρυθμίσεις επηρεάζ<PERSON>υν μόνο την ηλιακή Φ/Β λειτουργία. Η συμπεριφορά φόρτισης προσαρμόζεται ανάλογα.", "gridChargeTab": "Φόρτιση δικτύου", "legendBottomName": "Προτεραιότητα στη φόρτιση της οικιακής μπαταρίας", "legendBottomSubline": "μέχρι να φτάσει {soc}.", "legendMiddleName": "Προτεραιότητα στη φόρτιση οχήματος", "legendMiddleSubline": "όταν η οικιακή μπαταρία είναι πάνω από {soc}.", "legendTopAutostart": "Ξεκινά αυτόματα", "legendTopName": "Φόρτιση οχήματος μέσω μπαταρίας", "legendTopSubline": "όταν η οικιακή μπαταρία είναι πάνω από {soc}.", "modalTitle": "Οικιακή Μπαταρία", "usageTab": "Χρήση μπαταρίας"}, "config": {"aux": {"description": "Συσκευή που προσαρμόζει την κατανάλωσή της με βάση το διαθέσιμο πλεόνασμα (όπως έξυπνοι θερμαντήρες νερού). Το EVCC αναμένει ότι αυτή η συσκευή θα μειώσει την κατανάλωση ενέργειας εάν χρειαστεί.", "titleAdd": "Προσθήκη αυτορυθμιζόμενου καταναλωτή", "titleEdit": "Επεξεργα<PERSON><PERSON>α αυτορυθμιζόμενου καταναλωτή"}, "battery": {"titleAdd": "Πρόσθεσε Μπαταρία", "titleEdit": "Eπεξεργασ<PERSON>α Mπαταρίας"}, "charge": {"titleAdd": "Προσθήκη μετρητή φόρτισης", "titleEdit": "Επεξεργασία μετρητή φόρτισης"}, "charger": {"chargers": "Φορτιστ<PERSON>ς οχημάτων", "generic": "Γενι<PERSON><PERSON>ς ενσωματώσεις", "heatingdevices": "Συσκευές θέρμανσης", "ocppHelp": "Αντιγράψτε αυτή τη διεύθυνση στη διαμόρφωση των φορτιστών σας.", "ocppLabel": "OCPP-Server URL", "switchsockets": "Πρίζες με δυνατότητα εναλλαγής", "template": "Κατα<PERSON>κ<PERSON><PERSON><PERSON><PERSON>τής", "titleAdd": {"charging": "Προσθήκη Φορτιστή", "heating": "Προσθήκη Θερμαντήρα"}, "titleEdit": {"charging": "Επεξεργασία Φορτιστή", "heating": "Επεξεργασία Θερμαντήρα"}, "type": {"custom": {"charging": "Φορτιστή<PERSON> που καθορίζε<PERSON><PERSON>ι από τον χρήστη", "heating": "Θερμαντ<PERSON><PERSON><PERSON><PERSON> που καθορίζετ<PERSON>ι από τον χρήστη"}, "heatpump": "Αντλία θερμότητας που καθορίζεται από τον χρήστη", "sgready": "Αντλία θερμότητας που καθορίζεται από τον χρήστη (SG-ready, all)", "sgready-boost": "Αντλία θερμότητας που καθορίζεται από τον χρήστη (SG-ready, boost)", "switchsocket": "Διακ<PERSON>πτης πρίζας που καθορίζεται από τον χρήστη"}}, "circuits": {"description": "Διασφαλίζει ότι το άθροισμα όλων των σημείων φόρτισης που είναι συνδεδεμένα σε ένα κύκλωμα δεν υπερβαίνει τα οριζόμενα όρια ισχύος και έντασης ρεύματος. Τα κυκλώματα μπορούν να είναι σε ένθετες δομές για τη δημιουργία μιας ιεραρχίας.", "title": "Διαχείριση Φορτίου"}, "control": {"description": "Συνήθως οι προεπιλεγμένες τιμές αρκούν. Αλλάξτε τις μόνο εάν γνωρίζετε τι κάνετε.", "descriptionInterval": "Έλεγχος του κύκλου ενημέρωσης βρόχου σε δευτερόλεπτα. Καθορίζει πόσο συχνά το evcc διαβάζει τα δεδομένα του μετρητή, προσαρμόζει την ισχύ φόρτισης και ενημερώνει τη διεπαφή χρήστη. Μικρά διαστήματα (< 30 δευτερόλεπτα) μπορεί να προκαλέσουν ταλαντώσεις και ανεπιθύμητη συμπεριφορά.", "descriptionResidualPower": "Μετατοπίζει το σημείο λειτουργίας του βρόχου ελέγχου. Εάν έχετε οικιακή μπαταρία, συνιστάται να ορίσετε μια τιμή 100 W. Με αυτόν τον τρόπο η μπαταρία θα έχει μικρότερη προτεραιότητα σε σχέση με τη χρήση δικτύου.", "labelInterval": "Διάστημα ενημέρωσης", "labelResidualPower": "Υπολειπόμενη ισχύς", "title": "Έλεγχος συμπεριφοράς"}, "deviceValue": {"amount": "Ποσό", "broker": "Broker", "bucket": "Κου<PERSON><PERSON>ς", "capacity": "Χω<PERSON>ητικ<PERSON>τητα", "chargeStatus": "Κατάσταση", "chargeStatusA": "μη συνδεδεμένο", "chargeStatusB": "συνδεδεμένο", "chargeStatusC": "φορτίζει", "chargeStatusE": "χωρ<PERSON>ς ισχύ", "chargeStatusF": "σφάλμα", "chargedEnergy": "Φορτισμένο", "co2": "CO₂ Δικτύου", "configured": "Ρυθμίστηκε", "controllable": "«Ελεγχόμενο»", "currency": "Νόμισμα", "current": "Ένταση", "currentRange": "Ένταση", "enabled": "Ενεργοποιημένο", "energy": "Ενέργεια", "feedinPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Feed-in", "gridPrice": "Τιμή δικτύου", "heaterTempLimit": "Όριο θερμαντήρα", "hemsType": "Σύστημα", "identifier": "Αναγνωριστικό RFID", "no": "όχι", "odometer": "Οδόμετρο", "org": "Οργανισμός", "phaseCurrents": "Ένταση L1, L2, L3", "phasePowers": "Ισχύς L1, L2, L3", "phaseVoltages": "Τάση L1, L2, L3", "phases1p3p": "Eναλλαγ<PERSON> φά<PERSON>εων", "power": "Ισχύς", "powerRange": "Ισχύς", "range": "Αυτονομία", "singlePhase": "Μονοφασικό", "soc": "Κατάσταση φόρτισης", "solarForecast": "Ηλιακή πρόγνωση", "temp": "Θερμοκρασία", "topic": "Θέμα", "url": "URL", "vehicleLimitSoc": "Όριο οχήματος", "yes": "ναι"}, "deviceValueChargeStatus": {"A": "A (δεν είναι συνδεδεμένο)", "B": "B (συνδεδεμένο)", "C": "C (φορτίζει)"}, "devices": {"auxMeter": "Έξυπνος καταναλωτής", "batteryStorage": "Χωριτικότητα μπαταρίας", "solarSystem": "Ηλιακό σύστημα"}, "editor": {"loading": "Φόρτωση του επεξεργαστή YAML…"}, "eebus": {"description": "Διαμόρφωση που επιτρέπει στο evcc να επικοινωνεί με άλλες συσκευές EEBus.", "title": "EEBus"}, "ext": {"description": "Μπορεί να χρησιμοποιηθεί για σκοπούς διαχείρισης φορτίου ή στατιστικής.", "titleAdd": "Προσθήκη Εξωτερικού μετρητή", "titleEdit": "Επεξεργα<PERSON><PERSON><PERSON> εξωτερικού μετρητή"}, "form": {"danger": "Κίνδυνος", "deprecated": "καταργήθηκε", "example": "Παράδειγμα", "optional": "προαιρετικό"}, "general": {"cancel": "Ακύρωση", "customHelp": "Δημιουργήστε μια συσκευή οριζόμενη από το χρήστη χρησιμοποιώντας το σύστημα πρόσθετων του evcc.", "customOption": "Οριζόμενη συσκευή από το χρήστη", "delete": "Διαγραφή", "docsLink": "Δείτε την τεκμηρίωση.", "experimental": "Πειραματικό", "hideAdvancedSettings": "Απόκρυψη ρυθμίσεων για προχωρημένους", "invalidFileSelected": "Επιλέχθηκε μη έγκυρο αρχείο", "noFileSelected": "Δεν έχει επιλεγεί αρχείο.", "off": "κλειστό", "on": "ανοιχτό", "password": "Κω<PERSON>ικ<PERSON>ς", "readFromFile": "Ανάγνωση από αρχείο", "remove": "Αφαίρεση", "save": "Αποθήκευση", "selectFile": "Περιήγηση", "showAdvancedSettings": "Εμφάνιση ρυθμίσεων για προχωρημένους", "telemetry": "Τηλεμετρία", "templateLoading": "Φόρτωση...", "title": "Τίτλος", "validateSave": "Επικύρωση και αποθήκευση"}, "grid": {"title": "Μετρητής δικτύου", "titleAdd": "Προσθήκη Μετρητή Δικτύου", "titleEdit": "Επεξεργασία Μετρητή Δικτύου"}, "hems": {"description": "Συνδέστε το evcc σε άλλο οικια<PERSON><PERSON> σύστημα διαχείρισης ενέργειας.", "title": "HEMS"}, "icon": {"change": "αλλαγή"}, "influx": {"description": "Γράφει δεδομένα χρέωσης και άλλες μετρήσεις στην InfluxDB. Χρησιμοποιήστε το Grafana ή άλλα εργαλεία για οπτικοποίηση των δεδομένων.", "descriptionToken": "Ελέγξτε την τεκμηρίωση της InfluxDB για να μάθετε πώς να δημιουργήσετε κάποιο. https://docs.influxdata.com/influxdb/v2/admin/", "labelBucket": "Κου<PERSON><PERSON>ς", "labelCheckInsecure": "Να επιτρέπονται τα self-signed πιστοποιητικά", "labelDatabase": "Βάση δεδομένων", "labelInsecure": "Επικύρωση πιστοποιητικού", "labelOrg": "Οργανισμός", "labelPassword": "Κω<PERSON>ικ<PERSON>ς", "labelToken": "API Token", "labelUrl": "URL", "labelUser": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "title": "InfluxDB", "v1Support": "Με υποστήριξη για την InfluxDB 1.x;", "v2Support": "Επιστροφή σε InfluxDB 2.x"}, "loadpoint": {"addCharger": {"charging": "Προσθήκη φορτιστή", "heating": "Προσθήκη θερμαντήρα"}, "addMeter": "Προσθήκη αποκλειστικού μετρητή ενέργειας", "cancel": "Άκυρο", "chargerError": {"charging": "Απαιτείται διαμόρφωση φορτιστή.", "heating": "Απαιτείτ<PERSON>ι η διαμόρφωση ενός θερμαντήρα."}, "chargerLabel": {"charging": "Φορτιστής", "heating": "Θερμαντή<PERSON><PERSON>ς"}, "chargerPower11kw": "11 kW", "chargerPower11kwHelp": "Θα χρησιμοποιήσει εύρος έντασης ρεύματος από 6 έως 16 Α.", "chargerPower22kw": "22 kW", "chargerPower22kwHelp": "Θα χρησιμοποιήσει εύρος έντασης ρεύματος από 6 έως 32 A.", "chargerPowerCustom": "άλλο", "chargerPowerCustomHelp": "Ορίστε ένα προσαρμοσμένο εύρος έντασης ρεύματος.", "chargerTypeLabel": "Τύ<PERSON>ος φορτιστή", "chargingTitle": "Συμπεριφορά", "circuitHelp": "Εκχώρηση διαχείρισης φορτίου για τη διασφάλιση της μη υπέρβασης των ορίων ισχύος και ρεύματος.", "circuitLabel": "Κύκλωμα", "circuitUnassigned": "μη εκχωρημένο", "defaultModeHelp": {"charging": "Λειτου<PERSON><PERSON><PERSON><PERSON> φόρτισης όταν συνδεθεί το όχημα.", "heating": "Ρυθμίζεται κατά την εκκίνηση του συστήματος."}, "defaultModeHelpKeep": "Διατηρεί την τελευταία επιλεγμένη λειτουργία.", "defaultModeLabel": "Προεπιλεγμένη λειτουργία", "delete": "Διαγραφή", "electricalSubtitle": "Όταν έχετε αμφιβολίες, ρωτήστε τον ηλεκτρολόγο σας.", "electricalTitle": "Ηλεκτρολογικό", "energyMeterHelp": "Πρόσθετος μετρητής εάν ο φορτιστής δεν έχει ενσωματωμένο.", "energyMeterLabel": "Μετρητής ενέργειας", "estimateLabel": "Παρεμβολή επιπέδου φόρτισης μεταξύ ενημερώσεων API", "maxCurrentHelp": "Πρέπει να είναι μεγαλύτερο από την ελάχιστη ένταση.", "maxCurrentLabel": "Μέγιστη ένταση", "minCurrentHelp": "Πηγαίνετε κάτω από 6A μόνο εάν ξέρετε τι κάνετε.", "minCurrentLabel": "Ελάχιστη ένταση", "noVehicles": "Δεν έχουν προστεθεί οχήματα.", "option": {"charging": "Προσθήκη σημείου φόρτισης", "heating": "Προσθήκη συσκευής θέρμανσης"}, "phases1p": "μονοφασικό", "phases3p": "τριφασικό", "phasesAutomatic": "Αυτόματα οι φάσεις", "phasesAutomaticHelp": "Ο φορτιστής σας υποστηρίζει αυτόματη εναλλαγή μεταξύ φόρτισης 1 και 3 φάσεων. Στην οθόνη του μπορείτε να προσαρμόσετε αυτή τη λειτουργία επιλογής φάσεων κατά τη φόρτιση.", "phasesHelp": "Αριθμός συνδεδεμένων φάσεων.", "phasesLabel": "Φάσεις", "pollIntervalDanger": "Η τακτική αναζήτηση του οχήματος μπορεί να εξαντλήσει την μπαταρία του οχήματος. Ορισμένοι κατασκευαστές οχημάτων ενδέχεται να αποτρέψουν ενεργά τη φόρτιση σε αυτήν την περίπτωση. Δεν συνιστάται! Χρησιμοποιήστε το μόνο εάν γνωρίζετε τους κινδύνους.", "pollIntervalHelp": "<PERSON>ρ<PERSON><PERSON><PERSON> μετα<PERSON>ύ των ενημερώσεων API του οχήματος. Τα μικρά διαστήματα ενδέχεται να ταλαιπωρήσουν τη μπαταρία του οχήματος.", "pollIntervalLabel": "Διάστημα ενημέρωσης", "pollModeAlways": "πάντοτε", "pollModeAlwaysHelp": "Να ζητά πάντα ενημερώσεις κατάστασης σε τακτά χρονικά διαστήματα.", "pollModeCharging": "φορτίζει", "pollModeChargingHelp": "Να ζητά ενημερώσεις κατάστασης οχήματος μόνο κατά τη φόρτιση.", "pollModeConnected": "συνδεδεμένο", "pollModeConnectedHelp": "Ενημέρωση της κατάστασης του οχήματος σε τακτά χρονικά διαστήματα όταν είναι συνδεδεμένο.", "pollModeLabel": "Συμπεριφορά ενημέρωσης", "priorityHelp": "Η μεγαλύτερη προτεραιότητα έχει προτιμότερη πρόσβαση σε ηλιακό πλεόνασμα.", "priorityLabel": "Προτεραιότητα", "save": "Αποθήκευση", "showAllSettings": "Εμφάνιση όλων των ρυθμίσεων", "solarBehaviorCustomHelp": "Ορίστε το δικό σας εύρος ενεργοποίησης και απενεργοποίησης κατώτατων ορίων και καθυστερήσεων.", "solarBehaviorDefaultHelp": "Έναρξη μετά από {enableDelay} επαρκούς πλεονάσματος. Διακοπή όταν δεν υπάρχει αρκετό πλεόνασμα για {disableDelay}.", "solarBehaviorLabel": "Ηλιακά", "solarModeCustom": "προσαρμοσμένo", "solarModeMaximum": "μέγιστη ηλιακή ενέργεια", "thresholdDisableDelayLabel": "Απενεργοποίηση καθυστέρησης", "thresholdDisableHelpInvalid": "Χρησιμοποιήστε μια θετική τιμή.", "thresholdDisableHelpPositive": "Διακ<PERSON><PERSON><PERSON> όταν χρησιμοποιείτα<PERSON> περισσότερο από {power} από το πλέγμα για {καθυστέρηση}.", "thresholdDisableHelpZero": "Διακο<PERSON><PERSON> όταν η ελάχιστη απαιτούμενη ισχύς δεν μπορεί να ικανοποιηθεί για {καθυστέρηση}.", "thresholdDisableLabel": "Απενεργοποίηση τροφοδοσίας δικτύου", "thresholdEnableDelayLabel": "Ενεργοποίηση καθυστέρησης", "thresholdEnableHelpInvalid": "Χρησιμοποιήστε μια αρνητική τιμή.", "thresholdEnableHelpNegative": "Έναρξη όταν το πλεόνασμα {surplus} είναι διαθέσιμο για {delay}.", "thresholdEnableHelpZero": "Έναρξη όταν μπορεί να ικανοποιηθεί η ελάχιστη απαιτούμενη ισχύς για {delay}.", "thresholdEnableLabel": "Ενεργοποίηση τροφοδοσίας δικτύου", "titleAdd": {"charging": "Προσθήκη Σημείου Φόρτισης", "heating": "Προσθήκη Συσκευής Θέρμανσης", "unknown": "Προσθήκη Φορτιστ<PERSON> ή Θερμαντήρα"}, "titleEdit": {"charging": "Επεξεργασία Σημείου Φόρτισης", "heating": "Επεξεργα<PERSON><PERSON><PERSON> Συσκευής Θέρμανσης", "unknown": "Επεξεργα<PERSON><PERSON><PERSON> Φορτιστή ή Θερμαντήρα"}, "titleExample": {"charging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Υπόστεγο, κλπ.", "heating": "Αντλ<PERSON><PERSON> Θερμότητα<PERSON>, Θερ<PERSON>αντ<PERSON><PERSON><PERSON><PERSON>, κτλ."}, "titleLabel": "Τίτλος", "vehicleAutoDetection": "αυτόματος εντοπισμός", "vehicleHelpAutoDetection": "Επιλέγει αυτόματα το πιο πιθανό όχημα. Είναι δυνατή και η χειροκίνητη επιλογή.", "vehicleHelpDefault": "Υποθέτει ότι πάντα θα φορτίζει εδώ αυτό το όχημα. Η αυτόματη ανίχνευση είναι απενεργοποιημένη. Είναι δυνατή η χειροκίνητη επιλογή.", "vehicleLabel": "Προεπιλεγμένο όχημα", "vehiclesTitle": "Οχήματα"}, "main": {"addAdditional": "Προσθήκη επιπλέον μετρητή", "addGrid": "Προσθήκη Μετρητή Δικτύου", "addLoadpoint": "Προσθήκη φορτιστή ή θερμαντήρα", "addPvBattery": "Προσθήκη Φ/Β ενέργειας ή μπαταρίας", "addTariffs": "Προσθήκη τιμολογίων", "addVehicle": "Προσθήκη οχήματος", "configured": "ρυθμίστηκε", "edit": "επεξεργασία", "loadpointRequired": "Πρέπει να οριστεί τουλάχιστον ένα σημείο φόρτισης.", "name": "Όνομα", "title": "Διαμόρφωση", "unconfigured": "δεν έχει ρυθμιστεί", "vehicles": "Τα Οχήματά μου", "yaml": "Συσκευή που έχει οριστεί στο evcc.yaml δεν είναι προς επεξεργασία από εδώ."}, "messaging": {"description": "Λάβετε μηνύματα σχετικά με τις συνεδρίες φόρτισης.", "title": "Ειδοποιήσεις"}, "meter": {"cancel": "Ακύρωση", "delete": "Διαγραφή", "generic": "Γενι<PERSON><PERSON>ς ενσωματώσεις", "option": {"aux": "Προσθέστε τον αυτορυθμιζόμενο καταναλωτή", "battery": "Προσθήκη μετρητή μπαταρίας", "ext": "Προσθήκη εξωτερικού μετρητή", "pv": "Προσθήκη ηλιακού μετρητή"}, "save": "Αποθήκευση", "specific": "Ειδικές ενσωματώσεις", "template": "Κατα<PERSON>κ<PERSON><PERSON><PERSON><PERSON>τής", "titleChoice": "Τι θέλετε να προσθέσετε;", "validateSave": "Επικύρωση & αποθήκευση"}, "modbus": {"baudrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baud", "comset": "ComSet", "connection": "Σύνδεση Modbus", "connectionHintSerial": "Η συσκευή συνδέεται απευθείας με το evcc μέσω διεπαφής RS485.", "connectionHintTcpip": "Η συσκευή είναι διευθυνσιοδοτούμενη από evcc μέσω LAN / Wifi.", "connectionValueSerial": "Σειριακή / USB", "connectionValueTcpip": "Δίκτυο", "device": "Όνομα συσκευής", "deviceHint": "Παράδειγμα: /dev/ttyUSB0", "host": "Διεύθυνση IP ή όνομα κεντρικού υπολογιστή", "hostHint": "Παράδειγμα: *********", "id": "Αναγνωριστικό Modbus", "port": "Θύρα", "protocol": "Πρωτόκ<PERSON><PERSON><PERSON><PERSON> Modbus", "protocolHintRtu": "Σύνδεση μέσω προσαρμογέα RS485 σε Ethernet χωρίς μετάφραση πρωτοκόλλου.", "protocolHintTcp": "Η συσκευή διαθέτει εγγενή υποστήριξη LAN/Wi-Fi ή είναι συνδεδεμένη μέσω προσαρμογέα RS485 σε Ethernet με μετάφραση πρωτοκόλλου.", "protocolValueRtu": "RTU", "protocolValueTcp": "TCP"}, "modbusproxy": {"description": "Να επιτρέπεται σε πολλούς πελάτες να έχουν πρόσβαση σε μία συσκευή Modbus.", "title": "Διακομιστής μεσολάβησης <PERSON>"}, "mqtt": {"authentication": "Έλεγχος ταυτότητας", "description": "Συνδεθείτε σε ένα MQTT broker για ανταλλάγη δεδομένων με άλλα συστήματα στο δίκτυό σας.", "descriptionClientId": "Αποστολέας των μηνυμάτων. <PERSON><PERSON>ν μείνει κενό θα γίνει χρήση του `evcc-[τυχαίο]`.", "descriptionTopic": "Αφήστε το κενό για να απενεργοποιήσετε δημοσιεύσεις.", "labelBroker": "Broker", "labelCaCert": "Πιστοποιητικ<PERSON> διακομιστή (CA)", "labelCheckInsecure": "Να επιτρέπονται τα πιστοποιητικ<PERSON> self-signed", "labelClientCert": "Πιστοποιητικ<PERSON> πελάτη", "labelClientId": "ID πελάτη", "labelClientKey": "Κλειδί πελάτη", "labelInsecure": "Επικύρωση πιστοποιητικού", "labelPassword": "Κω<PERSON>ικ<PERSON>ς", "labelTopic": "Θέμα", "labelUser": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "publishing": "Δημοσιεύσεις", "title": "MQTT"}, "network": {"descriptionHost": "Κάντε χρήση του επιθέματος .local για να μπορεί να γίνει χρήση του mDNS. Σχετικό για την ανακάλυψη της εφαρμογής για κινητά και ορισμένων φορτιστών OCPP.", "descriptionPort": "Θύρα για τη διεπαφή ιστού (web-UI) και το API. Θα χρειαστεί να ενημερώσετε τη διεύθυνση URL του προγράμματος περιήγησής σας, εάν το αλλάξετε.", "descriptionSchema": "Επηρεάζει μόνο τον τρόπο δημιουργίας των διευθύνσεων URL. Η επιλογή HTTPS δεν θα ενεργοποιήσει την κρυπτογράφηση.", "labelHost": "Hostname", "labelPort": "Θύρα", "labelSchema": "Σχήμα", "title": "Δίκτυο"}, "options": {"boolean": {"no": "όχι", "yes": "ναι"}, "endianness": {"big": "big-endian", "little": "little-endian"}, "operationMode": {"heating": "Θέρμανση", "standby": "Αναμονή"}, "schema": {"http": "HTTP (χωρ<PERSON>ς κρυπτογράφηση)", "https": "HTTPS (με κρυπτογράφηση)"}, "status": {"A": "A (δεν είναι συνδεδεμένο)", "B": "B (συνδεδεμένο)", "C": "C (φορτίζει)"}}, "pv": {"titleAdd": "Προσθήκη Μετρητή Φ/Β", "titleEdit": "Επεξεργασία Μετρητή Φ/Β"}, "section": {"additionalMeter": "Επιπλέον μετρητές", "general": "Γενικά", "grid": "Δίκτυο ενέργειας", "integrations": "Ενσωματώσεις", "loadpoints": "Φόρτιση και Θέρμανση", "meter": "Φ/Β και μπαταρία", "system": "Σύστημα", "vehicles": "Οχήματα"}, "sponsor": {"addToken": "Εισαγάγετε το token", "changeToken": "Αλλαγή του token", "description": "Το μοντέλο χορηγίας μας βοηθά να διατηρήσουμε το έργο και να δημιουργήσουμε με βιώσιμο τρόπο νέες και συναρπαστικές λειτουργίες. Ως χορηγός έχετε πρόσβαση σε όλες τις εφαρμογές φορτιστή.", "descriptionToken": "Θα λάβετε το token από το {url}. Προσφέρουμε επίσης ένα προσωρινό token για δοκιμή.", "error": "Το sponsor token δεν είναι έγκυρο .", "labelToken": "Sponsor token", "title": "Χορηγία", "tokenRequired": "Πρέπει να εισάγετε ένα sponsor token για να μπορέσετε να δημιουργήσετε αυτή τη συσκευή.", "tokenRequiredLearnMore": "Μάθετε περισσότερα.", "trialToken": "Δοκιμαστικό token"}, "system": {"backupRestore": {"backup": {"action": "Λή<PERSON>η αντιγράφου ασφαλείας...", "confirmationButton": "<PERSON>ήψ<PERSON> αντιγράφου ασφαλείας", "confirmationText": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τον κωδικ<PERSON> πρόσβασής σας για να κάνετε λήψη του αρχείου βάσης δεδομένων.", "description": "Δημιουργία αντίγραφου ασφάλειας των δεδομένων σας σε ένα αρχείο. Αυτό το αρχείο μπορεί να χρησιμοποιηθεί για την επαναφορά των δεδομένων σας σε περίπτωση βλάβης του συστήματος.", "title": "Αντίγραφο ασφάλειας"}, "cancel": "Ακύρωση", "description": "Δημιουργ<PERSON>α αντίγραφου ασφάλειας, επα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και αρχικοποίηση των δεδομένων σας. Χρή<PERSON>ι<PERSON><PERSON> εάν θέλετε να μεταφέρετε τα δεδομένα σας σε άλλο σύστημα.", "note": "Σημείωση: Όλες οι παραπάνω ενέργειες επηρεάζουν μόνο τη βάση δεδομένων. Το αρχείο διαμόρφωσης evcc.yaml παραμένει αμετάβλητο.", "reset": {"action": "Αρχικοποίηση...", "confirmationButton": "Αρχικοποίηση και επανεκκίνηση", "confirmationText": "Αυτό θα διαγράψει οριστικά τα δεδομένα που έχετε επιλέξει. Βεβαιωθείτε πρώτα ότι έχετε κατεβάσει ένα αντίγραφο ασφαλείας.", "description": "Αντιμετωπίζετε προβλήματα με τη διαμόρφωση και θέλετε να ξεκινήσετε από την αρχή; Διαγράψτε όλα τα δεδομένα και ξεκινήστε από την αρχή.", "sessions": "Συνεδρίες φόρτισης", "sessionsDescription": "Διαγράφει το ιστορικό των συνεδριών φόρτισης.", "settings": "Διαμόρφωση και ρυθμίσεις", "settingsDescription": "Διαγρά<PERSON><PERSON>ι όλες τις διαμορφωμένες συσκευές, υπηρεσίες, προγράμματα κ.λπ.", "title": "Αρχικοποίηση"}, "restore": {"action": "Επαναφορά...", "confirmationButton": "Επαναφ<PERSON>ρ<PERSON> και επανεκκίνηση", "confirmationText": "Αυτό θα αντικαταστήσει ολόκληρη τη βάση δεδομένων σας. Βεβαιωθείτε ότι έχετε κατεβάσει πρώτα ένα αντίγραφο ασφαλείας.", "description": "Επαναφέρετε τα δεδομένα σας από ένα αντίγραφο ασφαλείας. Αυτό θα αντικαταστήσει όλα τα τρέχοντα δεδομένα σας.", "labelFile": "Αρ<PERSON><PERSON><PERSON><PERSON> αντίγραφου ασφάλειας", "title": "Επαναφορά"}, "title": "Δημιουρ<PERSON><PERSON><PERSON> αντίγραφου ασφάλειας και επαναφορά"}, "logs": "Καταγρα<PERSON><PERSON>ς", "restart": "Επανεκκίνηση", "restartRequiredDescription": "Π<PERSON>ρα<PERSON><PERSON><PERSON><PERSON> επανεκκινήστε για να δείτε το αποτέλεσμα.", "restartRequiredMessage": "Άλλαξε η διαμόρφωση.", "restartingDescription": "Παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε…", "restartingMessage": "Επανεκκίνηση του evcc."}, "tariffs": {"description": "Καθορίστε ενεργειακά τιμολόγια για να υπολογίσετε το κόστος κάθε φόρτισης.", "title": "Τιμολόγια"}, "title": {"description": "Προβάλλεται στην κεντρική οθόνη και στον φυλλομετρητή.", "label": "Τίτλος", "title": "Επεξεργασία Τίτλου"}, "validation": {"failed": "απέτυχε", "label": "Κατάσταση", "running": "επικύρωση…", "success": "επιτυχία", "unknown": "απροσδιόριστο", "validate": "επικύρωση"}, "vehicle": {"cancel": "Ακύρωση", "chargingSettings": "Ρυθμίσεις φόρτισης", "defaultMode": "Προεπιλεγμένη λειτουργία", "defaultModeHelp": "Λειτου<PERSON><PERSON><PERSON><PERSON> φόρτισης κατά τη σύνδεση του οχήματος.", "delete": "Διαγραφή", "generic": "Άλλες ενσωματώσεις", "identifiers": "Αναγνωριστικά RFID", "identifiersHelp": "Κατάλο<PERSON>ος συμβολο<PERSON>ε<PERSON>ρών RFID για την αναγνώριση του οχήματος. Μία καταχώρηση ανά γραμμή. Μπορείτε να βρείτε το τρέχον αναγνωριστικό στο αντίστοιχο σημείο φόρτισης στη σελίδα επισκόπησης.", "maximumCurrent": "Μέγιστο ρεύμα", "maximumCurrentHelp": "Πρέπει να είναι μεγαλύτερο από το ελάχιστο ρεύμα.", "maximumPhases": "Μέγιστες φάσεις", "maximumPhasesHelp": "Με πόσες φάσεις μπορεί να φορτίσει αυτό το όχημα; Χρησιμοποιείται για τον υπολογισμό του απαιτούμενου ελάχιστου ηλιακού πλεονάσματος και της διάρκειας του σχεδίου.", "minimumCurrent": "Ελάχιστο ρεύμα", "minimumCurrentHelp": "Πηγαίνετε κάτω από το 6Α μόνο αν ξέρετε τι κάνετε.", "online": "Οχήματα με διαδικτυακό API", "primary": "Γενι<PERSON><PERSON>ς ενσωματώσεις", "priority": "Προτεραιότητα", "priorityHelp": "Υψηλότερη προτεραιότητα σημαίνει ότι αυτό το όχημα έχει προτιμώμενη πρόσβαση στο ηλιακό πλεόνασμα.", "save": "Αποθήκευση", "scooter": "Σκούτερ", "template": "Κατα<PERSON>κ<PERSON><PERSON><PERSON><PERSON>τής", "titleAdd": "Προσθήκη Οχήματος", "titleEdit": "Επεξεργασία Οχήματος", "validateSave": "Επικύρωση και αποθήκευση"}}, "footer": {"community": {"greenEnergy": "Φ/Β", "greenEnergySub1": "φόρτιση με το evcc", "greenEnergySub2": "από τον Οκτώβριο του 2022", "greenShare": "Μερίδιο Φ/Β", "greenShareSub1": "παροχ<PERSON> ισχύος από", "greenShareSub2": "Φ/Β και απόθεμα μπαταρίας", "power": "Ισχ<PERSON>ς φόρτισης", "powerSub1": "{activeClients} από {totalClients} συμμετέχοντες", "powerSub2": "φορτίζει…", "tabTitle": "Κοινότητα live"}, "savings": {"co2Saved": "{value} αποθηκεύτηκε", "co2Title": "Εκπομπές CO₂", "configurePriceCo2": "Μάθετε πώς να διαμορφώνετε δεδομένα τιμής και CO₂.", "footerLong": "{percent} ενέργεια Φ/Β", "footerShort": "{percent} Φ/Β", "modalTitle": "Επισκόπηση Ενέργειας Φόρτισης", "moneySaved": "εξοικονομήθηκαν {value}", "percentGrid": "{grid} kWh δικτύου", "percentSelf": "{self} kWh Φ/Β", "percentTitle": "Ηλιακή Φ/Β Ενέργεια", "period": {"30d": "Τελευταίες 30 ημέρες", "365d": "τελευταίες 365 ημέρες", "thisYear": "φέτος", "total": "από πάντα"}, "periodLabel": "Περίοδος:", "priceTitle": "Τιμή ενέργειας", "referenceGrid": "δίκτυο", "referenceLabel": "Δεδομένα αναφοράς:", "tabTitle": "Τα δεδομένα μου"}, "sponsor": {"becomeSponsor": "Γίνε<PERSON><PERSON> χορηγός", "becomeSponsorExtended": "Υποστηρίξτε μας με άμεσο τρόπο και λάβετε αυτοκόλλητα.", "confetti": "Είστε έτοιμοι για κομφετί;", "confettiPromise": "Παίρνετε αυτοκόλλητα και ψηφιακό κομφετί", "sticker": "… ή αυτοκόλλητα evcc;", "supportUs": "Η αποστολή μας είναι να κάνουμε κανόνα την ηλιακή φόρτιση. Βοηθήστε το evcc δίνοντας ότι νομίζετε οτι αξίζει για εσάς.", "thanks": "Ευχαριστούμε, {χορηγός}! Η συνεισφορά σας βοηθά στην περαιτέρω ανάπτυξη του evcc.", "titleNoSponsor": "Στηρίξτε μας", "titleSponsor": "Είστε υποστηρικτής", "titleTrial": "Δοκιμαστική λειτουργία", "titleVictron": "Χορηγία της Victron Energy", "trial": "Είστε σε δοκιμαστική λειτουργία και μπορείτε να κάνετε χρήση όλων των δυνατοτήτων. Εξετάστε το ενδεχόμενο να το στηρίξετε μέσω χορηγίας.", "victron": "Χρησιμοποιείτε το evcc με συσκευές Victron Energy και έχετε πρόσβαση σε όλες τις δυνατότητες."}, "telemetry": {"optIn": "Θέλω να συνεισφέρω τα δεδομένα μου.", "optInMoreDetails": "Περισσότερες λεπτομέρειες {0}.", "optInMoreDetailsLink": "εδώ", "optInSponsorship": "Απαιτείτ<PERSON>ι χορηγία."}, "version": {"availableLong": "διαθέσιμη νέα έκδοση", "modalCancel": "Ακύρωση", "modalDownload": "Λή<PERSON>η", "modalInstalledVersion": "Εγκατεστημένη έκδοση", "modalNoReleaseNotes": "Δεν υπάρχουν διαθέσιμες σημειώσεις έκδοσης. Περισσότερες πληροφορίες σχετικά με τη νέα έκδοση:", "modalTitle": "Διατίθεται νέα έκδοση", "modalUpdate": "Εγκατάσταση", "modalUpdateNow": "Εγκατάσταση τώρα", "modalUpdateStarted": "Εκκίνηση της νέας έκδοσης του evcc…", "modalUpdateStatusStart": "Ξεκίνησε η εγκατάσταση:"}}, "forecast": {"co2": {"average": "<PERSON><PERSON><PERSON><PERSON> όρ<PERSON>", "lowestHour": "«Πιο καθαρή ώρα»", "range": "'Ε<PERSON><PERSON><PERSON>"}, "modalTitle": "Πρόγνωση", "price": {"average": "<PERSON><PERSON><PERSON><PERSON> όρ<PERSON>", "lowestHour": "Πιο φθηνή ώρα", "range": "'Ε<PERSON><PERSON><PERSON>"}, "solar": {"dayAfterTomorrow": "Μεθαύριο", "partly": "μερικ<PERSON>ς", "remaining": "Απομένει", "today": "Σήμερα", "tomorrow": "Αύριο"}, "solarAdjust": "Προσαρμόστε την ηλιακή πρόβλεψη με βάση τα πραγματικά δεδομένα παραγωγής{τοις εκατό}.", "type": {"co2": "CO₂", "price": "Τιμή", "solar": "Ηλιακή"}}, "header": {"about": "Σχετικά", "blog": "Blog", "docs": "Τεκμηρίωση", "github": "GitHub", "login": "Συνδέσεις οχημάτων", "logout": "Αποσύνδεση", "nativeSettings": "Αλλαγή διακομιστή", "needHelp": "Χρειάζ<PERSON>στε Βοήθεια;", "sessions": "Συνεδρίες φόρτισης"}, "help": {"discussionsButton": "Συζητ<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentationButton": "Τεκμηρίωση", "issueButton": "Αναφορά σφάλματος", "issueDescription": "Βρήκατε μια παράξενη ή λάθος συμπεριφορά;", "logsButton": "Προβολή αρχείων καταγραφής", "logsDescription": "Ελέγξτε τα αρχεία καταγραφής για σφάλματα.", "modalTitle": "Χρειάζ<PERSON>στε βοήθεια;", "primaryActions": "Κάτι δεν λειτουργεί όπως θα έπρεπε; Αυτά είναι καλά μέρη για να λάβετε βοήθεια.", "restart": {"cancel": "Ακύρωση", "confirm": "Ναι, επανεκκίνηση!", "description": "Υπό κανονικές συνθήκες η επανεκκίνηση δε θα πρέπει να είναι απαραίτητη. Εξετάστε το ενδεχόμενο να αναφέρετε σφάλμα εάν χρειάζεται να κάνετε επανεκκίνηση του evcc σε τακτική βάση.", "disclaimer": "Σημείωση: το evcc θα τερματιστε<PERSON> και θα βασιστεί στο λειτουργικό σύστημα για την επανεκκίνηση της υπηρεσίας.", "modalTitle": "Είστε βέβαιοι ότι θέλετε να κάνετε επανεκκίνηση;"}, "restartButton": "Επανεκκίνηση", "restartDescription": "Δοκιμάσατε να το απενεργοποιήσετε και να το ενεργοποιήσετε ξανά;", "secondaryActions": "Ακόμα δε μπορείτε να λύσετε το πρόβλημά σας; Ακολουθούν ορισμένες πιο σοβαρές επιλογές."}, "log": {"areaLabel": "Φίλτρο τομέα", "areas": "Όλοι οι τομείς", "download": "<PERSON>ή<PERSON><PERSON> πλήρους αρχείου καταγραφής", "levelLabel": "Φίλτρ<PERSON> επιπέδου αρχείου καταγραφής", "nAreas": "{count} τομείς", "noResults": "Δεν υπάρχουν αντίστοιχες εγγραφές στο ημερολόγιο.", "search": "Αναζήτηση", "selectAll": "επιλογ<PERSON>λων", "showAll": "Εμφάνιση όλων των καταχωρήσεων", "title": "Καταγρα<PERSON><PERSON>ς", "update": "Αυτόματη ενημέρωση"}, "loginModal": {"cancel": "Άκυρο", "demoMode": "Η σύνδεση χρήστη δεν υποστηρίζεται σε κατάσταση επίδειξης.", "error": "Η σύνδεση απέτυχε: ", "iframeHint": "Ανοίξτε το evcc σε νέα καρτέλα.", "iframeIssue": "Ο κωδικός πρόσβασής είναι σωστός, αλλά το πρόγραμμα περιήγησής φαίνεται να έχει χάσει το cookie ελέγχου ταυτότητας. Αυτό μπορεί να συμβεί εάν εκτελέσετε το evcc σε ένα iframe μέσω HTTP.", "invalid": "Ο κωδικός πρόσβασης δεν είναι έγκυρος.", "login": "Σύνδεση", "password": "Κωδικ<PERSON><PERSON> Διαχειριστή", "reset": "Επανα<PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης;", "title": "Πιστοποίηση"}, "main": {"chargingPlan": {"active": "Ενεργό", "addRepeatingPlan": "Προσθήκη επαναλαμβανόμενου προγράμματος", "arrivalTab": "Άφιξη", "day": "Ημέρα", "departureTab": "Αναχώρηση", "goal": "Επιθυμητή φόρτιση", "modalTitle": "Πρόγραμμα Φόρτισης", "none": "κανένα", "planNumber": "Πρόγραμμα {number}", "preconditionDescription": "Φορτίστε {διάρκεια} πριν από την αναχώρηση για προετοιμασία μπαταρίας.", "preconditionLong": "Καθυστερημένη φόρτιση", "preconditionOptionAll": "Όλα", "preconditionOptionNo": "Όχι", "preconditionShort": "Αργά", "remove": "Αφαίρεση", "repeating": "επαναλαμβανόμενο", "repeatingPlans": "Επαναλαμβανόμενα προγράμματα", "selectAll": "Επιλογ<PERSON> όλων", "time": "Ώρα", "title": "Πρόγραμμα", "titleMinSoc": "Ελάχιστη φόρτιση", "titleTargetCharge": "Αναχώρηση", "unsavedChanges": "Υπάρχουν μη αποθηκευμένες αλλαγές. Υποβολή τώρα;", "update": "Υποβολή", "weekdays": "Ημέρες"}, "energyflow": {"battery": "Μπαταρία", "batteryCharge": "Φόρτιση μπαταρίας", "batteryDischarge": "Αποφόρτιση μπαταρίας", "batteryGridChargeActive": "ενεργή φόρτιση από το δίκτυο", "batteryGridChargeLimit": "φόρτιση από το δίκτυο <PERSON>αν", "batteryHold": "Μπαταρία (κλειδωμένο)", "batteryTooltip": "{energy} από {total} ({soc})", "forecastTooltip": "πρόβλεψη: εναπομένουσα ηλιακή παραγωγή σήμερα", "gridImport": "Χρήση δικτύου", "homePower": "Κατανάλωση", "loadpoints": "Φορτιστής| Φορτιστής | {count} φορτιστές", "noEnergy": "<PERSON><PERSON><PERSON><PERSON><PERSON> δεδομένα μετρητή", "pv": "Ηλιακό σύστημα", "pvExport": "Εξαγωγή προς δικτύο", "pvProduction": "Παραγωγή", "selfConsumption": "«Αυτοκατανάλωση»"}, "heatingStatus": {"charging": "Θερμαίνεται…", "connected": "Αναμονή.", "vehicleLimit": "Όριο θερμαντήρα", "waitForVehicle": "Έτοιμο. Αναμονή για θερμαντήρα…"}, "loadpoint": {"avgPrice": "⌀ Τιμή", "charged": "Φορτίστηκε", "co2": "⌀ CO₂", "duration": "Διάρκεια", "fallbackName": "Σημ<PERSON><PERSON><PERSON> φόρτισης", "finished": "<PERSON>ρ<PERSON><PERSON>ος τερματισμού", "power": "Ισχύς", "price": "Κόστος", "remaining": "Υπόλοιπο", "remoteDisabledHard": "{source}: απενεργοποιημένο", "remoteDisabledSoft": "{source}: απενεργοποιημένη προσαρμοζόμενη ηλιακή Φ/Β φόρτιση", "solar": "Φ/Β"}, "loadpointSettings": {"batteryBoost": {"description": "Γρήγορη φόρτιση από οικιακή μπαταρία.", "label": "Ενίσχυση από μπαταρία", "mode": "Διαθέσιμο μόνο σε λειτουργία Φ/Β ή Ελαχ+Φ/Β.", "once": "Ενεργή ενίσχυση για αυτή τη συνεδρία φόρτισης."}, "batteryUsage": "Οικιακή Μπαταρία", "currents": "Ένταση φόρτισης", "default": "προεπιλογή", "disclaimerHint": "Σημείωση:", "limitSoc": {"description": "Όριο φόρτισης που χρησιμοποιείτ<PERSON><PERSON> όταν αυτό το όχημα είναι συνδεδεμένο.", "label": "Προεπιλεγμένο όριο"}, "maxCurrent": {"label": "Μέγιστη Ένταση"}, "minCurrent": {"label": "Ελάχιστη Ένταση"}, "minSoc": {"description": "Το όχημα φορτίζ<PERSON><PERSON><PERSON><PERSON> (γρήγ<PERSON><PERSON><PERSON>) μέχρι {0} σε λειτουργία ηλιακής Φ/Β ενέργειας. Μετά συνεχίζει μόνο με το πλεόνασμα Φ/Β ενέργειας. Χρήσιμο για να εξασφαλίσει μια ελάχιστη εμβέλεια ακόμη και για πιο συννεφιασμένες ημέρες.", "label": "Ελ. % φόρτισης"}, "onlyForSocBasedCharging": "Αυτές οι επιλογές είναι διαθέσιμες μόνο για οχήματα με γνωστό επίπεδο φόρτισης.", "phasesConfigured": {"label": "Φάσεις", "no1p3pSupport": "Πώς είναι συνδεδεμένος ο φορτιστής σας;", "phases_0": "αυτόματη εναλλαγή", "phases_1": "1 φάση", "phases_1_hint": "({min} έως {max})", "phases_3": "3 φάσεις", "phases_3_hint": "({min} έως {max})"}, "smartCostCheap": "Φτηνή φόρτιση δικτύου", "smartCostClean": "Περιβαλλοντική φόρτιση δικτύου", "title": "Ρυθμίσεις {0}", "vehicle": "Όχημα"}, "mode": {"minpv": "Ελαχ+Φ/Β", "now": "Ταχύ", "off": "Κλειστό", "pv": "Φ/Β", "smart": "Έξυπνο"}, "provider": {"login": "σύνδεση", "logout": "αποσύνδεση"}, "startConfiguration": "Ας ξεκινήσουμε τη διαμόρφωση", "targetCharge": {"activate": "Ενεργοποίηση", "co2Limit": "Όριο CO₂ από {co2}", "costLimitIgnore": "Το διαμορφωμένο {limit} θα αγνοηθεί κατά τη διάρκεια αυτής της περιόδου.", "currentPlan": "ενεργό πρόγραμμα", "descriptionEnergy": "Μέχρι πότε θα πρέπει το {targetEnergy} να φορτίζει το όχημα;", "descriptionSoc": "Πότε πρέπει να φορτιστεί το όχημα μέχρι {targetSoc};", "goalReached": "Ο στόχος έχει ήδη επιτευχθεί", "inactiveLabel": "Στόχευση ώρας", "nextPlan": "Επόμενο πρόγραμμα", "notReachableInTime": "Ο στόχος θα επιτευχθεί {overrun} αργότερα.", "onlyInPvMode": "Το πρόγραμμα φόρτισης λειτουργεί μόνο σε ηλιακή Φ/Β λειτουργία.", "planDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON> φόρτισης", "planPeriodLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planPeriodValue": "{start} έως {end}", "planUnknown": "δεν είναι γνωστό ακόμα", "preview": "Προεπισκόπηση προγράμματος", "priceLimit": "όριο τιμής του {price}", "remove": "Αφαίρεση", "setTargetTime": "κανένα", "targetIsAboveLimit": "Το οριζόμενο όριο φόρτισης {limit} θα αγνοηθεί κατά τη διάρκεια αυτής της περιόδου.", "targetIsAboveVehicleLimit": "Το όριο του οχήματος είναι χαμηλότερο από το στόχο φόρτισης.", "targetIsInThePast": "Διάλεξε μια στιγμή στο μέλλον, <PERSON>.", "targetIsTooFarInTheFuture": "Θα αναπροσαρμόσουμε το πρόγραμμα μόλις μάθουμε περισσότερα για το μέλλον.", "title": "Στόχευση ώρας", "today": "σήμερα", "tomorrow": "αύριο", "update": "Ενημέρωση", "vehicleCapacityDocs": "Μάθετε πώς να το ρυθμίσετε.", "vehicleCapacityRequired": "Απαιτε<PERSON><PERSON><PERSON><PERSON> η χωρητικότητα της μπαταρίας του οχήματος για την εκτίμηση του χρόνου φόρτισης."}, "targetChargePlan": {"chargeDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON> φόρτισης", "co2Label": "εκπομπές CO2 ⌀", "priceLabel": "Τιμή ενέργειας", "timeRange": "{day} {range} ω", "unknownPrice": "άγνωστο ακόμα"}, "targetEnergy": {"label": "Όριο", "noLimit": "κανένα"}, "vehicle": {"addVehicle": "Προσθήκη οχήματος", "changeVehicle": "Αλλαγ<PERSON> οχήματος", "detectionActive": "Προσδιορισμός οχήματος…", "fallbackName": "Όχημα", "moreActions": "Περισσότερες ενέργειες", "none": "Κανένα όχημα", "notReachable": "Το όχημα δεν ήταν ανιχνεύσιμο. Δοκιμάστε να επανεκκινήσετε το evcc.", "targetSoc": "Όριο", "temp": "Θερμ.", "tempLimit": "Θερμ. όριο", "unknown": "Όχημα επισκέπτη", "vehicleSoc": "Φορτισμένο"}, "vehicleStatus": {"awaitingAuthorization": "Αναμονή για εξουσιοδότηση.", "batteryBoost": "Ενεργή ενίσχυση από μπαταρία.", "charging": "Φορτίζει…", "cheapEnergyCharging": "Διαθέσιμη φτηνή ενέργεια.", "cheapEnergyNextStart": "Φτηνή ενέργεια σε {duration}.", "cheapEnergySet": "Ορίστηκε το όριο τιμής.", "cleanEnergyCharging": "Διαθέσιμη καθαρή ενέργεια.", "cleanEnergyNextStart": "Πράσινη ενέργεια σε {duration}.", "cleanEnergySet": "Ορίστηκε το όριο CO₂.", "climating": "Εντοπίστηκε προκλιματισμός.", "connected": "Συνδέθηκε.", "disconnectRequired": "Η συνεδρία τερματίστηκε. Συνδεθείτε ξανά.", "disconnected": "Αποσυνδεδεμένο.", "feedinPriorityNextStart": "H υψηλή τιμή feed-in ξεκινά σε {διάρκεια}.", "feedinPriorityPausing": "Η ηλιακή φόρτιση διακόπηκε για μεγιστοποίηση του feed-in.", "finished": "Τελείωσε.", "minCharge": "Ελάχιστη φόρτιση μέχρι {soc}.", "pvDisable": "Δεν υπάρχει αρκετό πλεόνασμα. Σε λίγο θα σταματήσει.", "pvEnable": "Διαθέσιμο πλεόνασμα. Θα ξεκινήσει σύντομα.", "scale1p": "Σε λίγο θα γίνει μετάπτωση σε μονοφασική φόρτιση.", "scale3p": "Σε λίγο θα γίνει επαύξηση σε τριφασική φόρτιση.", "targetChargeActive": "Ενεργό πρόγραμμα φόρτισης. Εκτίμηση ολοκλήρωσης σε {duration}.", "targetChargePlanned": "Το πρόγραμμα θα ξεκινήσει τη φόρτιση σε {duration}.", "targetChargeWaitForVehicle": "Είναι σε ετοιμότητα το πρόγραμμα φόρτισης. Αναμονή για το όχημα…", "vehicleLimit": "Όριο οχήματος", "vehicleLimitReached": "Έχει φτάσει στο όριο του οχήματος.", "waitForVehicle": "Έτοιμο. Αναμονή για όχημα…", "welcome": "Σύντομη αρχική φόρτιση για επιβεβαίωση της σύνδεσης."}, "vehicles": "Στάθμευση", "welcome": "Γεια σας!"}, "notifications": {"dismissAll": "Απόρριψη όλων", "logs": "Προβολή πλήρους καταγραφής", "modalTitle": "Ειδοποιήσεις"}, "offline": {"configurationError": "Σφάλμα κατά την εκκίνηση. Ελέγξτε τις ρυθμίσεις παραμέτρων και επανεκκινήστε.", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεση με διακομιστή.", "restart": "Επανεκκίνηση", "restartNeeded": "Απαιτείται για την εφαρμογή των αλλαγών.", "restarting": "Ο διακομιστής θα είναι διαθέσιμος σε λίγο."}, "passwordModal": {"description": "Ορίστε έναν κωδικό πρόσβασης για την προστασία των ρυθμίσεων διαμόρφωσης. Η χρήση της κύριας οθόνης εξακολουθεί να είναι δυνατή χωρίς σύνδεση.", "empty": "Ο κωδικός πρόσβασης δεν πρέπει να είναι κενός", "error": "Σφάλμα: ", "labelCurrent": "Τρέχων κωδικός", "labelNew": "<PERSON><PERSON><PERSON> κωδικός", "labelRepeat": "Επανάληψη κωδικού", "newPassword": "Δημιουργ<PERSON><PERSON> κωδικού", "noMatch": "Οι κωδικοι δεν ταιριάζουν", "titleNew": "Ορισ<PERSON><PERSON><PERSON>ωδικού Διαχειριστή", "titleUpdate": "Ενημέρωση Κωδικού Διαχειριστή", "updatePassword": "Ενημέρωση κωδικού"}, "session": {"cancel": "Άκυρο", "co2": "CO₂", "date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Διαγραφή", "finished": "Τελείωσε", "meter": "Μετρητής", "meterstart": "Αρχή μετρητή", "meterstop": "Τ<PERSON><PERSON><PERSON> μετρητή", "odometer": "Οδόμετρο", "price": "Τιμή", "started": "Ξεκίνησε", "title": "Συνεδρία φόρτισης"}, "sessions": {"avgPower": "⌀ Ισχύς", "avgPrice": "⌀ Τιμή", "chargeDuration": "Διάρκεια", "chartTitle": {"avgCo2ByGroup": "⌀ CO₂ {byGroup}", "avgPriceByGroup": "⌀ Τιμή {byGroup}", "byGroupLoadpoint": "κατά Σημείο Φόρτισης", "byGroupVehicle": "κατά Όχημα", "energy": "Φορτισμένη Ενέργεια", "energyGrouped": "Ηλιακή Φ/Β vs. Ενέργε<PERSON><PERSON><PERSON> Δικτύου", "energyGroupedByGroup": "Ενέργεια {byGroup}", "energySubSolar": "{value} ηλιακή", "energySubTotal": "{value} σύνολο", "groupedCo2ByGroup": "Ποσότητα CO₂ {byGroup}", "groupedPriceByGroup": "Συνολικ<PERSON> {byGroup}", "historyCo2": "Εκπομπές CO₂", "historyCo2Sub": "{value} σύνολο", "historyPrice": "Κόστη Φορτίσεων", "historyPriceSub": "{value} σύνολο", "solar": "Ηλιακό Μερίδιο σε Ετήσια βάση", "solarByGroup": "Ηλιακό Μερίδιο {byGroup}"}, "co2": "⌀ CO₂", "csv": {"chargedenergy": "Ενέργεια (kWh)", "chargeduration": "Διάρκεια", "co2perkwh": "CO₂/kWh", "created": "Δημιουργήθηκε", "finished": "Τελείωσε", "identifier": "Αναγνωριστικό", "loadpoint": "Σημ<PERSON><PERSON><PERSON> φόρτισης", "meterstart": "Εκκίνηση μετρητή (kWh)", "meterstop": "Τέλος μετρητή (kWh)", "odometer": "Οδόμετρο (χλμ)", "price": "Τιμή", "priceperkwh": "Τιμή/kWh", "solarpercentage": "Φ/Β (%)", "vehicle": "Όχημα"}, "csvPeriod": "Λήψη CSV {period}", "csvTotal": "Λήψη CSV συνόλων", "date": "Ξεκίνησε", "energy": "Φόρτιση", "filter": {"allLoadpoints": "όλα τα σημεία φόρτισης", "allVehicles": "όλα τα οχήματα", "filter": "Φίλτρο"}, "group": {"co2": "Εκπομπές", "grid": "Πλέγμα", "price": "Τιμή", "self": "Φ/Β"}, "groupBy": {"loadpoint": "Σημ<PERSON><PERSON><PERSON> φόρτισης", "none": "Σύνολο", "vehicle": "Όχημα"}, "loadpoint": "Σημ<PERSON><PERSON><PERSON> φόρτισης", "noData": "Δεν υπάρχουν συνεδρίες φόρτισης αυτό το μήνα.", "overview": "Επισκόπηση", "period": {"month": "Μήνας", "total": "Σύνολο", "year": "Έτος"}, "price": "Κόστος", "reallyDelete": "Θέλετε πραγματικά να διαγράψετε αυτή τη συνεδρία;", "showIndividualEntries": "Εμφάνιση μεμονωμένων συνεδριών", "solar": "Φ/Β", "title": "Συνεδρίες φόρτισης", "total": "Σύνολο", "type": {"co2": "CO₂", "price": "Τιμή", "solar": "Φ/Β"}, "vehicle": "Όχημα"}, "settings": {"fullscreen": {"enter": "Μετάπτωση σε πλήρη οθόνη", "exit": "Έξοδος από πλήρη οθόνη", "label": "Πλή<PERSON><PERSON><PERSON> οθόνη"}, "hiddenFeatures": {"label": "Πειραματικό", "value": "Εμφάνιση πειραματικών δυνατοτήτων διεπαφής χρήστη."}, "language": {"auto": "Αυτόματο", "label": "Γλώσσα"}, "sponsorToken": {"expires": "Το sponsor token λήγει {inXDays}. {getNewToken} και κάντε εδώ μία ενημέρωση.", "getNew": "Πάρτε ένα φρέσκο", "hint": "Σημείωση: Θα το αυτοματοποιήσουμε στο μέλλον."}, "telemetry": {"label": "Τηλεμετρία"}, "theme": {"auto": "συστήματος", "dark": "σκοτεινό", "label": "Σχέδιο προβολής", "light": "φωτεινό"}, "time": {"12h": "12ω", "24h": "24ω", "label": "Μορφή ώρας"}, "title": "Διεπα<PERSON><PERSON> χρήστη", "unit": {"km": "χλμ", "label": "Μονάδες", "mi": "μίλια"}}, "smartCost": {"activeHours": "{active} από {total}", "activeHoursLabel": "Ενεργές ώρες", "applyToAll": "Εφαρμογή παντού;", "batteryDescription": "Φορτίζει την οικιακή μπαταρία με ενέργεια από το δίκτυο.", "cheapTitle": "Φτηνή φόρτιση δικτύου", "cleanTitle": "Φόρτιση δικτύου πράσινης ενέργειας", "co2Label": "εκπομπές CO₂", "co2Limit": "Όριο CO₂", "loadpointDescription": "Επιτρέπει την προσωρινή γρήγορη φόρτιση σε λειτουργία ηλιακής Φ/Β ενέργειας.", "modalTitle": "Έξυπνη φόρτιση δικτύου", "none": "κανένα", "priceLabel": "Τιμή ενέργειας", "priceLimit": "Όριο τιμής", "resetAction": "Κατάργηση ορίου", "resetWarning": "Δεν έχει διαμορφωθεί δυναμική τιμή δικτύου ή πηγή CO₂. Ω<PERSON>τ<PERSON><PERSON><PERSON>, εξακολουθεί να υπάρχει όριο {limit}. Καθαρίστε τη διαμόρφωσή σας;", "saved": "Αποθηκεύτηκε."}, "smartFeedInPriority": {"activeHoursLabel": "Ώρες παύσης", "description": "Διακόπτει τη φόρτιση κατά τη διάρκεια υψηλής τιμολόγησης για να δώσει προτεραιότητα στην επικερδή feed-in τροφοδοσία του δικτύου.", "priceLabel": "<PERSON><PERSON><PERSON><PERSON> Feed-in", "priceLimit": "Όριο Feed-in", "resetWarning": "Δεν έχει διαμορφωθεί δυναμική ταρίφα feed-in. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, εξακολουθεί να υπάρχει ένα όριο {limit}. Θέλετε εκκαθάριση της διαμόρφωσής σας;", "title": "Προτε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Feed-in"}, "startupError": {"configFile": "Αρχεί<PERSON> διαμόρφωσης που χρησιμοποιείται:", "configuration": "Διαμόρφωση", "description": "Ελέγξτε το αρχείο διαμόρφωσής. Εάν το μήνυμα σφάλματος δεν βοηθά, ελέγξτε το {0}.", "discussions": "Συζητ<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixAndRestart": "Διορθώστε το πρόβλημα και επανεκκινήστε το διακομιστή.", "hint": "Σημείωση: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επίσης να έχετε μια ελαττωματική συσκευή (μετατροπέας, μετρητής, ...). Ελέγξτε τις συνδέσεις δικτύου σας.", "lineError": "Σφάλμα στο {0}.", "lineErrorLink": "γραμμή {0}", "restartButton": "Επανεκκίνηση", "title": "Σφάλμα εκκίνησης"}}