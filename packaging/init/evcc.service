# evcc.service
#

[Unit]
Description=evcc
Requires=network-online.target
After=syslog.target network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=10
StartLimitBurst=10

[Service]
AmbientCapabilities=CAP_NET_BIND_SERVICE
ExecStart=/usr/bin/evcc
Environment="EVCC_DATABASE_DSN=/var/lib/evcc/evcc.db"
Restart=always
RestartSec=10

User=evcc
Group=evcc

[Install]
WantedBy=multi-user.target
