// Code generated by "enumer -type BatteryMode -trimprefix Battery -transform=lower"; DO NOT EDIT.

package api

import (
	"fmt"
	"strings"
)

const _BatteryModeName = "unknownnormalholdcharge"

var _BatteryModeIndex = [...]uint8{0, 7, 13, 17, 23}

const _BatteryModeLowerName = "unknownnormalholdcharge"

func (i BatteryMode) String() string {
	if i < 0 || i >= BatteryMode(len(_BatteryModeIndex)-1) {
		return fmt.Sprintf("BatteryMode(%d)", i)
	}
	return _BatteryModeName[_BatteryModeIndex[i]:_BatteryModeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _BatteryModeNoOp() {
	var x [1]struct{}
	_ = x[BatteryUnknown-(0)]
	_ = x[BatteryNormal-(1)]
	_ = x[BatteryHold-(2)]
	_ = x[BatteryCharge-(3)]
}

var _BatteryModeValues = []BatteryMode{BatteryUnknown, BatteryNormal, BatteryHold, BatteryCharge}

var _BatteryModeNameToValueMap = map[string]BatteryMode{
	_BatteryModeName[0:7]:        BatteryUnknown,
	_BatteryModeLowerName[0:7]:   BatteryUnknown,
	_BatteryModeName[7:13]:       BatteryNormal,
	_BatteryModeLowerName[7:13]:  BatteryNormal,
	_BatteryModeName[13:17]:      BatteryHold,
	_BatteryModeLowerName[13:17]: BatteryHold,
	_BatteryModeName[17:23]:      BatteryCharge,
	_BatteryModeLowerName[17:23]: BatteryCharge,
}

var _BatteryModeNames = []string{
	_BatteryModeName[0:7],
	_BatteryModeName[7:13],
	_BatteryModeName[13:17],
	_BatteryModeName[17:23],
}

// BatteryModeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func BatteryModeString(s string) (BatteryMode, error) {
	if val, ok := _BatteryModeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _BatteryModeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to BatteryMode values", s)
}

// BatteryModeValues returns all values of the enum
func BatteryModeValues() []BatteryMode {
	return _BatteryModeValues
}

// BatteryModeStrings returns a slice of all String values of the enum
func BatteryModeStrings() []string {
	strs := make([]string, len(_BatteryModeNames))
	copy(strs, _BatteryModeNames)
	return strs
}

// IsABatteryMode returns "true" if the value is listed in the enum definition. "false" otherwise
func (i BatteryMode) IsABatteryMode() bool {
	for _, v := range _BatteryModeValues {
		if i == v {
			return true
		}
	}
	return false
}
