syntax = "proto3";

// protoc proto/vehicle.proto --go_out=. --go-grpc_out=.

option go_package = "proto/pb";

service Vehicle {
	rpc New (NewRequest) returns (NewReply) {}
	rpc SoC (SoCRequest) returns (SoCReply) {}
}

message NewRequest {
	string token = 1;
	string type = 2;
	map<string,string> config = 3;
}

message NewReply {
	int64 vehicle_id = 1;
}

message SoCRequest {
	string token = 1;
	int64 vehicle_id = 2;
}

message SoCReply {
	double soc = 1;
}
