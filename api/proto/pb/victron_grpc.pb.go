// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.26.1
// source: proto/victron.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Victron_IsValidDevice_FullMethodName = "/Victron/IsValidDevice"
)

// VictronClient is the client API for Victron service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VictronClient interface {
	IsValidDevice(ctx context.Context, in *VictronRequest, opts ...grpc.CallOption) (*VictronReply, error)
}

type victronClient struct {
	cc grpc.ClientConnInterface
}

func NewVictronClient(cc grpc.ClientConnInterface) VictronClient {
	return &victronClient{cc}
}

func (c *victronClient) IsValidDevice(ctx context.Context, in *VictronRequest, opts ...grpc.CallOption) (*VictronReply, error) {
	out := new(VictronReply)
	err := c.cc.Invoke(ctx, Victron_IsValidDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VictronServer is the server API for Victron service.
// All implementations must embed UnimplementedVictronServer
// for forward compatibility
type VictronServer interface {
	IsValidDevice(context.Context, *VictronRequest) (*VictronReply, error)
	mustEmbedUnimplementedVictronServer()
}

// UnimplementedVictronServer must be embedded to have forward compatible implementations.
type UnimplementedVictronServer struct {
}

func (UnimplementedVictronServer) IsValidDevice(context.Context, *VictronRequest) (*VictronReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsValidDevice not implemented")
}
func (UnimplementedVictronServer) mustEmbedUnimplementedVictronServer() {}

// UnsafeVictronServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VictronServer will
// result in compilation errors.
type UnsafeVictronServer interface {
	mustEmbedUnimplementedVictronServer()
}

func RegisterVictronServer(s grpc.ServiceRegistrar, srv VictronServer) {
	s.RegisterService(&Victron_ServiceDesc, srv)
}

func _Victron_IsValidDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VictronRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VictronServer).IsValidDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Victron_IsValidDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VictronServer).IsValidDevice(ctx, req.(*VictronRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Victron_ServiceDesc is the grpc.ServiceDesc for Victron service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Victron_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Victron",
	HandlerType: (*VictronServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsValidDevice",
			Handler:    _Victron_IsValidDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/victron.proto",
}
