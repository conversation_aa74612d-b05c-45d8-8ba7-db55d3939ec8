// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.26.1
// source: proto/victron.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VictronRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"`
	VrmId     string `protobuf:"bytes,2,opt,name=vrmId,proto3" json:"vrmId,omitempty"`
	Serial    string `protobuf:"bytes,3,opt,name=serial,proto3" json:"serial,omitempty"`
	Board     string `protobuf:"bytes,4,opt,name=board,proto3" json:"board,omitempty"`
}

func (x *VictronRequest) Reset() {
	*x = VictronRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_victron_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VictronRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VictronRequest) ProtoMessage() {}

func (x *VictronRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_victron_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VictronRequest.ProtoReflect.Descriptor instead.
func (*VictronRequest) Descriptor() ([]byte, []int) {
	return file_proto_victron_proto_rawDescGZIP(), []int{0}
}

func (x *VictronRequest) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *VictronRequest) GetVrmId() string {
	if x != nil {
		return x.VrmId
	}
	return ""
}

func (x *VictronRequest) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *VictronRequest) GetBoard() string {
	if x != nil {
		return x.Board
	}
	return ""
}

type VictronReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Authorized bool   `protobuf:"varint,1,opt,name=authorized,proto3" json:"authorized,omitempty"`
	Subject    string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
}

func (x *VictronReply) Reset() {
	*x = VictronReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_victron_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VictronReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VictronReply) ProtoMessage() {}

func (x *VictronReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_victron_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VictronReply.ProtoReflect.Descriptor instead.
func (*VictronReply) Descriptor() ([]byte, []int) {
	return file_proto_victron_proto_rawDescGZIP(), []int{1}
}

func (x *VictronReply) GetAuthorized() bool {
	if x != nil {
		return x.Authorized
	}
	return false
}

func (x *VictronReply) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

var File_proto_victron_proto protoreflect.FileDescriptor

var file_proto_victron_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x69, 0x63, 0x74, 0x72, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72, 0x0a, 0x0e, 0x56, 0x69, 0x63, 0x74, 0x72, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x22, 0x48, 0x0a, 0x0c, 0x56, 0x69, 0x63,
	0x74, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x32, 0x3c, 0x0a, 0x07, 0x56, 0x69, 0x63, 0x74, 0x72, 0x6f, 0x6e, 0x12, 0x31,
	0x0a, 0x0d, 0x49, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x0f, 0x2e, 0x56, 0x69, 0x63, 0x74, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x0d, 0x2e, 0x56, 0x69, 0x63, 0x74, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x00, 0x42, 0x0a, 0x5a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_victron_proto_rawDescOnce sync.Once
	file_proto_victron_proto_rawDescData = file_proto_victron_proto_rawDesc
)

func file_proto_victron_proto_rawDescGZIP() []byte {
	file_proto_victron_proto_rawDescOnce.Do(func() {
		file_proto_victron_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_victron_proto_rawDescData)
	})
	return file_proto_victron_proto_rawDescData
}

var file_proto_victron_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_victron_proto_goTypes = []interface{}{
	(*VictronRequest)(nil), // 0: VictronRequest
	(*VictronReply)(nil),   // 1: VictronReply
}
var file_proto_victron_proto_depIdxs = []int32{
	0, // 0: Victron.IsValidDevice:input_type -> VictronRequest
	1, // 1: Victron.IsValidDevice:output_type -> VictronReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_victron_proto_init() }
func file_proto_victron_proto_init() {
	if File_proto_victron_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_victron_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VictronRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_victron_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VictronReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_victron_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_victron_proto_goTypes,
		DependencyIndexes: file_proto_victron_proto_depIdxs,
		MessageInfos:      file_proto_victron_proto_msgTypes,
	}.Build()
	File_proto_victron_proto = out.File
	file_proto_victron_proto_rawDesc = nil
	file_proto_victron_proto_goTypes = nil
	file_proto_victron_proto_depIdxs = nil
}
