package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateKeba(base *Keba, meter func() (float64, error), meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error), identifier func() (string, error), statusReasoner func() (api.Reason, error), phaseSwitcher func(int) error, phaseGetter func() (int, error)) api.Charger {
	switch {
	case identifier == nil && meter == nil && phaseSwitcher == nil && statusReasoner == nil:
		return base

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier != nil && meter == nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier == nil && meter == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.StatusReasoner
		}{
			Keba: base,
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.PhaseSwitcher
		}{
			Keba: base,
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner == nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && statusReasoner != nil:
		return &struct {
			*Keba
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.StatusReasoner
		}{
			Keba: base,
			Identifier: &decorateKebaIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateKebaMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateKebaPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateKebaPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			StatusReasoner: &decorateKebaStatusReasonerImpl{
				statusReasoner: statusReasoner,
			},
		}
	}

	return nil
}

type decorateKebaIdentifierImpl struct {
	identifier func() (string, error)
}

func (impl *decorateKebaIdentifierImpl) Identify() (string, error) {
	return impl.identifier()
}

type decorateKebaMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateKebaMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateKebaMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateKebaMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateKebaPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateKebaPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}

type decorateKebaPhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateKebaPhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateKebaPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateKebaPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}

type decorateKebaStatusReasonerImpl struct {
	statusReasoner func() (api.Reason, error)
}

func (impl *decorateKebaStatusReasonerImpl) StatusReason() (api.Reason, error) {
	return impl.statusReasoner()
}
