// Code generated by "enumer -type ObservationID"; DO NOT EDIT.

package easee

import (
	"fmt"
	"strings"
)

const _ObservationIDName = "SELF_TEST_RESULTSELF_TEST_DETAILSWIFI_EVENTCHARGER_OFFLINE_REASONEASEE_LINK_COMMAND_RESPONSEEASEE_LINK_DATA_RECEIVEDLOCAL_PRE_AUTHORIZE_ENABLEDLOCAL_AUTHORIZE_OFFLINE_ENABLEDALLOW_OFFLINE_TX_FOR_UNKNOWN_IDERRATIC_EVMAX_TOGGLESBACKPLATE_TYPESITE_STRUCTUREDETECTED_POWER_GRID_TYPECIRCUIT_MAX_CURRENT_P1CIRCUIT_MAX_CURRENT_P2CIRCUIT_MAX_CURRENT_P3LOCATIONSITE_IDSTRINGSITE_IDNUMERICLOCK_CABLE_PERMANENTLYIS_ENABLEDCIRCUIT_SEQUENCE_NUMBERSINGLE_PHASE_NUMBERENABLE3_PHASES_DEPRECATEDWI_FI_SSIDENABLE_IDLE_CURRENTPHASE_MODEFORCED_THREE_PHASE_ON_ITWITH_GND_FAULTLED_STRIP_BRIGHTNESSLOCAL_AUTHORIZATION_REQUIREDAUTHORIZATION_REQUIREDREMOTE_START_REQUIREDSMART_BUTTON_ENABLEDOFFLINE_CHARGING_MODELEDMODEMAX_CHARGER_CURRENTDYNAMIC_CHARGER_CURRENTMAX_CURRENT_OFFLINE_FALLBACK_P1MAX_CURRENT_OFFLINE_FALLBACK_P2MAX_CURRENT_OFFLINE_FALLBACK_P3LISTEN_TO_CONTROL_PULSECONTROL_PULSE_RTTCHARGING_SCHEDULEPAIRED_EQUALIZERWI_FI_APENABLEDPAIRED_USER_IDTOKENCIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L1CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L2CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L3CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L1CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L2CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L3NUMBER_OF_CARS_CONNECTEDNUMBER_OF_CARS_CHARGINGNUMBER_OF_CARS_IN_QUEUENUMBER_OF_CARS_FULLY_CHARGEDSOFTWARE_RELEASEICCIDMODEM_FW_IDOTAERROR_CODEMOBILE_NETWORK_OPERATORREBOOT_REASONPOWER_PCBVERSIONCOM_PCBVERSIONREASON_FOR_NO_CURRENTLOAD_BALANCING_NUMBER_OF_CONNECTED_CHARGERSUDPNUM_OF_CONNECTED_NODESLOCAL_CONNECTIONPILOT_MODECAR_CONNECTED_DEPRECATEDSMART_CHARGINGCABLE_LOCKEDCABLE_RATINGPILOT_HIGHPILOT_LOWBACK_PLATE_IDUSER_IDTOKEN_REVERSEDCHARGER_OP_MODEOUTPUT_PHASEDYNAMIC_CIRCUIT_CURRENT_P1DYNAMIC_CIRCUIT_CURRENT_P2DYNAMIC_CIRCUIT_CURRENT_P3OUTPUT_CURRENTDERATED_CURRENTDERATING_ACTIVEDEBUG_STRINGERROR_STRINGERROR_CODETOTAL_POWERSESSION_ENERGYENERGY_PER_HOURLEGACY_EV_STATUSLIFETIME_ENERGYLIFETIME_RELAY_SWITCHESLIFETIME_HOURSDYNAMIC_CURRENT_OFFLINE_FALLBACK_DEPRICATEDUSER_IDTOKENCHARGING_SESSIONCELL_RSSICELL_RATWI_FI_RSSICELL_ADDRESSWI_FI_ADDRESSWI_FI_TYPELOCAL_RSSIMASTER_BACK_PLATE_IDLOCAL_TX_POWERLOCAL_STATEFOUND_WI_FICHARGER_RATCELLULAR_INTERFACE_ERROR_COUNTCELLULAR_INTERFACE_RESET_COUNTWIFI_INTERFACE_ERROR_COUNTWIFI_INTERFACE_RESET_COUNTLOCAL_NODE_TYPELOCAL_RADIO_CHANNELLOCAL_SHORT_ADDRESSLOCAL_PARENT_ADDR_OR_NUM_OF_NODESTEMP_MAXTEMP_AMBIENT_POWER_BOARDTEMP_INPUT_T2TEMP_INPUT_T3TEMP_INPUT_T4TEMP_INPUT_T5TEMP_OUTPUT_NTEMP_OUTPUT_L1TEMP_OUTPUT_L2TEMP_OUTPUT_L3TEMP_AMBIENTLIGHT_AMBIENTINT_REL_HUMIDITYBACK_PLATE_LOCKEDCURRENT_MOTORBACK_PLATE_HALL_SENSORIN_CURRENT_T2IN_CURRENT_T3IN_CURRENT_T4IN_CURRENT_T5IN_VOLT_T1_T2IN_VOLT_T1_T3IN_VOLT_T1_T4IN_VOLT_T1_T5IN_VOLT_T2_T3IN_VOLT_T2_T4IN_VOLT_T2_T5IN_VOLT_T3_T4IN_VOLT_T3_T5IN_VOLT_T4_T5OUT_VOLT_PIN1_2OUT_VOLT_PIN1_3OUT_VOLT_PIN1_4OUT_VOLT_PIN1_5VOLT_LEVEL33VOLT_LEVEL5VOLT_LEVEL12LTE_RSRPLTE_SINRLTE_RSRQEQ_AVAILABLE_CURRENT_P1EQ_AVAILABLE_CURRENT_P2EQ_AVAILABLE_CURRENT_P3CONNECTED_TO_CLOUDCLOUD_DISCONNECT_REASON"
const _ObservationIDLowerName = "self_test_resultself_test_detailswifi_eventcharger_offline_reasoneasee_link_command_responseeasee_link_data_receivedlocal_pre_authorize_enabledlocal_authorize_offline_enabledallow_offline_tx_for_unknown_iderratic_evmax_togglesbackplate_typesite_structuredetected_power_grid_typecircuit_max_current_p1circuit_max_current_p2circuit_max_current_p3locationsite_idstringsite_idnumericlock_cable_permanentlyis_enabledcircuit_sequence_numbersingle_phase_numberenable3_phases_deprecatedwi_fi_ssidenable_idle_currentphase_modeforced_three_phase_on_itwith_gnd_faultled_strip_brightnesslocal_authorization_requiredauthorization_requiredremote_start_requiredsmart_button_enabledoffline_charging_modeledmodemax_charger_currentdynamic_charger_currentmax_current_offline_fallback_p1max_current_offline_fallback_p2max_current_offline_fallback_p3listen_to_control_pulsecontrol_pulse_rttcharging_schedulepaired_equalizerwi_fi_apenabledpaired_user_idtokencircuit_total_allocated_phase_conductor_current_l1circuit_total_allocated_phase_conductor_current_l2circuit_total_allocated_phase_conductor_current_l3circuit_total_phase_conductor_current_l1circuit_total_phase_conductor_current_l2circuit_total_phase_conductor_current_l3number_of_cars_connectednumber_of_cars_chargingnumber_of_cars_in_queuenumber_of_cars_fully_chargedsoftware_releaseiccidmodem_fw_idotaerror_codemobile_network_operatorreboot_reasonpower_pcbversioncom_pcbversionreason_for_no_currentload_balancing_number_of_connected_chargersudpnum_of_connected_nodeslocal_connectionpilot_modecar_connected_deprecatedsmart_chargingcable_lockedcable_ratingpilot_highpilot_lowback_plate_iduser_idtoken_reversedcharger_op_modeoutput_phasedynamic_circuit_current_p1dynamic_circuit_current_p2dynamic_circuit_current_p3output_currentderated_currentderating_activedebug_stringerror_stringerror_codetotal_powersession_energyenergy_per_hourlegacy_ev_statuslifetime_energylifetime_relay_switcheslifetime_hoursdynamic_current_offline_fallback_depricateduser_idtokencharging_sessioncell_rssicell_ratwi_fi_rssicell_addresswi_fi_addresswi_fi_typelocal_rssimaster_back_plate_idlocal_tx_powerlocal_statefound_wi_ficharger_ratcellular_interface_error_countcellular_interface_reset_countwifi_interface_error_countwifi_interface_reset_countlocal_node_typelocal_radio_channellocal_short_addresslocal_parent_addr_or_num_of_nodestemp_maxtemp_ambient_power_boardtemp_input_t2temp_input_t3temp_input_t4temp_input_t5temp_output_ntemp_output_l1temp_output_l2temp_output_l3temp_ambientlight_ambientint_rel_humidityback_plate_lockedcurrent_motorback_plate_hall_sensorin_current_t2in_current_t3in_current_t4in_current_t5in_volt_t1_t2in_volt_t1_t3in_volt_t1_t4in_volt_t1_t5in_volt_t2_t3in_volt_t2_t4in_volt_t2_t5in_volt_t3_t4in_volt_t3_t5in_volt_t4_t5out_volt_pin1_2out_volt_pin1_3out_volt_pin1_4out_volt_pin1_5volt_level33volt_level5volt_level12lte_rsrplte_sinrlte_rsrqeq_available_current_p1eq_available_current_p2eq_available_current_p3connected_to_cloudcloud_disconnect_reason"

var _ObservationIDMap = map[ObservationID]string{
	1:   _ObservationIDName[0:16],
	2:   _ObservationIDName[16:33],
	10:  _ObservationIDName[33:43],
	11:  _ObservationIDName[43:65],
	13:  _ObservationIDName[65:92],
	14:  _ObservationIDName[92:116],
	15:  _ObservationIDName[116:143],
	16:  _ObservationIDName[143:174],
	17:  _ObservationIDName[174:205],
	18:  _ObservationIDName[205:226],
	19:  _ObservationIDName[226:240],
	20:  _ObservationIDName[240:254],
	21:  _ObservationIDName[254:278],
	22:  _ObservationIDName[278:300],
	23:  _ObservationIDName[300:322],
	24:  _ObservationIDName[322:344],
	25:  _ObservationIDName[344:352],
	26:  _ObservationIDName[352:365],
	27:  _ObservationIDName[365:379],
	30:  _ObservationIDName[379:401],
	31:  _ObservationIDName[401:411],
	33:  _ObservationIDName[411:434],
	34:  _ObservationIDName[434:453],
	35:  _ObservationIDName[453:478],
	36:  _ObservationIDName[478:488],
	37:  _ObservationIDName[488:507],
	38:  _ObservationIDName[507:517],
	39:  _ObservationIDName[517:555],
	40:  _ObservationIDName[555:575],
	41:  _ObservationIDName[575:603],
	42:  _ObservationIDName[603:625],
	43:  _ObservationIDName[625:646],
	44:  _ObservationIDName[646:666],
	45:  _ObservationIDName[666:687],
	46:  _ObservationIDName[687:694],
	47:  _ObservationIDName[694:713],
	48:  _ObservationIDName[713:736],
	50:  _ObservationIDName[736:767],
	51:  _ObservationIDName[767:798],
	52:  _ObservationIDName[798:829],
	56:  _ObservationIDName[829:852],
	57:  _ObservationIDName[852:869],
	62:  _ObservationIDName[869:886],
	65:  _ObservationIDName[886:902],
	68:  _ObservationIDName[902:917],
	69:  _ObservationIDName[917:936],
	70:  _ObservationIDName[936:986],
	71:  _ObservationIDName[986:1036],
	72:  _ObservationIDName[1036:1086],
	73:  _ObservationIDName[1086:1126],
	74:  _ObservationIDName[1126:1166],
	75:  _ObservationIDName[1166:1206],
	76:  _ObservationIDName[1206:1230],
	77:  _ObservationIDName[1230:1253],
	78:  _ObservationIDName[1253:1276],
	79:  _ObservationIDName[1276:1304],
	80:  _ObservationIDName[1304:1320],
	81:  _ObservationIDName[1320:1325],
	82:  _ObservationIDName[1325:1336],
	83:  _ObservationIDName[1336:1349],
	84:  _ObservationIDName[1349:1372],
	89:  _ObservationIDName[1372:1385],
	90:  _ObservationIDName[1385:1401],
	91:  _ObservationIDName[1401:1415],
	96:  _ObservationIDName[1415:1436],
	97:  _ObservationIDName[1436:1479],
	98:  _ObservationIDName[1479:1504],
	99:  _ObservationIDName[1504:1520],
	100: _ObservationIDName[1520:1530],
	101: _ObservationIDName[1530:1554],
	102: _ObservationIDName[1554:1568],
	103: _ObservationIDName[1568:1580],
	104: _ObservationIDName[1580:1592],
	105: _ObservationIDName[1592:1602],
	106: _ObservationIDName[1602:1611],
	107: _ObservationIDName[1611:1624],
	108: _ObservationIDName[1624:1645],
	109: _ObservationIDName[1645:1660],
	110: _ObservationIDName[1660:1672],
	111: _ObservationIDName[1672:1698],
	112: _ObservationIDName[1698:1724],
	113: _ObservationIDName[1724:1750],
	114: _ObservationIDName[1750:1764],
	115: _ObservationIDName[1764:1779],
	116: _ObservationIDName[1779:1794],
	117: _ObservationIDName[1794:1806],
	118: _ObservationIDName[1806:1818],
	119: _ObservationIDName[1818:1828],
	120: _ObservationIDName[1828:1839],
	121: _ObservationIDName[1839:1853],
	122: _ObservationIDName[1853:1868],
	123: _ObservationIDName[1868:1884],
	124: _ObservationIDName[1884:1899],
	125: _ObservationIDName[1899:1922],
	126: _ObservationIDName[1922:1936],
	127: _ObservationIDName[1936:1979],
	128: _ObservationIDName[1979:1991],
	129: _ObservationIDName[1991:2007],
	130: _ObservationIDName[2007:2016],
	131: _ObservationIDName[2016:2024],
	132: _ObservationIDName[2024:2034],
	133: _ObservationIDName[2034:2046],
	134: _ObservationIDName[2046:2059],
	135: _ObservationIDName[2059:2069],
	136: _ObservationIDName[2069:2079],
	137: _ObservationIDName[2079:2099],
	138: _ObservationIDName[2099:2113],
	139: _ObservationIDName[2113:2124],
	140: _ObservationIDName[2124:2135],
	141: _ObservationIDName[2135:2146],
	142: _ObservationIDName[2146:2176],
	143: _ObservationIDName[2176:2206],
	144: _ObservationIDName[2206:2232],
	145: _ObservationIDName[2232:2258],
	146: _ObservationIDName[2258:2273],
	147: _ObservationIDName[2273:2292],
	148: _ObservationIDName[2292:2311],
	149: _ObservationIDName[2311:2344],
	150: _ObservationIDName[2344:2352],
	151: _ObservationIDName[2352:2376],
	152: _ObservationIDName[2376:2389],
	153: _ObservationIDName[2389:2402],
	154: _ObservationIDName[2402:2415],
	155: _ObservationIDName[2415:2428],
	160: _ObservationIDName[2428:2441],
	161: _ObservationIDName[2441:2455],
	162: _ObservationIDName[2455:2469],
	163: _ObservationIDName[2469:2483],
	170: _ObservationIDName[2483:2495],
	171: _ObservationIDName[2495:2508],
	172: _ObservationIDName[2508:2524],
	173: _ObservationIDName[2524:2541],
	174: _ObservationIDName[2541:2554],
	175: _ObservationIDName[2554:2576],
	182: _ObservationIDName[2576:2589],
	183: _ObservationIDName[2589:2602],
	184: _ObservationIDName[2602:2615],
	185: _ObservationIDName[2615:2628],
	190: _ObservationIDName[2628:2641],
	191: _ObservationIDName[2641:2654],
	192: _ObservationIDName[2654:2667],
	193: _ObservationIDName[2667:2680],
	194: _ObservationIDName[2680:2693],
	195: _ObservationIDName[2693:2706],
	196: _ObservationIDName[2706:2719],
	197: _ObservationIDName[2719:2732],
	198: _ObservationIDName[2732:2745],
	199: _ObservationIDName[2745:2758],
	202: _ObservationIDName[2758:2773],
	203: _ObservationIDName[2773:2788],
	204: _ObservationIDName[2788:2803],
	205: _ObservationIDName[2803:2818],
	210: _ObservationIDName[2818:2830],
	211: _ObservationIDName[2830:2841],
	212: _ObservationIDName[2841:2853],
	220: _ObservationIDName[2853:2861],
	221: _ObservationIDName[2861:2869],
	222: _ObservationIDName[2869:2877],
	230: _ObservationIDName[2877:2900],
	231: _ObservationIDName[2900:2923],
	232: _ObservationIDName[2923:2946],
	250: _ObservationIDName[2946:2964],
	251: _ObservationIDName[2964:2987],
}

func (i ObservationID) String() string {
	if str, ok := _ObservationIDMap[i]; ok {
		return str
	}
	return fmt.Sprintf("ObservationID(%d)", i)
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ObservationIDNoOp() {
	var x [1]struct{}
	_ = x[SELF_TEST_RESULT-(1)]
	_ = x[SELF_TEST_DETAILS-(2)]
	_ = x[WIFI_EVENT-(10)]
	_ = x[CHARGER_OFFLINE_REASON-(11)]
	_ = x[EASEE_LINK_COMMAND_RESPONSE-(13)]
	_ = x[EASEE_LINK_DATA_RECEIVED-(14)]
	_ = x[LOCAL_PRE_AUTHORIZE_ENABLED-(15)]
	_ = x[LOCAL_AUTHORIZE_OFFLINE_ENABLED-(16)]
	_ = x[ALLOW_OFFLINE_TX_FOR_UNKNOWN_ID-(17)]
	_ = x[ERRATIC_EVMAX_TOGGLES-(18)]
	_ = x[BACKPLATE_TYPE-(19)]
	_ = x[SITE_STRUCTURE-(20)]
	_ = x[DETECTED_POWER_GRID_TYPE-(21)]
	_ = x[CIRCUIT_MAX_CURRENT_P1-(22)]
	_ = x[CIRCUIT_MAX_CURRENT_P2-(23)]
	_ = x[CIRCUIT_MAX_CURRENT_P3-(24)]
	_ = x[LOCATION-(25)]
	_ = x[SITE_IDSTRING-(26)]
	_ = x[SITE_IDNUMERIC-(27)]
	_ = x[LOCK_CABLE_PERMANENTLY-(30)]
	_ = x[IS_ENABLED-(31)]
	_ = x[CIRCUIT_SEQUENCE_NUMBER-(33)]
	_ = x[SINGLE_PHASE_NUMBER-(34)]
	_ = x[ENABLE3_PHASES_DEPRECATED-(35)]
	_ = x[WI_FI_SSID-(36)]
	_ = x[ENABLE_IDLE_CURRENT-(37)]
	_ = x[PHASE_MODE-(38)]
	_ = x[FORCED_THREE_PHASE_ON_ITWITH_GND_FAULT-(39)]
	_ = x[LED_STRIP_BRIGHTNESS-(40)]
	_ = x[LOCAL_AUTHORIZATION_REQUIRED-(41)]
	_ = x[AUTHORIZATION_REQUIRED-(42)]
	_ = x[REMOTE_START_REQUIRED-(43)]
	_ = x[SMART_BUTTON_ENABLED-(44)]
	_ = x[OFFLINE_CHARGING_MODE-(45)]
	_ = x[LEDMODE-(46)]
	_ = x[MAX_CHARGER_CURRENT-(47)]
	_ = x[DYNAMIC_CHARGER_CURRENT-(48)]
	_ = x[MAX_CURRENT_OFFLINE_FALLBACK_P1-(50)]
	_ = x[MAX_CURRENT_OFFLINE_FALLBACK_P2-(51)]
	_ = x[MAX_CURRENT_OFFLINE_FALLBACK_P3-(52)]
	_ = x[LISTEN_TO_CONTROL_PULSE-(56)]
	_ = x[CONTROL_PULSE_RTT-(57)]
	_ = x[CHARGING_SCHEDULE-(62)]
	_ = x[PAIRED_EQUALIZER-(65)]
	_ = x[WI_FI_APENABLED-(68)]
	_ = x[PAIRED_USER_IDTOKEN-(69)]
	_ = x[CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L1-(70)]
	_ = x[CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L2-(71)]
	_ = x[CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L3-(72)]
	_ = x[CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L1-(73)]
	_ = x[CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L2-(74)]
	_ = x[CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L3-(75)]
	_ = x[NUMBER_OF_CARS_CONNECTED-(76)]
	_ = x[NUMBER_OF_CARS_CHARGING-(77)]
	_ = x[NUMBER_OF_CARS_IN_QUEUE-(78)]
	_ = x[NUMBER_OF_CARS_FULLY_CHARGED-(79)]
	_ = x[SOFTWARE_RELEASE-(80)]
	_ = x[ICCID-(81)]
	_ = x[MODEM_FW_ID-(82)]
	_ = x[OTAERROR_CODE-(83)]
	_ = x[MOBILE_NETWORK_OPERATOR-(84)]
	_ = x[REBOOT_REASON-(89)]
	_ = x[POWER_PCBVERSION-(90)]
	_ = x[COM_PCBVERSION-(91)]
	_ = x[REASON_FOR_NO_CURRENT-(96)]
	_ = x[LOAD_BALANCING_NUMBER_OF_CONNECTED_CHARGERS-(97)]
	_ = x[UDPNUM_OF_CONNECTED_NODES-(98)]
	_ = x[LOCAL_CONNECTION-(99)]
	_ = x[PILOT_MODE-(100)]
	_ = x[CAR_CONNECTED_DEPRECATED-(101)]
	_ = x[SMART_CHARGING-(102)]
	_ = x[CABLE_LOCKED-(103)]
	_ = x[CABLE_RATING-(104)]
	_ = x[PILOT_HIGH-(105)]
	_ = x[PILOT_LOW-(106)]
	_ = x[BACK_PLATE_ID-(107)]
	_ = x[USER_IDTOKEN_REVERSED-(108)]
	_ = x[CHARGER_OP_MODE-(109)]
	_ = x[OUTPUT_PHASE-(110)]
	_ = x[DYNAMIC_CIRCUIT_CURRENT_P1-(111)]
	_ = x[DYNAMIC_CIRCUIT_CURRENT_P2-(112)]
	_ = x[DYNAMIC_CIRCUIT_CURRENT_P3-(113)]
	_ = x[OUTPUT_CURRENT-(114)]
	_ = x[DERATED_CURRENT-(115)]
	_ = x[DERATING_ACTIVE-(116)]
	_ = x[DEBUG_STRING-(117)]
	_ = x[ERROR_STRING-(118)]
	_ = x[ERROR_CODE-(119)]
	_ = x[TOTAL_POWER-(120)]
	_ = x[SESSION_ENERGY-(121)]
	_ = x[ENERGY_PER_HOUR-(122)]
	_ = x[LEGACY_EV_STATUS-(123)]
	_ = x[LIFETIME_ENERGY-(124)]
	_ = x[LIFETIME_RELAY_SWITCHES-(125)]
	_ = x[LIFETIME_HOURS-(126)]
	_ = x[DYNAMIC_CURRENT_OFFLINE_FALLBACK_DEPRICATED-(127)]
	_ = x[USER_IDTOKEN-(128)]
	_ = x[CHARGING_SESSION-(129)]
	_ = x[CELL_RSSI-(130)]
	_ = x[CELL_RAT-(131)]
	_ = x[WI_FI_RSSI-(132)]
	_ = x[CELL_ADDRESS-(133)]
	_ = x[WI_FI_ADDRESS-(134)]
	_ = x[WI_FI_TYPE-(135)]
	_ = x[LOCAL_RSSI-(136)]
	_ = x[MASTER_BACK_PLATE_ID-(137)]
	_ = x[LOCAL_TX_POWER-(138)]
	_ = x[LOCAL_STATE-(139)]
	_ = x[FOUND_WI_FI-(140)]
	_ = x[CHARGER_RAT-(141)]
	_ = x[CELLULAR_INTERFACE_ERROR_COUNT-(142)]
	_ = x[CELLULAR_INTERFACE_RESET_COUNT-(143)]
	_ = x[WIFI_INTERFACE_ERROR_COUNT-(144)]
	_ = x[WIFI_INTERFACE_RESET_COUNT-(145)]
	_ = x[LOCAL_NODE_TYPE-(146)]
	_ = x[LOCAL_RADIO_CHANNEL-(147)]
	_ = x[LOCAL_SHORT_ADDRESS-(148)]
	_ = x[LOCAL_PARENT_ADDR_OR_NUM_OF_NODES-(149)]
	_ = x[TEMP_MAX-(150)]
	_ = x[TEMP_AMBIENT_POWER_BOARD-(151)]
	_ = x[TEMP_INPUT_T2-(152)]
	_ = x[TEMP_INPUT_T3-(153)]
	_ = x[TEMP_INPUT_T4-(154)]
	_ = x[TEMP_INPUT_T5-(155)]
	_ = x[TEMP_OUTPUT_N-(160)]
	_ = x[TEMP_OUTPUT_L1-(161)]
	_ = x[TEMP_OUTPUT_L2-(162)]
	_ = x[TEMP_OUTPUT_L3-(163)]
	_ = x[TEMP_AMBIENT-(170)]
	_ = x[LIGHT_AMBIENT-(171)]
	_ = x[INT_REL_HUMIDITY-(172)]
	_ = x[BACK_PLATE_LOCKED-(173)]
	_ = x[CURRENT_MOTOR-(174)]
	_ = x[BACK_PLATE_HALL_SENSOR-(175)]
	_ = x[IN_CURRENT_T2-(182)]
	_ = x[IN_CURRENT_T3-(183)]
	_ = x[IN_CURRENT_T4-(184)]
	_ = x[IN_CURRENT_T5-(185)]
	_ = x[IN_VOLT_T1_T2-(190)]
	_ = x[IN_VOLT_T1_T3-(191)]
	_ = x[IN_VOLT_T1_T4-(192)]
	_ = x[IN_VOLT_T1_T5-(193)]
	_ = x[IN_VOLT_T2_T3-(194)]
	_ = x[IN_VOLT_T2_T4-(195)]
	_ = x[IN_VOLT_T2_T5-(196)]
	_ = x[IN_VOLT_T3_T4-(197)]
	_ = x[IN_VOLT_T3_T5-(198)]
	_ = x[IN_VOLT_T4_T5-(199)]
	_ = x[OUT_VOLT_PIN1_2-(202)]
	_ = x[OUT_VOLT_PIN1_3-(203)]
	_ = x[OUT_VOLT_PIN1_4-(204)]
	_ = x[OUT_VOLT_PIN1_5-(205)]
	_ = x[VOLT_LEVEL33-(210)]
	_ = x[VOLT_LEVEL5-(211)]
	_ = x[VOLT_LEVEL12-(212)]
	_ = x[LTE_RSRP-(220)]
	_ = x[LTE_SINR-(221)]
	_ = x[LTE_RSRQ-(222)]
	_ = x[EQ_AVAILABLE_CURRENT_P1-(230)]
	_ = x[EQ_AVAILABLE_CURRENT_P2-(231)]
	_ = x[EQ_AVAILABLE_CURRENT_P3-(232)]
	_ = x[CONNECTED_TO_CLOUD-(250)]
	_ = x[CLOUD_DISCONNECT_REASON-(251)]
}

var _ObservationIDValues = []ObservationID{SELF_TEST_RESULT, SELF_TEST_DETAILS, WIFI_EVENT, CHARGER_OFFLINE_REASON, EASEE_LINK_COMMAND_RESPONSE, EASEE_LINK_DATA_RECEIVED, LOCAL_PRE_AUTHORIZE_ENABLED, LOCAL_AUTHORIZE_OFFLINE_ENABLED, ALLOW_OFFLINE_TX_FOR_UNKNOWN_ID, ERRATIC_EVMAX_TOGGLES, BACKPLATE_TYPE, SITE_STRUCTURE, DETECTED_POWER_GRID_TYPE, CIRCUIT_MAX_CURRENT_P1, CIRCUIT_MAX_CURRENT_P2, CIRCUIT_MAX_CURRENT_P3, LOCATION, SITE_IDSTRING, SITE_IDNUMERIC, LOCK_CABLE_PERMANENTLY, IS_ENABLED, CIRCUIT_SEQUENCE_NUMBER, SINGLE_PHASE_NUMBER, ENABLE3_PHASES_DEPRECATED, WI_FI_SSID, ENABLE_IDLE_CURRENT, PHASE_MODE, FORCED_THREE_PHASE_ON_ITWITH_GND_FAULT, LED_STRIP_BRIGHTNESS, LOCAL_AUTHORIZATION_REQUIRED, AUTHORIZATION_REQUIRED, REMOTE_START_REQUIRED, SMART_BUTTON_ENABLED, OFFLINE_CHARGING_MODE, LEDMODE, MAX_CHARGER_CURRENT, DYNAMIC_CHARGER_CURRENT, MAX_CURRENT_OFFLINE_FALLBACK_P1, MAX_CURRENT_OFFLINE_FALLBACK_P2, MAX_CURRENT_OFFLINE_FALLBACK_P3, LISTEN_TO_CONTROL_PULSE, CONTROL_PULSE_RTT, CHARGING_SCHEDULE, PAIRED_EQUALIZER, WI_FI_APENABLED, PAIRED_USER_IDTOKEN, CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L1, CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L2, CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L3, CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L1, CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L2, CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L3, NUMBER_OF_CARS_CONNECTED, NUMBER_OF_CARS_CHARGING, NUMBER_OF_CARS_IN_QUEUE, NUMBER_OF_CARS_FULLY_CHARGED, SOFTWARE_RELEASE, ICCID, MODEM_FW_ID, OTAERROR_CODE, MOBILE_NETWORK_OPERATOR, REBOOT_REASON, POWER_PCBVERSION, COM_PCBVERSION, REASON_FOR_NO_CURRENT, LOAD_BALANCING_NUMBER_OF_CONNECTED_CHARGERS, UDPNUM_OF_CONNECTED_NODES, LOCAL_CONNECTION, PILOT_MODE, CAR_CONNECTED_DEPRECATED, SMART_CHARGING, CABLE_LOCKED, CABLE_RATING, PILOT_HIGH, PILOT_LOW, BACK_PLATE_ID, USER_IDTOKEN_REVERSED, CHARGER_OP_MODE, OUTPUT_PHASE, DYNAMIC_CIRCUIT_CURRENT_P1, DYNAMIC_CIRCUIT_CURRENT_P2, DYNAMIC_CIRCUIT_CURRENT_P3, OUTPUT_CURRENT, DERATED_CURRENT, DERATING_ACTIVE, DEBUG_STRING, ERROR_STRING, ERROR_CODE, TOTAL_POWER, SESSION_ENERGY, ENERGY_PER_HOUR, LEGACY_EV_STATUS, LIFETIME_ENERGY, LIFETIME_RELAY_SWITCHES, LIFETIME_HOURS, DYNAMIC_CURRENT_OFFLINE_FALLBACK_DEPRICATED, USER_IDTOKEN, CHARGING_SESSION, CELL_RSSI, CELL_RAT, WI_FI_RSSI, CELL_ADDRESS, WI_FI_ADDRESS, WI_FI_TYPE, LOCAL_RSSI, MASTER_BACK_PLATE_ID, LOCAL_TX_POWER, LOCAL_STATE, FOUND_WI_FI, CHARGER_RAT, CELLULAR_INTERFACE_ERROR_COUNT, CELLULAR_INTERFACE_RESET_COUNT, WIFI_INTERFACE_ERROR_COUNT, WIFI_INTERFACE_RESET_COUNT, LOCAL_NODE_TYPE, LOCAL_RADIO_CHANNEL, LOCAL_SHORT_ADDRESS, LOCAL_PARENT_ADDR_OR_NUM_OF_NODES, TEMP_MAX, TEMP_AMBIENT_POWER_BOARD, TEMP_INPUT_T2, TEMP_INPUT_T3, TEMP_INPUT_T4, TEMP_INPUT_T5, TEMP_OUTPUT_N, TEMP_OUTPUT_L1, TEMP_OUTPUT_L2, TEMP_OUTPUT_L3, TEMP_AMBIENT, LIGHT_AMBIENT, INT_REL_HUMIDITY, BACK_PLATE_LOCKED, CURRENT_MOTOR, BACK_PLATE_HALL_SENSOR, IN_CURRENT_T2, IN_CURRENT_T3, IN_CURRENT_T4, IN_CURRENT_T5, IN_VOLT_T1_T2, IN_VOLT_T1_T3, IN_VOLT_T1_T4, IN_VOLT_T1_T5, IN_VOLT_T2_T3, IN_VOLT_T2_T4, IN_VOLT_T2_T5, IN_VOLT_T3_T4, IN_VOLT_T3_T5, IN_VOLT_T4_T5, OUT_VOLT_PIN1_2, OUT_VOLT_PIN1_3, OUT_VOLT_PIN1_4, OUT_VOLT_PIN1_5, VOLT_LEVEL33, VOLT_LEVEL5, VOLT_LEVEL12, LTE_RSRP, LTE_SINR, LTE_RSRQ, EQ_AVAILABLE_CURRENT_P1, EQ_AVAILABLE_CURRENT_P2, EQ_AVAILABLE_CURRENT_P3, CONNECTED_TO_CLOUD, CLOUD_DISCONNECT_REASON}

var _ObservationIDNameToValueMap = map[string]ObservationID{
	_ObservationIDName[0:16]:           SELF_TEST_RESULT,
	_ObservationIDLowerName[0:16]:      SELF_TEST_RESULT,
	_ObservationIDName[16:33]:          SELF_TEST_DETAILS,
	_ObservationIDLowerName[16:33]:     SELF_TEST_DETAILS,
	_ObservationIDName[33:43]:          WIFI_EVENT,
	_ObservationIDLowerName[33:43]:     WIFI_EVENT,
	_ObservationIDName[43:65]:          CHARGER_OFFLINE_REASON,
	_ObservationIDLowerName[43:65]:     CHARGER_OFFLINE_REASON,
	_ObservationIDName[65:92]:          EASEE_LINK_COMMAND_RESPONSE,
	_ObservationIDLowerName[65:92]:     EASEE_LINK_COMMAND_RESPONSE,
	_ObservationIDName[92:116]:         EASEE_LINK_DATA_RECEIVED,
	_ObservationIDLowerName[92:116]:    EASEE_LINK_DATA_RECEIVED,
	_ObservationIDName[116:143]:        LOCAL_PRE_AUTHORIZE_ENABLED,
	_ObservationIDLowerName[116:143]:   LOCAL_PRE_AUTHORIZE_ENABLED,
	_ObservationIDName[143:174]:        LOCAL_AUTHORIZE_OFFLINE_ENABLED,
	_ObservationIDLowerName[143:174]:   LOCAL_AUTHORIZE_OFFLINE_ENABLED,
	_ObservationIDName[174:205]:        ALLOW_OFFLINE_TX_FOR_UNKNOWN_ID,
	_ObservationIDLowerName[174:205]:   ALLOW_OFFLINE_TX_FOR_UNKNOWN_ID,
	_ObservationIDName[205:226]:        ERRATIC_EVMAX_TOGGLES,
	_ObservationIDLowerName[205:226]:   ERRATIC_EVMAX_TOGGLES,
	_ObservationIDName[226:240]:        BACKPLATE_TYPE,
	_ObservationIDLowerName[226:240]:   BACKPLATE_TYPE,
	_ObservationIDName[240:254]:        SITE_STRUCTURE,
	_ObservationIDLowerName[240:254]:   SITE_STRUCTURE,
	_ObservationIDName[254:278]:        DETECTED_POWER_GRID_TYPE,
	_ObservationIDLowerName[254:278]:   DETECTED_POWER_GRID_TYPE,
	_ObservationIDName[278:300]:        CIRCUIT_MAX_CURRENT_P1,
	_ObservationIDLowerName[278:300]:   CIRCUIT_MAX_CURRENT_P1,
	_ObservationIDName[300:322]:        CIRCUIT_MAX_CURRENT_P2,
	_ObservationIDLowerName[300:322]:   CIRCUIT_MAX_CURRENT_P2,
	_ObservationIDName[322:344]:        CIRCUIT_MAX_CURRENT_P3,
	_ObservationIDLowerName[322:344]:   CIRCUIT_MAX_CURRENT_P3,
	_ObservationIDName[344:352]:        LOCATION,
	_ObservationIDLowerName[344:352]:   LOCATION,
	_ObservationIDName[352:365]:        SITE_IDSTRING,
	_ObservationIDLowerName[352:365]:   SITE_IDSTRING,
	_ObservationIDName[365:379]:        SITE_IDNUMERIC,
	_ObservationIDLowerName[365:379]:   SITE_IDNUMERIC,
	_ObservationIDName[379:401]:        LOCK_CABLE_PERMANENTLY,
	_ObservationIDLowerName[379:401]:   LOCK_CABLE_PERMANENTLY,
	_ObservationIDName[401:411]:        IS_ENABLED,
	_ObservationIDLowerName[401:411]:   IS_ENABLED,
	_ObservationIDName[411:434]:        CIRCUIT_SEQUENCE_NUMBER,
	_ObservationIDLowerName[411:434]:   CIRCUIT_SEQUENCE_NUMBER,
	_ObservationIDName[434:453]:        SINGLE_PHASE_NUMBER,
	_ObservationIDLowerName[434:453]:   SINGLE_PHASE_NUMBER,
	_ObservationIDName[453:478]:        ENABLE3_PHASES_DEPRECATED,
	_ObservationIDLowerName[453:478]:   ENABLE3_PHASES_DEPRECATED,
	_ObservationIDName[478:488]:        WI_FI_SSID,
	_ObservationIDLowerName[478:488]:   WI_FI_SSID,
	_ObservationIDName[488:507]:        ENABLE_IDLE_CURRENT,
	_ObservationIDLowerName[488:507]:   ENABLE_IDLE_CURRENT,
	_ObservationIDName[507:517]:        PHASE_MODE,
	_ObservationIDLowerName[507:517]:   PHASE_MODE,
	_ObservationIDName[517:555]:        FORCED_THREE_PHASE_ON_ITWITH_GND_FAULT,
	_ObservationIDLowerName[517:555]:   FORCED_THREE_PHASE_ON_ITWITH_GND_FAULT,
	_ObservationIDName[555:575]:        LED_STRIP_BRIGHTNESS,
	_ObservationIDLowerName[555:575]:   LED_STRIP_BRIGHTNESS,
	_ObservationIDName[575:603]:        LOCAL_AUTHORIZATION_REQUIRED,
	_ObservationIDLowerName[575:603]:   LOCAL_AUTHORIZATION_REQUIRED,
	_ObservationIDName[603:625]:        AUTHORIZATION_REQUIRED,
	_ObservationIDLowerName[603:625]:   AUTHORIZATION_REQUIRED,
	_ObservationIDName[625:646]:        REMOTE_START_REQUIRED,
	_ObservationIDLowerName[625:646]:   REMOTE_START_REQUIRED,
	_ObservationIDName[646:666]:        SMART_BUTTON_ENABLED,
	_ObservationIDLowerName[646:666]:   SMART_BUTTON_ENABLED,
	_ObservationIDName[666:687]:        OFFLINE_CHARGING_MODE,
	_ObservationIDLowerName[666:687]:   OFFLINE_CHARGING_MODE,
	_ObservationIDName[687:694]:        LEDMODE,
	_ObservationIDLowerName[687:694]:   LEDMODE,
	_ObservationIDName[694:713]:        MAX_CHARGER_CURRENT,
	_ObservationIDLowerName[694:713]:   MAX_CHARGER_CURRENT,
	_ObservationIDName[713:736]:        DYNAMIC_CHARGER_CURRENT,
	_ObservationIDLowerName[713:736]:   DYNAMIC_CHARGER_CURRENT,
	_ObservationIDName[736:767]:        MAX_CURRENT_OFFLINE_FALLBACK_P1,
	_ObservationIDLowerName[736:767]:   MAX_CURRENT_OFFLINE_FALLBACK_P1,
	_ObservationIDName[767:798]:        MAX_CURRENT_OFFLINE_FALLBACK_P2,
	_ObservationIDLowerName[767:798]:   MAX_CURRENT_OFFLINE_FALLBACK_P2,
	_ObservationIDName[798:829]:        MAX_CURRENT_OFFLINE_FALLBACK_P3,
	_ObservationIDLowerName[798:829]:   MAX_CURRENT_OFFLINE_FALLBACK_P3,
	_ObservationIDName[829:852]:        LISTEN_TO_CONTROL_PULSE,
	_ObservationIDLowerName[829:852]:   LISTEN_TO_CONTROL_PULSE,
	_ObservationIDName[852:869]:        CONTROL_PULSE_RTT,
	_ObservationIDLowerName[852:869]:   CONTROL_PULSE_RTT,
	_ObservationIDName[869:886]:        CHARGING_SCHEDULE,
	_ObservationIDLowerName[869:886]:   CHARGING_SCHEDULE,
	_ObservationIDName[886:902]:        PAIRED_EQUALIZER,
	_ObservationIDLowerName[886:902]:   PAIRED_EQUALIZER,
	_ObservationIDName[902:917]:        WI_FI_APENABLED,
	_ObservationIDLowerName[902:917]:   WI_FI_APENABLED,
	_ObservationIDName[917:936]:        PAIRED_USER_IDTOKEN,
	_ObservationIDLowerName[917:936]:   PAIRED_USER_IDTOKEN,
	_ObservationIDName[936:986]:        CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L1,
	_ObservationIDLowerName[936:986]:   CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L1,
	_ObservationIDName[986:1036]:       CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L2,
	_ObservationIDLowerName[986:1036]:  CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L2,
	_ObservationIDName[1036:1086]:      CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L3,
	_ObservationIDLowerName[1036:1086]: CIRCUIT_TOTAL_ALLOCATED_PHASE_CONDUCTOR_CURRENT_L3,
	_ObservationIDName[1086:1126]:      CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L1,
	_ObservationIDLowerName[1086:1126]: CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L1,
	_ObservationIDName[1126:1166]:      CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L2,
	_ObservationIDLowerName[1126:1166]: CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L2,
	_ObservationIDName[1166:1206]:      CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L3,
	_ObservationIDLowerName[1166:1206]: CIRCUIT_TOTAL_PHASE_CONDUCTOR_CURRENT_L3,
	_ObservationIDName[1206:1230]:      NUMBER_OF_CARS_CONNECTED,
	_ObservationIDLowerName[1206:1230]: NUMBER_OF_CARS_CONNECTED,
	_ObservationIDName[1230:1253]:      NUMBER_OF_CARS_CHARGING,
	_ObservationIDLowerName[1230:1253]: NUMBER_OF_CARS_CHARGING,
	_ObservationIDName[1253:1276]:      NUMBER_OF_CARS_IN_QUEUE,
	_ObservationIDLowerName[1253:1276]: NUMBER_OF_CARS_IN_QUEUE,
	_ObservationIDName[1276:1304]:      NUMBER_OF_CARS_FULLY_CHARGED,
	_ObservationIDLowerName[1276:1304]: NUMBER_OF_CARS_FULLY_CHARGED,
	_ObservationIDName[1304:1320]:      SOFTWARE_RELEASE,
	_ObservationIDLowerName[1304:1320]: SOFTWARE_RELEASE,
	_ObservationIDName[1320:1325]:      ICCID,
	_ObservationIDLowerName[1320:1325]: ICCID,
	_ObservationIDName[1325:1336]:      MODEM_FW_ID,
	_ObservationIDLowerName[1325:1336]: MODEM_FW_ID,
	_ObservationIDName[1336:1349]:      OTAERROR_CODE,
	_ObservationIDLowerName[1336:1349]: OTAERROR_CODE,
	_ObservationIDName[1349:1372]:      MOBILE_NETWORK_OPERATOR,
	_ObservationIDLowerName[1349:1372]: MOBILE_NETWORK_OPERATOR,
	_ObservationIDName[1372:1385]:      REBOOT_REASON,
	_ObservationIDLowerName[1372:1385]: REBOOT_REASON,
	_ObservationIDName[1385:1401]:      POWER_PCBVERSION,
	_ObservationIDLowerName[1385:1401]: POWER_PCBVERSION,
	_ObservationIDName[1401:1415]:      COM_PCBVERSION,
	_ObservationIDLowerName[1401:1415]: COM_PCBVERSION,
	_ObservationIDName[1415:1436]:      REASON_FOR_NO_CURRENT,
	_ObservationIDLowerName[1415:1436]: REASON_FOR_NO_CURRENT,
	_ObservationIDName[1436:1479]:      LOAD_BALANCING_NUMBER_OF_CONNECTED_CHARGERS,
	_ObservationIDLowerName[1436:1479]: LOAD_BALANCING_NUMBER_OF_CONNECTED_CHARGERS,
	_ObservationIDName[1479:1504]:      UDPNUM_OF_CONNECTED_NODES,
	_ObservationIDLowerName[1479:1504]: UDPNUM_OF_CONNECTED_NODES,
	_ObservationIDName[1504:1520]:      LOCAL_CONNECTION,
	_ObservationIDLowerName[1504:1520]: LOCAL_CONNECTION,
	_ObservationIDName[1520:1530]:      PILOT_MODE,
	_ObservationIDLowerName[1520:1530]: PILOT_MODE,
	_ObservationIDName[1530:1554]:      CAR_CONNECTED_DEPRECATED,
	_ObservationIDLowerName[1530:1554]: CAR_CONNECTED_DEPRECATED,
	_ObservationIDName[1554:1568]:      SMART_CHARGING,
	_ObservationIDLowerName[1554:1568]: SMART_CHARGING,
	_ObservationIDName[1568:1580]:      CABLE_LOCKED,
	_ObservationIDLowerName[1568:1580]: CABLE_LOCKED,
	_ObservationIDName[1580:1592]:      CABLE_RATING,
	_ObservationIDLowerName[1580:1592]: CABLE_RATING,
	_ObservationIDName[1592:1602]:      PILOT_HIGH,
	_ObservationIDLowerName[1592:1602]: PILOT_HIGH,
	_ObservationIDName[1602:1611]:      PILOT_LOW,
	_ObservationIDLowerName[1602:1611]: PILOT_LOW,
	_ObservationIDName[1611:1624]:      BACK_PLATE_ID,
	_ObservationIDLowerName[1611:1624]: BACK_PLATE_ID,
	_ObservationIDName[1624:1645]:      USER_IDTOKEN_REVERSED,
	_ObservationIDLowerName[1624:1645]: USER_IDTOKEN_REVERSED,
	_ObservationIDName[1645:1660]:      CHARGER_OP_MODE,
	_ObservationIDLowerName[1645:1660]: CHARGER_OP_MODE,
	_ObservationIDName[1660:1672]:      OUTPUT_PHASE,
	_ObservationIDLowerName[1660:1672]: OUTPUT_PHASE,
	_ObservationIDName[1672:1698]:      DYNAMIC_CIRCUIT_CURRENT_P1,
	_ObservationIDLowerName[1672:1698]: DYNAMIC_CIRCUIT_CURRENT_P1,
	_ObservationIDName[1698:1724]:      DYNAMIC_CIRCUIT_CURRENT_P2,
	_ObservationIDLowerName[1698:1724]: DYNAMIC_CIRCUIT_CURRENT_P2,
	_ObservationIDName[1724:1750]:      DYNAMIC_CIRCUIT_CURRENT_P3,
	_ObservationIDLowerName[1724:1750]: DYNAMIC_CIRCUIT_CURRENT_P3,
	_ObservationIDName[1750:1764]:      OUTPUT_CURRENT,
	_ObservationIDLowerName[1750:1764]: OUTPUT_CURRENT,
	_ObservationIDName[1764:1779]:      DERATED_CURRENT,
	_ObservationIDLowerName[1764:1779]: DERATED_CURRENT,
	_ObservationIDName[1779:1794]:      DERATING_ACTIVE,
	_ObservationIDLowerName[1779:1794]: DERATING_ACTIVE,
	_ObservationIDName[1794:1806]:      DEBUG_STRING,
	_ObservationIDLowerName[1794:1806]: DEBUG_STRING,
	_ObservationIDName[1806:1818]:      ERROR_STRING,
	_ObservationIDLowerName[1806:1818]: ERROR_STRING,
	_ObservationIDName[1818:1828]:      ERROR_CODE,
	_ObservationIDLowerName[1818:1828]: ERROR_CODE,
	_ObservationIDName[1828:1839]:      TOTAL_POWER,
	_ObservationIDLowerName[1828:1839]: TOTAL_POWER,
	_ObservationIDName[1839:1853]:      SESSION_ENERGY,
	_ObservationIDLowerName[1839:1853]: SESSION_ENERGY,
	_ObservationIDName[1853:1868]:      ENERGY_PER_HOUR,
	_ObservationIDLowerName[1853:1868]: ENERGY_PER_HOUR,
	_ObservationIDName[1868:1884]:      LEGACY_EV_STATUS,
	_ObservationIDLowerName[1868:1884]: LEGACY_EV_STATUS,
	_ObservationIDName[1884:1899]:      LIFETIME_ENERGY,
	_ObservationIDLowerName[1884:1899]: LIFETIME_ENERGY,
	_ObservationIDName[1899:1922]:      LIFETIME_RELAY_SWITCHES,
	_ObservationIDLowerName[1899:1922]: LIFETIME_RELAY_SWITCHES,
	_ObservationIDName[1922:1936]:      LIFETIME_HOURS,
	_ObservationIDLowerName[1922:1936]: LIFETIME_HOURS,
	_ObservationIDName[1936:1979]:      DYNAMIC_CURRENT_OFFLINE_FALLBACK_DEPRICATED,
	_ObservationIDLowerName[1936:1979]: DYNAMIC_CURRENT_OFFLINE_FALLBACK_DEPRICATED,
	_ObservationIDName[1979:1991]:      USER_IDTOKEN,
	_ObservationIDLowerName[1979:1991]: USER_IDTOKEN,
	_ObservationIDName[1991:2007]:      CHARGING_SESSION,
	_ObservationIDLowerName[1991:2007]: CHARGING_SESSION,
	_ObservationIDName[2007:2016]:      CELL_RSSI,
	_ObservationIDLowerName[2007:2016]: CELL_RSSI,
	_ObservationIDName[2016:2024]:      CELL_RAT,
	_ObservationIDLowerName[2016:2024]: CELL_RAT,
	_ObservationIDName[2024:2034]:      WI_FI_RSSI,
	_ObservationIDLowerName[2024:2034]: WI_FI_RSSI,
	_ObservationIDName[2034:2046]:      CELL_ADDRESS,
	_ObservationIDLowerName[2034:2046]: CELL_ADDRESS,
	_ObservationIDName[2046:2059]:      WI_FI_ADDRESS,
	_ObservationIDLowerName[2046:2059]: WI_FI_ADDRESS,
	_ObservationIDName[2059:2069]:      WI_FI_TYPE,
	_ObservationIDLowerName[2059:2069]: WI_FI_TYPE,
	_ObservationIDName[2069:2079]:      LOCAL_RSSI,
	_ObservationIDLowerName[2069:2079]: LOCAL_RSSI,
	_ObservationIDName[2079:2099]:      MASTER_BACK_PLATE_ID,
	_ObservationIDLowerName[2079:2099]: MASTER_BACK_PLATE_ID,
	_ObservationIDName[2099:2113]:      LOCAL_TX_POWER,
	_ObservationIDLowerName[2099:2113]: LOCAL_TX_POWER,
	_ObservationIDName[2113:2124]:      LOCAL_STATE,
	_ObservationIDLowerName[2113:2124]: LOCAL_STATE,
	_ObservationIDName[2124:2135]:      FOUND_WI_FI,
	_ObservationIDLowerName[2124:2135]: FOUND_WI_FI,
	_ObservationIDName[2135:2146]:      CHARGER_RAT,
	_ObservationIDLowerName[2135:2146]: CHARGER_RAT,
	_ObservationIDName[2146:2176]:      CELLULAR_INTERFACE_ERROR_COUNT,
	_ObservationIDLowerName[2146:2176]: CELLULAR_INTERFACE_ERROR_COUNT,
	_ObservationIDName[2176:2206]:      CELLULAR_INTERFACE_RESET_COUNT,
	_ObservationIDLowerName[2176:2206]: CELLULAR_INTERFACE_RESET_COUNT,
	_ObservationIDName[2206:2232]:      WIFI_INTERFACE_ERROR_COUNT,
	_ObservationIDLowerName[2206:2232]: WIFI_INTERFACE_ERROR_COUNT,
	_ObservationIDName[2232:2258]:      WIFI_INTERFACE_RESET_COUNT,
	_ObservationIDLowerName[2232:2258]: WIFI_INTERFACE_RESET_COUNT,
	_ObservationIDName[2258:2273]:      LOCAL_NODE_TYPE,
	_ObservationIDLowerName[2258:2273]: LOCAL_NODE_TYPE,
	_ObservationIDName[2273:2292]:      LOCAL_RADIO_CHANNEL,
	_ObservationIDLowerName[2273:2292]: LOCAL_RADIO_CHANNEL,
	_ObservationIDName[2292:2311]:      LOCAL_SHORT_ADDRESS,
	_ObservationIDLowerName[2292:2311]: LOCAL_SHORT_ADDRESS,
	_ObservationIDName[2311:2344]:      LOCAL_PARENT_ADDR_OR_NUM_OF_NODES,
	_ObservationIDLowerName[2311:2344]: LOCAL_PARENT_ADDR_OR_NUM_OF_NODES,
	_ObservationIDName[2344:2352]:      TEMP_MAX,
	_ObservationIDLowerName[2344:2352]: TEMP_MAX,
	_ObservationIDName[2352:2376]:      TEMP_AMBIENT_POWER_BOARD,
	_ObservationIDLowerName[2352:2376]: TEMP_AMBIENT_POWER_BOARD,
	_ObservationIDName[2376:2389]:      TEMP_INPUT_T2,
	_ObservationIDLowerName[2376:2389]: TEMP_INPUT_T2,
	_ObservationIDName[2389:2402]:      TEMP_INPUT_T3,
	_ObservationIDLowerName[2389:2402]: TEMP_INPUT_T3,
	_ObservationIDName[2402:2415]:      TEMP_INPUT_T4,
	_ObservationIDLowerName[2402:2415]: TEMP_INPUT_T4,
	_ObservationIDName[2415:2428]:      TEMP_INPUT_T5,
	_ObservationIDLowerName[2415:2428]: TEMP_INPUT_T5,
	_ObservationIDName[2428:2441]:      TEMP_OUTPUT_N,
	_ObservationIDLowerName[2428:2441]: TEMP_OUTPUT_N,
	_ObservationIDName[2441:2455]:      TEMP_OUTPUT_L1,
	_ObservationIDLowerName[2441:2455]: TEMP_OUTPUT_L1,
	_ObservationIDName[2455:2469]:      TEMP_OUTPUT_L2,
	_ObservationIDLowerName[2455:2469]: TEMP_OUTPUT_L2,
	_ObservationIDName[2469:2483]:      TEMP_OUTPUT_L3,
	_ObservationIDLowerName[2469:2483]: TEMP_OUTPUT_L3,
	_ObservationIDName[2483:2495]:      TEMP_AMBIENT,
	_ObservationIDLowerName[2483:2495]: TEMP_AMBIENT,
	_ObservationIDName[2495:2508]:      LIGHT_AMBIENT,
	_ObservationIDLowerName[2495:2508]: LIGHT_AMBIENT,
	_ObservationIDName[2508:2524]:      INT_REL_HUMIDITY,
	_ObservationIDLowerName[2508:2524]: INT_REL_HUMIDITY,
	_ObservationIDName[2524:2541]:      BACK_PLATE_LOCKED,
	_ObservationIDLowerName[2524:2541]: BACK_PLATE_LOCKED,
	_ObservationIDName[2541:2554]:      CURRENT_MOTOR,
	_ObservationIDLowerName[2541:2554]: CURRENT_MOTOR,
	_ObservationIDName[2554:2576]:      BACK_PLATE_HALL_SENSOR,
	_ObservationIDLowerName[2554:2576]: BACK_PLATE_HALL_SENSOR,
	_ObservationIDName[2576:2589]:      IN_CURRENT_T2,
	_ObservationIDLowerName[2576:2589]: IN_CURRENT_T2,
	_ObservationIDName[2589:2602]:      IN_CURRENT_T3,
	_ObservationIDLowerName[2589:2602]: IN_CURRENT_T3,
	_ObservationIDName[2602:2615]:      IN_CURRENT_T4,
	_ObservationIDLowerName[2602:2615]: IN_CURRENT_T4,
	_ObservationIDName[2615:2628]:      IN_CURRENT_T5,
	_ObservationIDLowerName[2615:2628]: IN_CURRENT_T5,
	_ObservationIDName[2628:2641]:      IN_VOLT_T1_T2,
	_ObservationIDLowerName[2628:2641]: IN_VOLT_T1_T2,
	_ObservationIDName[2641:2654]:      IN_VOLT_T1_T3,
	_ObservationIDLowerName[2641:2654]: IN_VOLT_T1_T3,
	_ObservationIDName[2654:2667]:      IN_VOLT_T1_T4,
	_ObservationIDLowerName[2654:2667]: IN_VOLT_T1_T4,
	_ObservationIDName[2667:2680]:      IN_VOLT_T1_T5,
	_ObservationIDLowerName[2667:2680]: IN_VOLT_T1_T5,
	_ObservationIDName[2680:2693]:      IN_VOLT_T2_T3,
	_ObservationIDLowerName[2680:2693]: IN_VOLT_T2_T3,
	_ObservationIDName[2693:2706]:      IN_VOLT_T2_T4,
	_ObservationIDLowerName[2693:2706]: IN_VOLT_T2_T4,
	_ObservationIDName[2706:2719]:      IN_VOLT_T2_T5,
	_ObservationIDLowerName[2706:2719]: IN_VOLT_T2_T5,
	_ObservationIDName[2719:2732]:      IN_VOLT_T3_T4,
	_ObservationIDLowerName[2719:2732]: IN_VOLT_T3_T4,
	_ObservationIDName[2732:2745]:      IN_VOLT_T3_T5,
	_ObservationIDLowerName[2732:2745]: IN_VOLT_T3_T5,
	_ObservationIDName[2745:2758]:      IN_VOLT_T4_T5,
	_ObservationIDLowerName[2745:2758]: IN_VOLT_T4_T5,
	_ObservationIDName[2758:2773]:      OUT_VOLT_PIN1_2,
	_ObservationIDLowerName[2758:2773]: OUT_VOLT_PIN1_2,
	_ObservationIDName[2773:2788]:      OUT_VOLT_PIN1_3,
	_ObservationIDLowerName[2773:2788]: OUT_VOLT_PIN1_3,
	_ObservationIDName[2788:2803]:      OUT_VOLT_PIN1_4,
	_ObservationIDLowerName[2788:2803]: OUT_VOLT_PIN1_4,
	_ObservationIDName[2803:2818]:      OUT_VOLT_PIN1_5,
	_ObservationIDLowerName[2803:2818]: OUT_VOLT_PIN1_5,
	_ObservationIDName[2818:2830]:      VOLT_LEVEL33,
	_ObservationIDLowerName[2818:2830]: VOLT_LEVEL33,
	_ObservationIDName[2830:2841]:      VOLT_LEVEL5,
	_ObservationIDLowerName[2830:2841]: VOLT_LEVEL5,
	_ObservationIDName[2841:2853]:      VOLT_LEVEL12,
	_ObservationIDLowerName[2841:2853]: VOLT_LEVEL12,
	_ObservationIDName[2853:2861]:      LTE_RSRP,
	_ObservationIDLowerName[2853:2861]: LTE_RSRP,
	_ObservationIDName[2861:2869]:      LTE_SINR,
	_ObservationIDLowerName[2861:2869]: LTE_SINR,
	_ObservationIDName[2869:2877]:      LTE_RSRQ,
	_ObservationIDLowerName[2869:2877]: LTE_RSRQ,
	_ObservationIDName[2877:2900]:      EQ_AVAILABLE_CURRENT_P1,
	_ObservationIDLowerName[2877:2900]: EQ_AVAILABLE_CURRENT_P1,
	_ObservationIDName[2900:2923]:      EQ_AVAILABLE_CURRENT_P2,
	_ObservationIDLowerName[2900:2923]: EQ_AVAILABLE_CURRENT_P2,
	_ObservationIDName[2923:2946]:      EQ_AVAILABLE_CURRENT_P3,
	_ObservationIDLowerName[2923:2946]: EQ_AVAILABLE_CURRENT_P3,
	_ObservationIDName[2946:2964]:      CONNECTED_TO_CLOUD,
	_ObservationIDLowerName[2946:2964]: CONNECTED_TO_CLOUD,
	_ObservationIDName[2964:2987]:      CLOUD_DISCONNECT_REASON,
	_ObservationIDLowerName[2964:2987]: CLOUD_DISCONNECT_REASON,
}

var _ObservationIDNames = []string{
	_ObservationIDName[0:16],
	_ObservationIDName[16:33],
	_ObservationIDName[33:43],
	_ObservationIDName[43:65],
	_ObservationIDName[65:92],
	_ObservationIDName[92:116],
	_ObservationIDName[116:143],
	_ObservationIDName[143:174],
	_ObservationIDName[174:205],
	_ObservationIDName[205:226],
	_ObservationIDName[226:240],
	_ObservationIDName[240:254],
	_ObservationIDName[254:278],
	_ObservationIDName[278:300],
	_ObservationIDName[300:322],
	_ObservationIDName[322:344],
	_ObservationIDName[344:352],
	_ObservationIDName[352:365],
	_ObservationIDName[365:379],
	_ObservationIDName[379:401],
	_ObservationIDName[401:411],
	_ObservationIDName[411:434],
	_ObservationIDName[434:453],
	_ObservationIDName[453:478],
	_ObservationIDName[478:488],
	_ObservationIDName[488:507],
	_ObservationIDName[507:517],
	_ObservationIDName[517:555],
	_ObservationIDName[555:575],
	_ObservationIDName[575:603],
	_ObservationIDName[603:625],
	_ObservationIDName[625:646],
	_ObservationIDName[646:666],
	_ObservationIDName[666:687],
	_ObservationIDName[687:694],
	_ObservationIDName[694:713],
	_ObservationIDName[713:736],
	_ObservationIDName[736:767],
	_ObservationIDName[767:798],
	_ObservationIDName[798:829],
	_ObservationIDName[829:852],
	_ObservationIDName[852:869],
	_ObservationIDName[869:886],
	_ObservationIDName[886:902],
	_ObservationIDName[902:917],
	_ObservationIDName[917:936],
	_ObservationIDName[936:986],
	_ObservationIDName[986:1036],
	_ObservationIDName[1036:1086],
	_ObservationIDName[1086:1126],
	_ObservationIDName[1126:1166],
	_ObservationIDName[1166:1206],
	_ObservationIDName[1206:1230],
	_ObservationIDName[1230:1253],
	_ObservationIDName[1253:1276],
	_ObservationIDName[1276:1304],
	_ObservationIDName[1304:1320],
	_ObservationIDName[1320:1325],
	_ObservationIDName[1325:1336],
	_ObservationIDName[1336:1349],
	_ObservationIDName[1349:1372],
	_ObservationIDName[1372:1385],
	_ObservationIDName[1385:1401],
	_ObservationIDName[1401:1415],
	_ObservationIDName[1415:1436],
	_ObservationIDName[1436:1479],
	_ObservationIDName[1479:1504],
	_ObservationIDName[1504:1520],
	_ObservationIDName[1520:1530],
	_ObservationIDName[1530:1554],
	_ObservationIDName[1554:1568],
	_ObservationIDName[1568:1580],
	_ObservationIDName[1580:1592],
	_ObservationIDName[1592:1602],
	_ObservationIDName[1602:1611],
	_ObservationIDName[1611:1624],
	_ObservationIDName[1624:1645],
	_ObservationIDName[1645:1660],
	_ObservationIDName[1660:1672],
	_ObservationIDName[1672:1698],
	_ObservationIDName[1698:1724],
	_ObservationIDName[1724:1750],
	_ObservationIDName[1750:1764],
	_ObservationIDName[1764:1779],
	_ObservationIDName[1779:1794],
	_ObservationIDName[1794:1806],
	_ObservationIDName[1806:1818],
	_ObservationIDName[1818:1828],
	_ObservationIDName[1828:1839],
	_ObservationIDName[1839:1853],
	_ObservationIDName[1853:1868],
	_ObservationIDName[1868:1884],
	_ObservationIDName[1884:1899],
	_ObservationIDName[1899:1922],
	_ObservationIDName[1922:1936],
	_ObservationIDName[1936:1979],
	_ObservationIDName[1979:1991],
	_ObservationIDName[1991:2007],
	_ObservationIDName[2007:2016],
	_ObservationIDName[2016:2024],
	_ObservationIDName[2024:2034],
	_ObservationIDName[2034:2046],
	_ObservationIDName[2046:2059],
	_ObservationIDName[2059:2069],
	_ObservationIDName[2069:2079],
	_ObservationIDName[2079:2099],
	_ObservationIDName[2099:2113],
	_ObservationIDName[2113:2124],
	_ObservationIDName[2124:2135],
	_ObservationIDName[2135:2146],
	_ObservationIDName[2146:2176],
	_ObservationIDName[2176:2206],
	_ObservationIDName[2206:2232],
	_ObservationIDName[2232:2258],
	_ObservationIDName[2258:2273],
	_ObservationIDName[2273:2292],
	_ObservationIDName[2292:2311],
	_ObservationIDName[2311:2344],
	_ObservationIDName[2344:2352],
	_ObservationIDName[2352:2376],
	_ObservationIDName[2376:2389],
	_ObservationIDName[2389:2402],
	_ObservationIDName[2402:2415],
	_ObservationIDName[2415:2428],
	_ObservationIDName[2428:2441],
	_ObservationIDName[2441:2455],
	_ObservationIDName[2455:2469],
	_ObservationIDName[2469:2483],
	_ObservationIDName[2483:2495],
	_ObservationIDName[2495:2508],
	_ObservationIDName[2508:2524],
	_ObservationIDName[2524:2541],
	_ObservationIDName[2541:2554],
	_ObservationIDName[2554:2576],
	_ObservationIDName[2576:2589],
	_ObservationIDName[2589:2602],
	_ObservationIDName[2602:2615],
	_ObservationIDName[2615:2628],
	_ObservationIDName[2628:2641],
	_ObservationIDName[2641:2654],
	_ObservationIDName[2654:2667],
	_ObservationIDName[2667:2680],
	_ObservationIDName[2680:2693],
	_ObservationIDName[2693:2706],
	_ObservationIDName[2706:2719],
	_ObservationIDName[2719:2732],
	_ObservationIDName[2732:2745],
	_ObservationIDName[2745:2758],
	_ObservationIDName[2758:2773],
	_ObservationIDName[2773:2788],
	_ObservationIDName[2788:2803],
	_ObservationIDName[2803:2818],
	_ObservationIDName[2818:2830],
	_ObservationIDName[2830:2841],
	_ObservationIDName[2841:2853],
	_ObservationIDName[2853:2861],
	_ObservationIDName[2861:2869],
	_ObservationIDName[2869:2877],
	_ObservationIDName[2877:2900],
	_ObservationIDName[2900:2923],
	_ObservationIDName[2923:2946],
	_ObservationIDName[2946:2964],
	_ObservationIDName[2964:2987],
}

// ObservationIDString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ObservationIDString(s string) (ObservationID, error) {
	if val, ok := _ObservationIDNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ObservationIDNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ObservationID values", s)
}

// ObservationIDValues returns all values of the enum
func ObservationIDValues() []ObservationID {
	return _ObservationIDValues
}

// ObservationIDStrings returns a slice of all String values of the enum
func ObservationIDStrings() []string {
	strs := make([]string, len(_ObservationIDNames))
	copy(strs, _ObservationIDNames)
	return strs
}

// IsAObservationID returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ObservationID) IsAObservationID() bool {
	_, ok := _ObservationIDMap[i]
	return ok
}
