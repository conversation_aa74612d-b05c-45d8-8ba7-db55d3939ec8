package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateVaillant(base *Vaillant, meter func() (float64, error), battery func() (float64, error)) api.Charger {
	switch {
	case battery == nil && meter == nil:
		return base

	case battery == nil && meter != nil:
		return &struct {
			*Vaillant
			api.Meter
		}{
			Vaillant: base,
			Meter: &decorateVaillantMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && meter == nil:
		return &struct {
			*Vaillant
			api.Battery
		}{
			Vaillant: base,
			Battery: &decorateVaillantBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && meter != nil:
		return &struct {
			*Vaillant
			api.Battery
			api.Meter
		}{
			Vaillant: base,
			Battery: &decorateVaillantBatteryImpl{
				battery: battery,
			},
			Meter: &decorateVaillantMeterImpl{
				meter: meter,
			},
		}
	}

	return nil
}

type decorateVaillantBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateVaillantBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateVaillantMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateVaillantMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}
