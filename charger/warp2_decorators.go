package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateWarp2(base *Warp2, meter func() (float64, error), meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error), phaseVoltages func() (float64, float64, float64, error), identifier func() (string, error), phaseSwitcher func(int) error, phaseGetter func() (int, error)) api.Charger {
	switch {
	case identifier == nil && meter == nil && phaseSwitcher == nil:
		return base

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*Warp2
			api.Identifier
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*Warp2
			api.PhaseSwitcher
		}{
			Warp2: base,
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*Warp2
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*Warp2
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Warp2: base,
			Identifier: &decorateWarp2IdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateWarp2MeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateWarp2MeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateWarp2PhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateWarp2PhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateWarp2PhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateWarp2PhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}
	}

	return nil
}

type decorateWarp2IdentifierImpl struct {
	identifier func() (string, error)
}

func (impl *decorateWarp2IdentifierImpl) Identify() (string, error) {
	return impl.identifier()
}

type decorateWarp2MeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateWarp2MeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateWarp2MeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateWarp2MeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateWarp2PhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateWarp2PhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}

type decorateWarp2PhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateWarp2PhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateWarp2PhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateWarp2PhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}

type decorateWarp2PhaseVoltagesImpl struct {
	phaseVoltages func() (float64, float64, float64, error)
}

func (impl *decorateWarp2PhaseVoltagesImpl) Voltages() (float64, float64, float64, error) {
	return impl.phaseVoltages()
}
