package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateWeidmüller(base *Weidmüller, meterEnergy func() (float64, error)) api.Charger {
	switch {
	case meterEnergy == nil:
		return base

	case meterEnergy != nil:
		return &struct {
			*Weidmüller
			api.MeterEnergy
		}{
			Weidmüller: base,
			MeterEnergy: &decorateWeidmüllerMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}
	}

	return nil
}

type decorateWeidmüllerMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateWeidmüllerMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}
