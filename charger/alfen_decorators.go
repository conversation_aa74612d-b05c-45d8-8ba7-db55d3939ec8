package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateAlfen(base *Alfen, phaseSwitcher func(int) error, phaseGetter func() (int, error)) api.Charger {
	switch {
	case phaseSwitcher == nil:
		return base

	case phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*Alfen
			api.PhaseSwitcher
		}{
			Alfen: base,
			PhaseSwitcher: &decorateAlfenPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*Alfen
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Alfen: base,
			PhaseGetter: &decorateAlfenPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateAlfenPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateAlfenPhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateAlfenPhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateAlfenPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateAlfenPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
