package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateZaptec(base *Zaptec, phaseSwitcher func(int) error) api.Charger {
	switch {
	case phaseSwitcher == nil:
		return base

	case phaseSwitcher != nil:
		return &struct {
			*Zaptec
			api.PhaseSwitcher
		}{
			Zaptec: base,
			PhaseSwitcher: &decorateZaptecPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateZaptecPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateZaptecPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
