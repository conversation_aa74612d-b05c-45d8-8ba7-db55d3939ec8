package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateDaheimLaden(base *DaheimLadenMB, phaseSwitcher func(int) error, phaseGetter func() (int, error)) api.Charger {
	switch {
	case phaseSwitcher == nil:
		return base

	case phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*DaheimLadenMB
			api.PhaseSwitcher
		}{
			DaheimLadenMB: base,
			PhaseSwitcher: &decorateDaheimLadenPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*DaheimLadenMB
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			DaheimLadenMB: base,
			PhaseGetter: &decorateDaheimLadenPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateDaheimLadenPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateDaheimLadenPhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateDaheimLadenPhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateDaheimLadenPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateDaheimLadenPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
