package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateVestel(base *Vestel, phaseSwitcher func(int) error, phaseGetter func() (int, error), identifier func() (string, error)) api.Charger {
	switch {
	case identifier == nil && phaseSwitcher == nil:
		return base

	case identifier == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*Vestel
			api.PhaseSwitcher
		}{
			Vestel: base,
			PhaseSwitcher: &decorateVestelPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*Vestel
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Vestel: base,
			PhaseGetter: &decorateVestelPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateVestelPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && phaseSwitcher == nil:
		return &struct {
			*Vestel
			api.Identifier
		}{
			Vestel: base,
			Identifier: &decorateVestelIdentifierImpl{
				identifier: identifier,
			},
		}

	case identifier != nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*Vestel
			api.Identifier
			api.PhaseSwitcher
		}{
			Vestel: base,
			Identifier: &decorateVestelIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateVestelPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case identifier != nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*Vestel
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			Vestel: base,
			Identifier: &decorateVestelIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateVestelPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateVestelPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateVestelIdentifierImpl struct {
	identifier func() (string, error)
}

func (impl *decorateVestelIdentifierImpl) Identify() (string, error) {
	return impl.identifier()
}

type decorateVestelPhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateVestelPhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateVestelPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateVestelPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
