package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decoratePhoenixEMEth(base *PhoenixEMEth, meter func() (float64, error), meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error), phaseVoltages func() (float64, float64, float64, error)) api.Charger {
	switch {
	case meter == nil:
		return base

	case meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseVoltages == nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseVoltages == nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.MeterEnergy
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decoratePhoenixEMEthMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseVoltages == nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.PhaseCurrents
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decoratePhoenixEMEthPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseVoltages == nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decoratePhoenixEMEthMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decoratePhoenixEMEthPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseVoltages != nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.PhaseVoltages
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decoratePhoenixEMEthPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseVoltages != nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decoratePhoenixEMEthMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decoratePhoenixEMEthPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseVoltages != nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decoratePhoenixEMEthPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decoratePhoenixEMEthPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseVoltages != nil:
		return &struct {
			*PhoenixEMEth
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			PhoenixEMEth: base,
			Meter: &decoratePhoenixEMEthMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decoratePhoenixEMEthMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decoratePhoenixEMEthPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decoratePhoenixEMEthPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}
	}

	return nil
}

type decoratePhoenixEMEthMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decoratePhoenixEMEthMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decoratePhoenixEMEthMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decoratePhoenixEMEthMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decoratePhoenixEMEthPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decoratePhoenixEMEthPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}

type decoratePhoenixEMEthPhaseVoltagesImpl struct {
	phaseVoltages func() (float64, float64, float64, error)
}

func (impl *decoratePhoenixEMEthPhaseVoltagesImpl) Voltages() (float64, float64, float64, error) {
	return impl.phaseVoltages()
}
