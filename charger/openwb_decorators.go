package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateOpenWB(base *OpenWB, phaseSwitcher func(int) error, battery func() (float64, error)) api.Charger {
	switch {
	case battery == nil && phaseSwitcher == nil:
		return base

	case battery == nil && phaseSwitcher != nil:
		return &struct {
			*OpenWB
			api.PhaseSwitcher
		}{
			OpenWB: base,
			PhaseSwitcher: &decorateOpenWBPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && phaseSwitcher == nil:
		return &struct {
			*OpenWB
			api.Battery
		}{
			OpenWB: base,
			Battery: &decorateOpenWBBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && phaseSwitcher != nil:
		return &struct {
			*OpenWB
			api.Battery
			api.PhaseSwitcher
		}{
			OpenWB: base,
			Battery: &decorateOpenWBBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateOpenWBPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateOpenWBBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateOpenWBBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateOpenWBPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateOpenWBPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
