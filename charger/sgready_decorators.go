package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateSgReady(base *SgReady, meter func() (float64, error), meterEnergy func() (float64, error), battery func() (float64, error), socLimiter func() (int64, error)) api.Charger {
	switch {
	case battery == nil && meter == nil:
		return base

	case battery == nil && meter != nil && meterEnergy == nil:
		return &struct {
			*SgReady
			api.Meter
		}{
			SgReady: base,
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && meter != nil && meterEnergy != nil:
		return &struct {
			*SgReady
			api.Meter
			api.MeterEnergy
		}{
			SgReady: base,
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateSgReadyMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && meter == nil && socLimiter == nil:
		return &struct {
			*SgReady
			api.Battery
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && meter != nil && meterEnergy == nil && socLimiter == nil:
		return &struct {
			*SgReady
			api.Battery
			api.Meter
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && meter != nil && meterEnergy != nil && socLimiter == nil:
		return &struct {
			*SgReady
			api.Battery
			api.Meter
			api.MeterEnergy
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateSgReadyMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && meter == nil && socLimiter != nil:
		return &struct {
			*SgReady
			api.Battery
			api.SocLimiter
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
			SocLimiter: &decorateSgReadySocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && meter != nil && meterEnergy == nil && socLimiter != nil:
		return &struct {
			*SgReady
			api.Battery
			api.Meter
			api.SocLimiter
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateSgReadySocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && meter != nil && meterEnergy != nil && socLimiter != nil:
		return &struct {
			*SgReady
			api.Battery
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			SgReady: base,
			Battery: &decorateSgReadyBatteryImpl{
				battery: battery,
			},
			Meter: &decorateSgReadyMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateSgReadyMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateSgReadySocLimiterImpl{
				socLimiter: socLimiter,
			},
		}
	}

	return nil
}

type decorateSgReadyBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateSgReadyBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateSgReadyMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateSgReadyMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateSgReadyMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateSgReadyMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateSgReadySocLimiterImpl struct {
	socLimiter func() (int64, error)
}

func (impl *decorateSgReadySocLimiterImpl) GetLimitSoc() (int64, error) {
	return impl.socLimiter()
}
