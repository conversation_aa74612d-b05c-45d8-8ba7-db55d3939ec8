// Code generated by "enumer -type ObservationID"; DO NOT EDIT.

package zaptec

import (
	"fmt"
	"strings"
)

const _ObservationIDName = "IsOcppConnectedIsOnlinePulseUnknownOfflineModeCapabilitiesAuthenticationRequiredPaymentActivePaymentCurrencyPaymentSessionUnitPricePaymentEnergyUnitPricePaymentTimeUnitPriceCommunicationModePermanentCableLockProductCodeHmiBrightnessLockCableWhenConnectedSoftStartDisabledFirmwareApiHostMIDBlinkEnabledTemperatureInternal5TemperatureInternal6TemperatureInternalLimitTemperatureInternalMaxLimitHumidityVoltagePhase1VoltagePhase2VoltagePhase3CurrentPhase1CurrentPhase2CurrentPhase3ChargerMaxCurrentChargerMinCurrentActivePhasesTotalChargePowerRcdCurrentInternal12vCurrentPowerFactorSetPhasesMaxPhasesChargerOfflinePhaseChargerOfflineCurrentRcdCalibrationRcdCalibrationNoiseTotalChargePowerSessionSignedMeterValueSignedMeterValueIntervalSessionEnergyCountExportActiveSessionEnergyCountExportReactiveSessionEnergyCountImportActiveSessionEnergyCountImportReactiveSoftStartTimeChargeDurationChargeModeChargePilotLevelInstantChargePilotLevelAveragePilotVsProximityTimeChargeCurrentInstallationMaxLimitChargeCurrentSetChargerOperationModeIsEnabledIsStandAloneChargerCurrentUserUuidDeprecatedCableTypeNetworkTypeDetectedCarGridTestResultFinalStopActiveSessionIdentifierChargerCurrentUserUuidCompletedSessionNewChargeCardAuthenticationListVersionEnabledNfcTechnologiesLteRoamingDisabledInstallationIdRoutingIdNotificationsWarningsDiagnosticsModeInternalDiagnosticsLogDiagnosticsStringCommunicationSignalStrengthCloudConnectionStatusMcuResetSourceMcuRxErrorsMcuToVariscitePacketErrorsVarisciteToMcuPacketErrorsUptimeVarisciteUptimeMCUCarSessionLogCommunicationModeConfigurationInconsistencyRawPilotMonitorIT3PhaseDiagnosticsLogPilotTestResultsUnconditionalNfcDetectionIndicationEmcTestCounterProductionTestResultsPostProductionTestResultsSmartMainboardSoftwareApplicationVersionSmartMainboardSoftwareBootloaderVersionSmartComputerSoftwareApplicationVersionSmartComputerSoftwareBootloaderVersionSmartComputerHardwareVersionMacMainMacPlcModuleGridMacWiFiMacPlcModuleEvLteImsiLteMsisdnLteIccidLteImeiMIDCalibration"
const _ObservationIDLowerName = "isocppconnectedisonlinepulseunknownofflinemodecapabilitiesauthenticationrequiredpaymentactivepaymentcurrencypaymentsessionunitpricepaymentenergyunitpricepaymenttimeunitpricecommunicationmodepermanentcablelockproductcodehmibrightnesslockcablewhenconnectedsoftstartdisabledfirmwareapihostmidblinkenabledtemperatureinternal5temperatureinternal6temperatureinternallimittemperatureinternalmaxlimithumidityvoltagephase1voltagephase2voltagephase3currentphase1currentphase2currentphase3chargermaxcurrentchargermincurrentactivephasestotalchargepowerrcdcurrentinternal12vcurrentpowerfactorsetphasesmaxphaseschargerofflinephasechargerofflinecurrentrcdcalibrationrcdcalibrationnoisetotalchargepowersessionsignedmetervaluesignedmetervalueintervalsessionenergycountexportactivesessionenergycountexportreactivesessionenergycountimportactivesessionenergycountimportreactivesoftstarttimechargedurationchargemodechargepilotlevelinstantchargepilotlevelaveragepilotvsproximitytimechargecurrentinstallationmaxlimitchargecurrentsetchargeroperationmodeisenabledisstandalonechargercurrentuseruuiddeprecatedcabletypenetworktypedetectedcargridtestresultfinalstopactivesessionidentifierchargercurrentuseruuidcompletedsessionnewchargecardauthenticationlistversionenablednfctechnologieslteroamingdisabledinstallationidroutingidnotificationswarningsdiagnosticsmodeinternaldiagnosticslogdiagnosticsstringcommunicationsignalstrengthcloudconnectionstatusmcuresetsourcemcurxerrorsmcutovariscitepacketerrorsvariscitetomcupacketerrorsuptimevarisciteuptimemcucarsessionlogcommunicationmodeconfigurationinconsistencyrawpilotmonitorit3phasediagnosticslogpilottestresultsunconditionalnfcdetectionindicationemctestcounterproductiontestresultspostproductiontestresultssmartmainboardsoftwareapplicationversionsmartmainboardsoftwarebootloaderversionsmartcomputersoftwareapplicationversionsmartcomputersoftwarebootloaderversionsmartcomputerhardwareversionmacmainmacplcmodulegridmacwifimacplcmoduleevlteimsiltemsisdnlteiccidlteimeimidcalibration"

var _ObservationIDMap = map[ObservationID]string{
	-3:  _ObservationIDName[0:15],
	-2:  _ObservationIDName[15:23],
	-1:  _ObservationIDName[23:28],
	0:   _ObservationIDName[28:35],
	1:   _ObservationIDName[35:46],
	100: _ObservationIDName[46:58],
	120: _ObservationIDName[58:80],
	130: _ObservationIDName[80:93],
	131: _ObservationIDName[93:108],
	132: _ObservationIDName[108:131],
	133: _ObservationIDName[131:153],
	134: _ObservationIDName[153:173],
	150: _ObservationIDName[173:190],
	151: _ObservationIDName[190:208],
	152: _ObservationIDName[208:219],
	153: _ObservationIDName[219:232],
	154: _ObservationIDName[232:254],
	155: _ObservationIDName[254:271],
	156: _ObservationIDName[271:286],
	170: _ObservationIDName[286:301],
	201: _ObservationIDName[301:321],
	202: _ObservationIDName[321:341],
	203: _ObservationIDName[341:365],
	241: _ObservationIDName[365:392],
	270: _ObservationIDName[392:400],
	501: _ObservationIDName[400:413],
	502: _ObservationIDName[413:426],
	503: _ObservationIDName[426:439],
	507: _ObservationIDName[439:452],
	508: _ObservationIDName[452:465],
	509: _ObservationIDName[465:478],
	510: _ObservationIDName[478:495],
	511: _ObservationIDName[495:512],
	512: _ObservationIDName[512:524],
	513: _ObservationIDName[524:540],
	515: _ObservationIDName[540:550],
	517: _ObservationIDName[550:568],
	518: _ObservationIDName[568:579],
	519: _ObservationIDName[579:588],
	520: _ObservationIDName[588:597],
	522: _ObservationIDName[597:616],
	523: _ObservationIDName[616:637],
	540: _ObservationIDName[637:651],
	541: _ObservationIDName[651:670],
	553: _ObservationIDName[670:693],
	554: _ObservationIDName[693:709],
	555: _ObservationIDName[709:733],
	560: _ObservationIDName[733:763],
	561: _ObservationIDName[763:795],
	562: _ObservationIDName[795:825],
	563: _ObservationIDName[825:857],
	570: _ObservationIDName[857:870],
	701: _ObservationIDName[870:884],
	702: _ObservationIDName[884:894],
	703: _ObservationIDName[894:917],
	704: _ObservationIDName[917:940],
	706: _ObservationIDName[940:960],
	707: _ObservationIDName[960:993],
	708: _ObservationIDName[993:1009],
	710: _ObservationIDName[1009:1029],
	711: _ObservationIDName[1029:1038],
	712: _ObservationIDName[1038:1050],
	713: _ObservationIDName[1050:1082],
	714: _ObservationIDName[1082:1091],
	715: _ObservationIDName[1091:1102],
	716: _ObservationIDName[1102:1113],
	717: _ObservationIDName[1113:1127],
	718: _ObservationIDName[1127:1142],
	721: _ObservationIDName[1142:1159],
	722: _ObservationIDName[1159:1181],
	723: _ObservationIDName[1181:1197],
	750: _ObservationIDName[1197:1210],
	751: _ObservationIDName[1210:1235],
	752: _ObservationIDName[1235:1257],
	753: _ObservationIDName[1257:1275],
	800: _ObservationIDName[1275:1289],
	801: _ObservationIDName[1289:1298],
	803: _ObservationIDName[1298:1311],
	804: _ObservationIDName[1311:1319],
	805: _ObservationIDName[1319:1334],
	807: _ObservationIDName[1334:1356],
	808: _ObservationIDName[1356:1373],
	809: _ObservationIDName[1373:1400],
	810: _ObservationIDName[1400:1421],
	811: _ObservationIDName[1421:1435],
	812: _ObservationIDName[1435:1446],
	813: _ObservationIDName[1446:1472],
	814: _ObservationIDName[1472:1498],
	820: _ObservationIDName[1498:1513],
	821: _ObservationIDName[1513:1522],
	850: _ObservationIDName[1522:1535],
	851: _ObservationIDName[1535:1578],
	852: _ObservationIDName[1578:1593],
	853: _ObservationIDName[1593:1615],
	854: _ObservationIDName[1615:1631],
	855: _ObservationIDName[1631:1666],
	899: _ObservationIDName[1666:1680],
	900: _ObservationIDName[1680:1701],
	901: _ObservationIDName[1701:1726],
	908: _ObservationIDName[1726:1766],
	909: _ObservationIDName[1766:1805],
	911: _ObservationIDName[1805:1844],
	912: _ObservationIDName[1844:1882],
	913: _ObservationIDName[1882:1910],
	950: _ObservationIDName[1910:1917],
	951: _ObservationIDName[1917:1933],
	952: _ObservationIDName[1933:1940],
	953: _ObservationIDName[1940:1954],
	960: _ObservationIDName[1954:1961],
	961: _ObservationIDName[1961:1970],
	962: _ObservationIDName[1970:1978],
	963: _ObservationIDName[1978:1985],
	980: _ObservationIDName[1985:1999],
}

func (i ObservationID) String() string {
	if str, ok := _ObservationIDMap[i]; ok {
		return str
	}
	return fmt.Sprintf("ObservationID(%d)", i)
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ObservationIDNoOp() {
	var x [1]struct{}
	_ = x[IsOcppConnected-(-3)]
	_ = x[IsOnline-(-2)]
	_ = x[Pulse-(-1)]
	_ = x[Unknown-(0)]
	_ = x[OfflineMode-(1)]
	_ = x[Capabilities-(100)]
	_ = x[AuthenticationRequired-(120)]
	_ = x[PaymentActive-(130)]
	_ = x[PaymentCurrency-(131)]
	_ = x[PaymentSessionUnitPrice-(132)]
	_ = x[PaymentEnergyUnitPrice-(133)]
	_ = x[PaymentTimeUnitPrice-(134)]
	_ = x[CommunicationMode-(150)]
	_ = x[PermanentCableLock-(151)]
	_ = x[ProductCode-(152)]
	_ = x[HmiBrightness-(153)]
	_ = x[LockCableWhenConnected-(154)]
	_ = x[SoftStartDisabled-(155)]
	_ = x[FirmwareApiHost-(156)]
	_ = x[MIDBlinkEnabled-(170)]
	_ = x[TemperatureInternal5-(201)]
	_ = x[TemperatureInternal6-(202)]
	_ = x[TemperatureInternalLimit-(203)]
	_ = x[TemperatureInternalMaxLimit-(241)]
	_ = x[Humidity-(270)]
	_ = x[VoltagePhase1-(501)]
	_ = x[VoltagePhase2-(502)]
	_ = x[VoltagePhase3-(503)]
	_ = x[CurrentPhase1-(507)]
	_ = x[CurrentPhase2-(508)]
	_ = x[CurrentPhase3-(509)]
	_ = x[ChargerMaxCurrent-(510)]
	_ = x[ChargerMinCurrent-(511)]
	_ = x[ActivePhases-(512)]
	_ = x[TotalChargePower-(513)]
	_ = x[RcdCurrent-(515)]
	_ = x[Internal12vCurrent-(517)]
	_ = x[PowerFactor-(518)]
	_ = x[SetPhases-(519)]
	_ = x[MaxPhases-(520)]
	_ = x[ChargerOfflinePhase-(522)]
	_ = x[ChargerOfflineCurrent-(523)]
	_ = x[RcdCalibration-(540)]
	_ = x[RcdCalibrationNoise-(541)]
	_ = x[TotalChargePowerSession-(553)]
	_ = x[SignedMeterValue-(554)]
	_ = x[SignedMeterValueInterval-(555)]
	_ = x[SessionEnergyCountExportActive-(560)]
	_ = x[SessionEnergyCountExportReactive-(561)]
	_ = x[SessionEnergyCountImportActive-(562)]
	_ = x[SessionEnergyCountImportReactive-(563)]
	_ = x[SoftStartTime-(570)]
	_ = x[ChargeDuration-(701)]
	_ = x[ChargeMode-(702)]
	_ = x[ChargePilotLevelInstant-(703)]
	_ = x[ChargePilotLevelAverage-(704)]
	_ = x[PilotVsProximityTime-(706)]
	_ = x[ChargeCurrentInstallationMaxLimit-(707)]
	_ = x[ChargeCurrentSet-(708)]
	_ = x[ChargerOperationMode-(710)]
	_ = x[IsEnabled-(711)]
	_ = x[IsStandAlone-(712)]
	_ = x[ChargerCurrentUserUuidDeprecated-(713)]
	_ = x[CableType-(714)]
	_ = x[NetworkType-(715)]
	_ = x[DetectedCar-(716)]
	_ = x[GridTestResult-(717)]
	_ = x[FinalStopActive-(718)]
	_ = x[SessionIdentifier-(721)]
	_ = x[ChargerCurrentUserUuid-(722)]
	_ = x[CompletedSession-(723)]
	_ = x[NewChargeCard-(750)]
	_ = x[AuthenticationListVersion-(751)]
	_ = x[EnabledNfcTechnologies-(752)]
	_ = x[LteRoamingDisabled-(753)]
	_ = x[InstallationId-(800)]
	_ = x[RoutingId-(801)]
	_ = x[Notifications-(803)]
	_ = x[Warnings-(804)]
	_ = x[DiagnosticsMode-(805)]
	_ = x[InternalDiagnosticsLog-(807)]
	_ = x[DiagnosticsString-(808)]
	_ = x[CommunicationSignalStrength-(809)]
	_ = x[CloudConnectionStatus-(810)]
	_ = x[McuResetSource-(811)]
	_ = x[McuRxErrors-(812)]
	_ = x[McuToVariscitePacketErrors-(813)]
	_ = x[VarisciteToMcuPacketErrors-(814)]
	_ = x[UptimeVariscite-(820)]
	_ = x[UptimeMCU-(821)]
	_ = x[CarSessionLog-(850)]
	_ = x[CommunicationModeConfigurationInconsistency-(851)]
	_ = x[RawPilotMonitor-(852)]
	_ = x[IT3PhaseDiagnosticsLog-(853)]
	_ = x[PilotTestResults-(854)]
	_ = x[UnconditionalNfcDetectionIndication-(855)]
	_ = x[EmcTestCounter-(899)]
	_ = x[ProductionTestResults-(900)]
	_ = x[PostProductionTestResults-(901)]
	_ = x[SmartMainboardSoftwareApplicationVersion-(908)]
	_ = x[SmartMainboardSoftwareBootloaderVersion-(909)]
	_ = x[SmartComputerSoftwareApplicationVersion-(911)]
	_ = x[SmartComputerSoftwareBootloaderVersion-(912)]
	_ = x[SmartComputerHardwareVersion-(913)]
	_ = x[MacMain-(950)]
	_ = x[MacPlcModuleGrid-(951)]
	_ = x[MacWiFi-(952)]
	_ = x[MacPlcModuleEv-(953)]
	_ = x[LteImsi-(960)]
	_ = x[LteMsisdn-(961)]
	_ = x[LteIccid-(962)]
	_ = x[LteImei-(963)]
	_ = x[MIDCalibration-(980)]
}

var _ObservationIDValues = []ObservationID{IsOcppConnected, IsOnline, Pulse, Unknown, OfflineMode, Capabilities, AuthenticationRequired, PaymentActive, PaymentCurrency, PaymentSessionUnitPrice, PaymentEnergyUnitPrice, PaymentTimeUnitPrice, CommunicationMode, PermanentCableLock, ProductCode, HmiBrightness, LockCableWhenConnected, SoftStartDisabled, FirmwareApiHost, MIDBlinkEnabled, TemperatureInternal5, TemperatureInternal6, TemperatureInternalLimit, TemperatureInternalMaxLimit, Humidity, VoltagePhase1, VoltagePhase2, VoltagePhase3, CurrentPhase1, CurrentPhase2, CurrentPhase3, ChargerMaxCurrent, ChargerMinCurrent, ActivePhases, TotalChargePower, RcdCurrent, Internal12vCurrent, PowerFactor, SetPhases, MaxPhases, ChargerOfflinePhase, ChargerOfflineCurrent, RcdCalibration, RcdCalibrationNoise, TotalChargePowerSession, SignedMeterValue, SignedMeterValueInterval, SessionEnergyCountExportActive, SessionEnergyCountExportReactive, SessionEnergyCountImportActive, SessionEnergyCountImportReactive, SoftStartTime, ChargeDuration, ChargeMode, ChargePilotLevelInstant, ChargePilotLevelAverage, PilotVsProximityTime, ChargeCurrentInstallationMaxLimit, ChargeCurrentSet, ChargerOperationMode, IsEnabled, IsStandAlone, ChargerCurrentUserUuidDeprecated, CableType, NetworkType, DetectedCar, GridTestResult, FinalStopActive, SessionIdentifier, ChargerCurrentUserUuid, CompletedSession, NewChargeCard, AuthenticationListVersion, EnabledNfcTechnologies, LteRoamingDisabled, InstallationId, RoutingId, Notifications, Warnings, DiagnosticsMode, InternalDiagnosticsLog, DiagnosticsString, CommunicationSignalStrength, CloudConnectionStatus, McuResetSource, McuRxErrors, McuToVariscitePacketErrors, VarisciteToMcuPacketErrors, UptimeVariscite, UptimeMCU, CarSessionLog, CommunicationModeConfigurationInconsistency, RawPilotMonitor, IT3PhaseDiagnosticsLog, PilotTestResults, UnconditionalNfcDetectionIndication, EmcTestCounter, ProductionTestResults, PostProductionTestResults, SmartMainboardSoftwareApplicationVersion, SmartMainboardSoftwareBootloaderVersion, SmartComputerSoftwareApplicationVersion, SmartComputerSoftwareBootloaderVersion, SmartComputerHardwareVersion, MacMain, MacPlcModuleGrid, MacWiFi, MacPlcModuleEv, LteImsi, LteMsisdn, LteIccid, LteImei, MIDCalibration}

var _ObservationIDNameToValueMap = map[string]ObservationID{
	_ObservationIDName[0:15]:           IsOcppConnected,
	_ObservationIDLowerName[0:15]:      IsOcppConnected,
	_ObservationIDName[15:23]:          IsOnline,
	_ObservationIDLowerName[15:23]:     IsOnline,
	_ObservationIDName[23:28]:          Pulse,
	_ObservationIDLowerName[23:28]:     Pulse,
	_ObservationIDName[28:35]:          Unknown,
	_ObservationIDLowerName[28:35]:     Unknown,
	_ObservationIDName[35:46]:          OfflineMode,
	_ObservationIDLowerName[35:46]:     OfflineMode,
	_ObservationIDName[46:58]:          Capabilities,
	_ObservationIDLowerName[46:58]:     Capabilities,
	_ObservationIDName[58:80]:          AuthenticationRequired,
	_ObservationIDLowerName[58:80]:     AuthenticationRequired,
	_ObservationIDName[80:93]:          PaymentActive,
	_ObservationIDLowerName[80:93]:     PaymentActive,
	_ObservationIDName[93:108]:         PaymentCurrency,
	_ObservationIDLowerName[93:108]:    PaymentCurrency,
	_ObservationIDName[108:131]:        PaymentSessionUnitPrice,
	_ObservationIDLowerName[108:131]:   PaymentSessionUnitPrice,
	_ObservationIDName[131:153]:        PaymentEnergyUnitPrice,
	_ObservationIDLowerName[131:153]:   PaymentEnergyUnitPrice,
	_ObservationIDName[153:173]:        PaymentTimeUnitPrice,
	_ObservationIDLowerName[153:173]:   PaymentTimeUnitPrice,
	_ObservationIDName[173:190]:        CommunicationMode,
	_ObservationIDLowerName[173:190]:   CommunicationMode,
	_ObservationIDName[190:208]:        PermanentCableLock,
	_ObservationIDLowerName[190:208]:   PermanentCableLock,
	_ObservationIDName[208:219]:        ProductCode,
	_ObservationIDLowerName[208:219]:   ProductCode,
	_ObservationIDName[219:232]:        HmiBrightness,
	_ObservationIDLowerName[219:232]:   HmiBrightness,
	_ObservationIDName[232:254]:        LockCableWhenConnected,
	_ObservationIDLowerName[232:254]:   LockCableWhenConnected,
	_ObservationIDName[254:271]:        SoftStartDisabled,
	_ObservationIDLowerName[254:271]:   SoftStartDisabled,
	_ObservationIDName[271:286]:        FirmwareApiHost,
	_ObservationIDLowerName[271:286]:   FirmwareApiHost,
	_ObservationIDName[286:301]:        MIDBlinkEnabled,
	_ObservationIDLowerName[286:301]:   MIDBlinkEnabled,
	_ObservationIDName[301:321]:        TemperatureInternal5,
	_ObservationIDLowerName[301:321]:   TemperatureInternal5,
	_ObservationIDName[321:341]:        TemperatureInternal6,
	_ObservationIDLowerName[321:341]:   TemperatureInternal6,
	_ObservationIDName[341:365]:        TemperatureInternalLimit,
	_ObservationIDLowerName[341:365]:   TemperatureInternalLimit,
	_ObservationIDName[365:392]:        TemperatureInternalMaxLimit,
	_ObservationIDLowerName[365:392]:   TemperatureInternalMaxLimit,
	_ObservationIDName[392:400]:        Humidity,
	_ObservationIDLowerName[392:400]:   Humidity,
	_ObservationIDName[400:413]:        VoltagePhase1,
	_ObservationIDLowerName[400:413]:   VoltagePhase1,
	_ObservationIDName[413:426]:        VoltagePhase2,
	_ObservationIDLowerName[413:426]:   VoltagePhase2,
	_ObservationIDName[426:439]:        VoltagePhase3,
	_ObservationIDLowerName[426:439]:   VoltagePhase3,
	_ObservationIDName[439:452]:        CurrentPhase1,
	_ObservationIDLowerName[439:452]:   CurrentPhase1,
	_ObservationIDName[452:465]:        CurrentPhase2,
	_ObservationIDLowerName[452:465]:   CurrentPhase2,
	_ObservationIDName[465:478]:        CurrentPhase3,
	_ObservationIDLowerName[465:478]:   CurrentPhase3,
	_ObservationIDName[478:495]:        ChargerMaxCurrent,
	_ObservationIDLowerName[478:495]:   ChargerMaxCurrent,
	_ObservationIDName[495:512]:        ChargerMinCurrent,
	_ObservationIDLowerName[495:512]:   ChargerMinCurrent,
	_ObservationIDName[512:524]:        ActivePhases,
	_ObservationIDLowerName[512:524]:   ActivePhases,
	_ObservationIDName[524:540]:        TotalChargePower,
	_ObservationIDLowerName[524:540]:   TotalChargePower,
	_ObservationIDName[540:550]:        RcdCurrent,
	_ObservationIDLowerName[540:550]:   RcdCurrent,
	_ObservationIDName[550:568]:        Internal12vCurrent,
	_ObservationIDLowerName[550:568]:   Internal12vCurrent,
	_ObservationIDName[568:579]:        PowerFactor,
	_ObservationIDLowerName[568:579]:   PowerFactor,
	_ObservationIDName[579:588]:        SetPhases,
	_ObservationIDLowerName[579:588]:   SetPhases,
	_ObservationIDName[588:597]:        MaxPhases,
	_ObservationIDLowerName[588:597]:   MaxPhases,
	_ObservationIDName[597:616]:        ChargerOfflinePhase,
	_ObservationIDLowerName[597:616]:   ChargerOfflinePhase,
	_ObservationIDName[616:637]:        ChargerOfflineCurrent,
	_ObservationIDLowerName[616:637]:   ChargerOfflineCurrent,
	_ObservationIDName[637:651]:        RcdCalibration,
	_ObservationIDLowerName[637:651]:   RcdCalibration,
	_ObservationIDName[651:670]:        RcdCalibrationNoise,
	_ObservationIDLowerName[651:670]:   RcdCalibrationNoise,
	_ObservationIDName[670:693]:        TotalChargePowerSession,
	_ObservationIDLowerName[670:693]:   TotalChargePowerSession,
	_ObservationIDName[693:709]:        SignedMeterValue,
	_ObservationIDLowerName[693:709]:   SignedMeterValue,
	_ObservationIDName[709:733]:        SignedMeterValueInterval,
	_ObservationIDLowerName[709:733]:   SignedMeterValueInterval,
	_ObservationIDName[733:763]:        SessionEnergyCountExportActive,
	_ObservationIDLowerName[733:763]:   SessionEnergyCountExportActive,
	_ObservationIDName[763:795]:        SessionEnergyCountExportReactive,
	_ObservationIDLowerName[763:795]:   SessionEnergyCountExportReactive,
	_ObservationIDName[795:825]:        SessionEnergyCountImportActive,
	_ObservationIDLowerName[795:825]:   SessionEnergyCountImportActive,
	_ObservationIDName[825:857]:        SessionEnergyCountImportReactive,
	_ObservationIDLowerName[825:857]:   SessionEnergyCountImportReactive,
	_ObservationIDName[857:870]:        SoftStartTime,
	_ObservationIDLowerName[857:870]:   SoftStartTime,
	_ObservationIDName[870:884]:        ChargeDuration,
	_ObservationIDLowerName[870:884]:   ChargeDuration,
	_ObservationIDName[884:894]:        ChargeMode,
	_ObservationIDLowerName[884:894]:   ChargeMode,
	_ObservationIDName[894:917]:        ChargePilotLevelInstant,
	_ObservationIDLowerName[894:917]:   ChargePilotLevelInstant,
	_ObservationIDName[917:940]:        ChargePilotLevelAverage,
	_ObservationIDLowerName[917:940]:   ChargePilotLevelAverage,
	_ObservationIDName[940:960]:        PilotVsProximityTime,
	_ObservationIDLowerName[940:960]:   PilotVsProximityTime,
	_ObservationIDName[960:993]:        ChargeCurrentInstallationMaxLimit,
	_ObservationIDLowerName[960:993]:   ChargeCurrentInstallationMaxLimit,
	_ObservationIDName[993:1009]:       ChargeCurrentSet,
	_ObservationIDLowerName[993:1009]:  ChargeCurrentSet,
	_ObservationIDName[1009:1029]:      ChargerOperationMode,
	_ObservationIDLowerName[1009:1029]: ChargerOperationMode,
	_ObservationIDName[1029:1038]:      IsEnabled,
	_ObservationIDLowerName[1029:1038]: IsEnabled,
	_ObservationIDName[1038:1050]:      IsStandAlone,
	_ObservationIDLowerName[1038:1050]: IsStandAlone,
	_ObservationIDName[1050:1082]:      ChargerCurrentUserUuidDeprecated,
	_ObservationIDLowerName[1050:1082]: ChargerCurrentUserUuidDeprecated,
	_ObservationIDName[1082:1091]:      CableType,
	_ObservationIDLowerName[1082:1091]: CableType,
	_ObservationIDName[1091:1102]:      NetworkType,
	_ObservationIDLowerName[1091:1102]: NetworkType,
	_ObservationIDName[1102:1113]:      DetectedCar,
	_ObservationIDLowerName[1102:1113]: DetectedCar,
	_ObservationIDName[1113:1127]:      GridTestResult,
	_ObservationIDLowerName[1113:1127]: GridTestResult,
	_ObservationIDName[1127:1142]:      FinalStopActive,
	_ObservationIDLowerName[1127:1142]: FinalStopActive,
	_ObservationIDName[1142:1159]:      SessionIdentifier,
	_ObservationIDLowerName[1142:1159]: SessionIdentifier,
	_ObservationIDName[1159:1181]:      ChargerCurrentUserUuid,
	_ObservationIDLowerName[1159:1181]: ChargerCurrentUserUuid,
	_ObservationIDName[1181:1197]:      CompletedSession,
	_ObservationIDLowerName[1181:1197]: CompletedSession,
	_ObservationIDName[1197:1210]:      NewChargeCard,
	_ObservationIDLowerName[1197:1210]: NewChargeCard,
	_ObservationIDName[1210:1235]:      AuthenticationListVersion,
	_ObservationIDLowerName[1210:1235]: AuthenticationListVersion,
	_ObservationIDName[1235:1257]:      EnabledNfcTechnologies,
	_ObservationIDLowerName[1235:1257]: EnabledNfcTechnologies,
	_ObservationIDName[1257:1275]:      LteRoamingDisabled,
	_ObservationIDLowerName[1257:1275]: LteRoamingDisabled,
	_ObservationIDName[1275:1289]:      InstallationId,
	_ObservationIDLowerName[1275:1289]: InstallationId,
	_ObservationIDName[1289:1298]:      RoutingId,
	_ObservationIDLowerName[1289:1298]: RoutingId,
	_ObservationIDName[1298:1311]:      Notifications,
	_ObservationIDLowerName[1298:1311]: Notifications,
	_ObservationIDName[1311:1319]:      Warnings,
	_ObservationIDLowerName[1311:1319]: Warnings,
	_ObservationIDName[1319:1334]:      DiagnosticsMode,
	_ObservationIDLowerName[1319:1334]: DiagnosticsMode,
	_ObservationIDName[1334:1356]:      InternalDiagnosticsLog,
	_ObservationIDLowerName[1334:1356]: InternalDiagnosticsLog,
	_ObservationIDName[1356:1373]:      DiagnosticsString,
	_ObservationIDLowerName[1356:1373]: DiagnosticsString,
	_ObservationIDName[1373:1400]:      CommunicationSignalStrength,
	_ObservationIDLowerName[1373:1400]: CommunicationSignalStrength,
	_ObservationIDName[1400:1421]:      CloudConnectionStatus,
	_ObservationIDLowerName[1400:1421]: CloudConnectionStatus,
	_ObservationIDName[1421:1435]:      McuResetSource,
	_ObservationIDLowerName[1421:1435]: McuResetSource,
	_ObservationIDName[1435:1446]:      McuRxErrors,
	_ObservationIDLowerName[1435:1446]: McuRxErrors,
	_ObservationIDName[1446:1472]:      McuToVariscitePacketErrors,
	_ObservationIDLowerName[1446:1472]: McuToVariscitePacketErrors,
	_ObservationIDName[1472:1498]:      VarisciteToMcuPacketErrors,
	_ObservationIDLowerName[1472:1498]: VarisciteToMcuPacketErrors,
	_ObservationIDName[1498:1513]:      UptimeVariscite,
	_ObservationIDLowerName[1498:1513]: UptimeVariscite,
	_ObservationIDName[1513:1522]:      UptimeMCU,
	_ObservationIDLowerName[1513:1522]: UptimeMCU,
	_ObservationIDName[1522:1535]:      CarSessionLog,
	_ObservationIDLowerName[1522:1535]: CarSessionLog,
	_ObservationIDName[1535:1578]:      CommunicationModeConfigurationInconsistency,
	_ObservationIDLowerName[1535:1578]: CommunicationModeConfigurationInconsistency,
	_ObservationIDName[1578:1593]:      RawPilotMonitor,
	_ObservationIDLowerName[1578:1593]: RawPilotMonitor,
	_ObservationIDName[1593:1615]:      IT3PhaseDiagnosticsLog,
	_ObservationIDLowerName[1593:1615]: IT3PhaseDiagnosticsLog,
	_ObservationIDName[1615:1631]:      PilotTestResults,
	_ObservationIDLowerName[1615:1631]: PilotTestResults,
	_ObservationIDName[1631:1666]:      UnconditionalNfcDetectionIndication,
	_ObservationIDLowerName[1631:1666]: UnconditionalNfcDetectionIndication,
	_ObservationIDName[1666:1680]:      EmcTestCounter,
	_ObservationIDLowerName[1666:1680]: EmcTestCounter,
	_ObservationIDName[1680:1701]:      ProductionTestResults,
	_ObservationIDLowerName[1680:1701]: ProductionTestResults,
	_ObservationIDName[1701:1726]:      PostProductionTestResults,
	_ObservationIDLowerName[1701:1726]: PostProductionTestResults,
	_ObservationIDName[1726:1766]:      SmartMainboardSoftwareApplicationVersion,
	_ObservationIDLowerName[1726:1766]: SmartMainboardSoftwareApplicationVersion,
	_ObservationIDName[1766:1805]:      SmartMainboardSoftwareBootloaderVersion,
	_ObservationIDLowerName[1766:1805]: SmartMainboardSoftwareBootloaderVersion,
	_ObservationIDName[1805:1844]:      SmartComputerSoftwareApplicationVersion,
	_ObservationIDLowerName[1805:1844]: SmartComputerSoftwareApplicationVersion,
	_ObservationIDName[1844:1882]:      SmartComputerSoftwareBootloaderVersion,
	_ObservationIDLowerName[1844:1882]: SmartComputerSoftwareBootloaderVersion,
	_ObservationIDName[1882:1910]:      SmartComputerHardwareVersion,
	_ObservationIDLowerName[1882:1910]: SmartComputerHardwareVersion,
	_ObservationIDName[1910:1917]:      MacMain,
	_ObservationIDLowerName[1910:1917]: MacMain,
	_ObservationIDName[1917:1933]:      MacPlcModuleGrid,
	_ObservationIDLowerName[1917:1933]: MacPlcModuleGrid,
	_ObservationIDName[1933:1940]:      MacWiFi,
	_ObservationIDLowerName[1933:1940]: MacWiFi,
	_ObservationIDName[1940:1954]:      MacPlcModuleEv,
	_ObservationIDLowerName[1940:1954]: MacPlcModuleEv,
	_ObservationIDName[1954:1961]:      LteImsi,
	_ObservationIDLowerName[1954:1961]: LteImsi,
	_ObservationIDName[1961:1970]:      LteMsisdn,
	_ObservationIDLowerName[1961:1970]: LteMsisdn,
	_ObservationIDName[1970:1978]:      LteIccid,
	_ObservationIDLowerName[1970:1978]: LteIccid,
	_ObservationIDName[1978:1985]:      LteImei,
	_ObservationIDLowerName[1978:1985]: LteImei,
	_ObservationIDName[1985:1999]:      MIDCalibration,
	_ObservationIDLowerName[1985:1999]: MIDCalibration,
}

var _ObservationIDNames = []string{
	_ObservationIDName[0:15],
	_ObservationIDName[15:23],
	_ObservationIDName[23:28],
	_ObservationIDName[28:35],
	_ObservationIDName[35:46],
	_ObservationIDName[46:58],
	_ObservationIDName[58:80],
	_ObservationIDName[80:93],
	_ObservationIDName[93:108],
	_ObservationIDName[108:131],
	_ObservationIDName[131:153],
	_ObservationIDName[153:173],
	_ObservationIDName[173:190],
	_ObservationIDName[190:208],
	_ObservationIDName[208:219],
	_ObservationIDName[219:232],
	_ObservationIDName[232:254],
	_ObservationIDName[254:271],
	_ObservationIDName[271:286],
	_ObservationIDName[286:301],
	_ObservationIDName[301:321],
	_ObservationIDName[321:341],
	_ObservationIDName[341:365],
	_ObservationIDName[365:392],
	_ObservationIDName[392:400],
	_ObservationIDName[400:413],
	_ObservationIDName[413:426],
	_ObservationIDName[426:439],
	_ObservationIDName[439:452],
	_ObservationIDName[452:465],
	_ObservationIDName[465:478],
	_ObservationIDName[478:495],
	_ObservationIDName[495:512],
	_ObservationIDName[512:524],
	_ObservationIDName[524:540],
	_ObservationIDName[540:550],
	_ObservationIDName[550:568],
	_ObservationIDName[568:579],
	_ObservationIDName[579:588],
	_ObservationIDName[588:597],
	_ObservationIDName[597:616],
	_ObservationIDName[616:637],
	_ObservationIDName[637:651],
	_ObservationIDName[651:670],
	_ObservationIDName[670:693],
	_ObservationIDName[693:709],
	_ObservationIDName[709:733],
	_ObservationIDName[733:763],
	_ObservationIDName[763:795],
	_ObservationIDName[795:825],
	_ObservationIDName[825:857],
	_ObservationIDName[857:870],
	_ObservationIDName[870:884],
	_ObservationIDName[884:894],
	_ObservationIDName[894:917],
	_ObservationIDName[917:940],
	_ObservationIDName[940:960],
	_ObservationIDName[960:993],
	_ObservationIDName[993:1009],
	_ObservationIDName[1009:1029],
	_ObservationIDName[1029:1038],
	_ObservationIDName[1038:1050],
	_ObservationIDName[1050:1082],
	_ObservationIDName[1082:1091],
	_ObservationIDName[1091:1102],
	_ObservationIDName[1102:1113],
	_ObservationIDName[1113:1127],
	_ObservationIDName[1127:1142],
	_ObservationIDName[1142:1159],
	_ObservationIDName[1159:1181],
	_ObservationIDName[1181:1197],
	_ObservationIDName[1197:1210],
	_ObservationIDName[1210:1235],
	_ObservationIDName[1235:1257],
	_ObservationIDName[1257:1275],
	_ObservationIDName[1275:1289],
	_ObservationIDName[1289:1298],
	_ObservationIDName[1298:1311],
	_ObservationIDName[1311:1319],
	_ObservationIDName[1319:1334],
	_ObservationIDName[1334:1356],
	_ObservationIDName[1356:1373],
	_ObservationIDName[1373:1400],
	_ObservationIDName[1400:1421],
	_ObservationIDName[1421:1435],
	_ObservationIDName[1435:1446],
	_ObservationIDName[1446:1472],
	_ObservationIDName[1472:1498],
	_ObservationIDName[1498:1513],
	_ObservationIDName[1513:1522],
	_ObservationIDName[1522:1535],
	_ObservationIDName[1535:1578],
	_ObservationIDName[1578:1593],
	_ObservationIDName[1593:1615],
	_ObservationIDName[1615:1631],
	_ObservationIDName[1631:1666],
	_ObservationIDName[1666:1680],
	_ObservationIDName[1680:1701],
	_ObservationIDName[1701:1726],
	_ObservationIDName[1726:1766],
	_ObservationIDName[1766:1805],
	_ObservationIDName[1805:1844],
	_ObservationIDName[1844:1882],
	_ObservationIDName[1882:1910],
	_ObservationIDName[1910:1917],
	_ObservationIDName[1917:1933],
	_ObservationIDName[1933:1940],
	_ObservationIDName[1940:1954],
	_ObservationIDName[1954:1961],
	_ObservationIDName[1961:1970],
	_ObservationIDName[1970:1978],
	_ObservationIDName[1978:1985],
	_ObservationIDName[1985:1999],
}

// ObservationIDString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ObservationIDString(s string) (ObservationID, error) {
	if val, ok := _ObservationIDNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ObservationIDNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ObservationID values", s)
}

// ObservationIDValues returns all values of the enum
func ObservationIDValues() []ObservationID {
	return _ObservationIDValues
}

// ObservationIDStrings returns a slice of all String values of the enum
func ObservationIDStrings() []string {
	strs := make([]string, len(_ObservationIDNames))
	copy(strs, _ObservationIDNames)
	return strs
}

// IsAObservationID returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ObservationID) IsAObservationID() bool {
	_, ok := _ObservationIDMap[i]
	return ok
}
