package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateHeatpump(base *Heatpump, meter func() (float64, error), meterEnergy func() (float64, error), battery func() (float64, error), socLimiter func() (int64, error)) api.Charger {
	switch {
	case battery == nil && meter == nil:
		return base

	case battery == nil && meter != nil && meterEnergy == nil:
		return &struct {
			*Heatpump
			api.Meter
		}{
			Heatpump: base,
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && meter != nil && meterEnergy != nil:
		return &struct {
			*Heatpump
			api.Meter
			api.MeterEnergy
		}{
			Heatpump: base,
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateHeatpumpMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && meter == nil && socLimiter == nil:
		return &struct {
			*Heatpump
			api.Battery
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && meter != nil && meterEnergy == nil && socLimiter == nil:
		return &struct {
			*Heatpump
			api.Battery
			api.Meter
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && meter != nil && meterEnergy != nil && socLimiter == nil:
		return &struct {
			*Heatpump
			api.Battery
			api.Meter
			api.MeterEnergy
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateHeatpumpMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && meter == nil && socLimiter != nil:
		return &struct {
			*Heatpump
			api.Battery
			api.SocLimiter
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
			SocLimiter: &decorateHeatpumpSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && meter != nil && meterEnergy == nil && socLimiter != nil:
		return &struct {
			*Heatpump
			api.Battery
			api.Meter
			api.SocLimiter
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateHeatpumpSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && meter != nil && meterEnergy != nil && socLimiter != nil:
		return &struct {
			*Heatpump
			api.Battery
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			Heatpump: base,
			Battery: &decorateHeatpumpBatteryImpl{
				battery: battery,
			},
			Meter: &decorateHeatpumpMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateHeatpumpMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateHeatpumpSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}
	}

	return nil
}

type decorateHeatpumpBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateHeatpumpBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateHeatpumpMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateHeatpumpMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateHeatpumpMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateHeatpumpMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateHeatpumpSocLimiterImpl struct {
	socLimiter func() (int64, error)
}

func (impl *decorateHeatpumpSocLimiterImpl) GetLimitSoc() (int64, error) {
	return impl.socLimiter()
}
