package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateKebaUdp(base *KebaUdp, meter func() (float64, error), meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error)) api.Charger {
	switch {
	case meter == nil:
		return base

	case meter != nil && meterEnergy == nil && phaseCurrents == nil:
		return &struct {
			*KebaUdp
			api.Meter
		}{
			KebaUdp: base,
			Meter: &decorateKebaUdpMeterImpl{
				meter: meter,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents == nil:
		return &struct {
			*KebaUdp
			api.Meter
			api.MeterEnergy
		}{
			KebaUdp: base,
			Meter: &decorateKebaUdpMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaUdpMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case meter != nil && meterEnergy == nil && phaseCurrents != nil:
		return &struct {
			*KebaUdp
			api.Meter
			api.PhaseCurrents
		}{
			KebaUdp: base,
			Meter: &decorateKebaUdpMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateKebaUdpPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case meter != nil && meterEnergy != nil && phaseCurrents != nil:
		return &struct {
			*KebaUdp
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			KebaUdp: base,
			Meter: &decorateKebaUdpMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateKebaUdpMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateKebaUdpPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}
	}

	return nil
}

type decorateKebaUdpMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateKebaUdpMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateKebaUdpMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateKebaUdpMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateKebaUdpPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateKebaUdpPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}
