// Code generated by "enumer -type ExternalControl -trimprefix ExternalControl -transform whitespace"; DO NOT EDIT.

package warp

import (
	"fmt"
	"strings"
)

const _ExternalControlName = "availabledeactivatedruntime conditions not metcurrently switching"

var _ExternalControlIndex = [...]uint8{0, 9, 20, 46, 65}

const _ExternalControlLowerName = "availabledeactivatedruntime conditions not metcurrently switching"

func (i ExternalControl) String() string {
	if i < 0 || i >= ExternalControl(len(_ExternalControlIndex)-1) {
		return fmt.Sprintf("ExternalControl(%d)", i)
	}
	return _ExternalControlName[_ExternalControlIndex[i]:_ExternalControlIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ExternalControlNoOp() {
	var x [1]struct{}
	_ = x[ExternalControlAvailable-(0)]
	_ = x[ExternalControlDeactivated-(1)]
	_ = x[ExternalControlRuntimeConditionsNotMet-(2)]
	_ = x[ExternalControlCurrentlySwitching-(3)]
}

var _ExternalControlValues = []ExternalControl{ExternalControlAvailable, ExternalControlDeactivated, ExternalControlRuntimeConditionsNotMet, ExternalControlCurrentlySwitching}

var _ExternalControlNameToValueMap = map[string]ExternalControl{
	_ExternalControlName[0:9]:        ExternalControlAvailable,
	_ExternalControlLowerName[0:9]:   ExternalControlAvailable,
	_ExternalControlName[9:20]:       ExternalControlDeactivated,
	_ExternalControlLowerName[9:20]:  ExternalControlDeactivated,
	_ExternalControlName[20:46]:      ExternalControlRuntimeConditionsNotMet,
	_ExternalControlLowerName[20:46]: ExternalControlRuntimeConditionsNotMet,
	_ExternalControlName[46:65]:      ExternalControlCurrentlySwitching,
	_ExternalControlLowerName[46:65]: ExternalControlCurrentlySwitching,
}

var _ExternalControlNames = []string{
	_ExternalControlName[0:9],
	_ExternalControlName[9:20],
	_ExternalControlName[20:46],
	_ExternalControlName[46:65],
}

// ExternalControlString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ExternalControlString(s string) (ExternalControl, error) {
	if val, ok := _ExternalControlNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ExternalControlNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ExternalControl values", s)
}

// ExternalControlValues returns all values of the enum
func ExternalControlValues() []ExternalControl {
	return _ExternalControlValues
}

// ExternalControlStrings returns a slice of all String values of the enum
func ExternalControlStrings() []string {
	strs := make([]string, len(_ExternalControlNames))
	copy(strs, _ExternalControlNames)
	return strs
}

// IsAExternalControl returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ExternalControl) IsAExternalControl() bool {
	for _, v := range _ExternalControlValues {
		if i == v {
			return true
		}
	}
	return false
}
