package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateSwitchSocket(base *SwitchSocket, meterEnergy func() (float64, error), battery func() (float64, error)) api.Charger {
	switch {
	case battery == nil && meterEnergy == nil:
		return base

	case battery == nil && meterEnergy != nil:
		return &struct {
			*SwitchSocket
			api.MeterEnergy
		}{
			SwitchSocket: base,
			MeterEnergy: &decorateSwitchSocketMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && meterEnergy == nil:
		return &struct {
			*SwitchSocket
			api.Battery
		}{
			SwitchSocket: base,
			Battery: &decorateSwitchSocketBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && meterEnergy != nil:
		return &struct {
			*SwitchSocket
			api.Battery
			api.MeterEnergy
		}{
			SwitchSocket: base,
			Battery: &decorateSwitchSocketBatteryImpl{
				battery: battery,
			},
			MeterEnergy: &decorateSwitchSocketMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}
	}

	return nil
}

type decorateSwitchSocketBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateSwitchSocketBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateSwitchSocketMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateSwitchSocketMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}
