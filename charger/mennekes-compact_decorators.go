package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateMennekesCompact(base *MennekesCompact, phaseSwitcher func(int) error) api.Charger {
	switch {
	case phaseSwitcher == nil:
		return base

	case phaseSwitcher != nil:
		return &struct {
			*MennekesCompact
			api.PhaseSwitcher
		}{
			MennekesCompact: base,
			PhaseSwitcher: &decorateMennekesCompactPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}
	}

	return nil
}

type decorateMennekesCompactPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateMennekesCompactPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}
