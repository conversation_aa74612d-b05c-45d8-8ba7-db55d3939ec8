package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateBenderCC(base *BenderCC, meter func() (float64, error), phaseCurrents func() (float64, float64, float64, error), phaseVoltages func() (float64, float64, float64, error), meterEnergy func() (float64, error), battery func() (float64, error), identifier func() (string, error), chargerEx func(float64) error, phaseSwitcher func(int) error, phaseGetter func() (int, error)) api.Charger {
	switch {
	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil:
		return base

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.Battery
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.Identifier
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.PhaseSwitcher
		}{
			BenderCC: base,
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseGetter == nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter == nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseGetter != nil && phaseSwitcher != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages == nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseGetter != nil && phaseSwitcher != nil && phaseVoltages != nil:
		return &struct {
			*BenderCC
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseGetter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			BenderCC: base,
			Battery: &decorateBenderCCBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateBenderCCChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateBenderCCIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateBenderCCMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateBenderCCMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateBenderCCPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseGetter: &decorateBenderCCPhaseGetterImpl{
				phaseGetter: phaseGetter,
			},
			PhaseSwitcher: &decorateBenderCCPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateBenderCCPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}
	}

	return nil
}

type decorateBenderCCBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateBenderCCBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateBenderCCChargerExImpl struct {
	chargerEx func(float64) error
}

func (impl *decorateBenderCCChargerExImpl) MaxCurrentMillis(p0 float64) error {
	return impl.chargerEx(p0)
}

type decorateBenderCCIdentifierImpl struct {
	identifier func() (string, error)
}

func (impl *decorateBenderCCIdentifierImpl) Identify() (string, error) {
	return impl.identifier()
}

type decorateBenderCCMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateBenderCCMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateBenderCCMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateBenderCCMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateBenderCCPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateBenderCCPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}

type decorateBenderCCPhaseGetterImpl struct {
	phaseGetter func() (int, error)
}

func (impl *decorateBenderCCPhaseGetterImpl) GetPhases() (int, error) {
	return impl.phaseGetter()
}

type decorateBenderCCPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateBenderCCPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}

type decorateBenderCCPhaseVoltagesImpl struct {
	phaseVoltages func() (float64, float64, float64, error)
}

func (impl *decorateBenderCCPhaseVoltagesImpl) Voltages() (float64, float64, float64, error) {
	return impl.phaseVoltages()
}
