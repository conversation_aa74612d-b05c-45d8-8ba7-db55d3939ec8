package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateCustom(base *Charger, chargerEx func(float64) error, identifier func() (string, error), phaseSwitcher func(int) error, resurrector func() error, battery func() (float64, error), socLimiter func() (int64, error), meter func() (float64, error), meterEnergy func() (float64, error), phaseCurrents func() (float64, float64, float64, error), phaseVoltages func() (float64, float64, float64, error)) api.Charger {
	switch {
	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil:
		return base

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil:
		return &struct {
			*Charger
			api.PhaseSwitcher
		}{
			Charger: base,
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.PhaseSwitcher
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Resurrector
		}{
			Charger: base,
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil:
		return &struct {
			*Charger
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter == nil && phaseSwitcher != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages == nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents == nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy == nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery == nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil:
		return &struct {
			*Charger
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter == nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector == nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher == nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier == nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx == nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}

	case battery != nil && chargerEx != nil && identifier != nil && meter != nil && meterEnergy != nil && phaseCurrents != nil && phaseSwitcher != nil && phaseVoltages != nil && resurrector != nil && socLimiter != nil:
		return &struct {
			*Charger
			api.Battery
			api.ChargerEx
			api.Identifier
			api.Meter
			api.MeterEnergy
			api.PhaseCurrents
			api.PhaseSwitcher
			api.PhaseVoltages
			api.Resurrector
			api.SocLimiter
		}{
			Charger: base,
			Battery: &decorateCustomBatteryImpl{
				battery: battery,
			},
			ChargerEx: &decorateCustomChargerExImpl{
				chargerEx: chargerEx,
			},
			Identifier: &decorateCustomIdentifierImpl{
				identifier: identifier,
			},
			Meter: &decorateCustomMeterImpl{
				meter: meter,
			},
			MeterEnergy: &decorateCustomMeterEnergyImpl{
				meterEnergy: meterEnergy,
			},
			PhaseCurrents: &decorateCustomPhaseCurrentsImpl{
				phaseCurrents: phaseCurrents,
			},
			PhaseSwitcher: &decorateCustomPhaseSwitcherImpl{
				phaseSwitcher: phaseSwitcher,
			},
			PhaseVoltages: &decorateCustomPhaseVoltagesImpl{
				phaseVoltages: phaseVoltages,
			},
			Resurrector: &decorateCustomResurrectorImpl{
				resurrector: resurrector,
			},
			SocLimiter: &decorateCustomSocLimiterImpl{
				socLimiter: socLimiter,
			},
		}
	}

	return nil
}

type decorateCustomBatteryImpl struct {
	battery func() (float64, error)
}

func (impl *decorateCustomBatteryImpl) Soc() (float64, error) {
	return impl.battery()
}

type decorateCustomChargerExImpl struct {
	chargerEx func(float64) error
}

func (impl *decorateCustomChargerExImpl) MaxCurrentMillis(p0 float64) error {
	return impl.chargerEx(p0)
}

type decorateCustomIdentifierImpl struct {
	identifier func() (string, error)
}

func (impl *decorateCustomIdentifierImpl) Identify() (string, error) {
	return impl.identifier()
}

type decorateCustomMeterImpl struct {
	meter func() (float64, error)
}

func (impl *decorateCustomMeterImpl) CurrentPower() (float64, error) {
	return impl.meter()
}

type decorateCustomMeterEnergyImpl struct {
	meterEnergy func() (float64, error)
}

func (impl *decorateCustomMeterEnergyImpl) TotalEnergy() (float64, error) {
	return impl.meterEnergy()
}

type decorateCustomPhaseCurrentsImpl struct {
	phaseCurrents func() (float64, float64, float64, error)
}

func (impl *decorateCustomPhaseCurrentsImpl) Currents() (float64, float64, float64, error) {
	return impl.phaseCurrents()
}

type decorateCustomPhaseSwitcherImpl struct {
	phaseSwitcher func(int) error
}

func (impl *decorateCustomPhaseSwitcherImpl) Phases1p3p(p0 int) error {
	return impl.phaseSwitcher(p0)
}

type decorateCustomPhaseVoltagesImpl struct {
	phaseVoltages func() (float64, float64, float64, error)
}

func (impl *decorateCustomPhaseVoltagesImpl) Voltages() (float64, float64, float64, error) {
	return impl.phaseVoltages()
}

type decorateCustomResurrectorImpl struct {
	resurrector func() error
}

func (impl *decorateCustomResurrectorImpl) WakeUp() error {
	return impl.resurrector()
}

type decorateCustomSocLimiterImpl struct {
	socLimiter func() (int64, error)
}

func (impl *decorateCustomSocLimiterImpl) GetLimitSoc() (int64, error) {
	return impl.socLimiter()
}
