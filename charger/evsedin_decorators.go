package charger

// Code generated by github.com/evcc-io/evcc/cmd/tools/decorate.go. DO NOT EDIT.

import (
	"github.com/evcc-io/evcc/api"
)

func decorateEvseDIN(base *EvseDIN, chargerEx func(float64) error) api.Charger {
	switch {
	case chargerEx == nil:
		return base

	case chargerEx != nil:
		return &struct {
			*EvseDIN
			api.ChargerEx
		}{
			EvseDIN: base,
			ChargerEx: &decorateEvseDINChargerExImpl{
				chargerEx: chargerEx,
			},
		}
	}

	return nil
}

type decorateEvseDINChargerExImpl struct {
	chargerEx func(float64) error
}

func (impl *decorateEvseDINChargerExImpl) MaxCurrentMillis(p0 float64) error {
	return impl.chargerEx(p0)
}
