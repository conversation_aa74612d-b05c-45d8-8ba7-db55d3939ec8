```mermaid
graph TD
    A[系统启动] --> B[读取电池SOC]
    B --> C{电池SOC < 优先SOC?}
    
    C -->|是| D[电池优先充电模式]
    D --> E[禁止车辆充电]
    D --> F[电池获得所有可用功率]
    
    C -->|否| G{电池SOC > 缓冲SOC?}
    
    G -->|否| H[仅光伏充电模式]
    H --> I[不使用电池为车辆充电]
    H --> J[仅使用光伏剩余功率]
    
    G -->|是| K{电池SOC > 启动SOC?}
    
    K -->|否| L[可选电池辅助模式]
    L --> M[PV模式下可使用电池]
    L --> N[手动启动电池辅助]
    
    K -->|是| O[自动电池辅助模式]
    O --> P[系统自动启动电池辅助充电]
    O --> Q[最大化车辆充电功率]
    
    subgraph "电池SOC区域划分"
        R[0% - 优先SOC: 电池优先]
        S[优先SOC - 缓冲SOC: 仅光伏]
        T[缓冲SOC - 启动SOC: 可选辅助]
        U[启动SOC - 100%: 自动辅助]
    end
    
    subgraph "功率计算逻辑"
        V[更新所有电表数据] --> W[计算站点可用功率]
        W --> X[考虑电池优先级]
        X --> Y[传递给充电点执行策略]
    end
    
    E --> V
    J --> V
    N --> V
    Q --> V
```

这个流程图展示了：

1. **电池SOC检查流程**： 中实现的电池优先级逻辑
2. **三个关键阈值**： 中定义的 prioritySoc、bufferSoc 和 bufferStartSoc
    > prioritySoc: 当电池的充电状态低于这个设定值时，系统会优先给家用电池充电
    > bufferSoc: 是允许使用家用电池为电动车充电的最低电池电量阈值，允许电池给ev充电
    > bufferStartSoc: 是自动开始电池辅助充电的电池电量阈值。当家用电池的 SOC 高于这个值时，系统会自动启动电池辅助的车辆充电
3. **四个电池使用区域**：从电池优先到自动辅助的完整策略
4. **功率计算和策略执行**： 中的功率数据收集和处理流程

这个流程确保了家用电池的智能管理，既保护电池不过度放电，又最大化了可再生能源的利用效率。
